<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="minimum-scale=1, initial-scale=1, width=device-width">
    <meta name="viewport" content="width=device-width">
    <meta name="robots" content="noindex">
    <title th:text="#{pages.confirmation.title}"></title>
    <link rel="icon" th:href="@{/favicon.ico}" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500">
    <link rel="stylesheet" media="screen" th:href="@{/css/confirm.css}" />
</head>
<body>
<div class="main">
    <header class="toolbar" th:unless="${title == null}">
        <div class="toolbar-root">
            <h1 th:text="${title}" />
        </div>
    </header>
    <div class="content" th:if="${success}">
        <div th:if="${successReason == 'confirmed'}">
            <h2 th:text="#{pages.confirmation.confirmed.heading}"></h2>
            <form action="#" th:action="${#request.requestURI + '?' + #request.queryString}" method="post">
                <button name="result" value="declined" th:text="#{pages.confirmation.decline.button}"></button>
            </form>
        </div>
        <div th:unless="${successReason == 'confirmed'}">
            <h2 th:text="#{pages.confirmation.declined.heading}"></h2>
            <p th:text="#{pages.confirmation.declined.body}"></p>
        </div>
    </div>
    <div class="content" th:unless="${success}">
        <div>
            <h2 th:text="#{pages.confirmation.problem.heading}"></h2>
            <p th:text="#{pages.confirmation.problem.body}"></p>
        </div>
    </div>
    <footer class="footer"></footer>
</div>
</body>
</html>