<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="minimum-scale=1, initial-scale=1, width=device-width">
    <meta name="viewport" content="width=device-width">
    <meta name="robots" content="noindex">
    <title th:text="#{pages.confirmation.title}"></title>
    <link rel="icon" th:href="@{/favicon.ico}" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500">
    <link rel="stylesheet" media="screen" th:href="@{/css/confirm.css}" />
</head>
<body>
  <div class="main">
    <header class="toolbar" th:unless="${title == null}">
        <div class="toolbar-root">
            <h1 th:text="${title}" />
        </div>
    </header>
    <div class="content">
        <form action="#" th:action="${#request.requestURI + '?' + #request.queryString}" method="post">
            <button name="result" value="confirmed" th:text="#{pages.confirmation.confirm.button}"></button>
            <button name="result" value="declined" th:text="#{pages.confirmation.decline.button}"></button>
        </form>
    </div>
    <footer class="footer"></footer>
  </div>
</body>
</html>