package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto

/**
 * A contract for services that handle engagement responses.
 */
interface EngagementResponseHandler {
    /**
     * Records an error response.
     * @param engagement The engagement the error response is for.
     * @param errors The error details to include in the response.
     * @return The resulting response.
     */
    fun recordError(engagement: Engagement, vararg errors: ErrorDto): ErrorResponse

    /**
     * Records a response to a confirmation request.
     * @param engagement The engagement the error response is for.
     * @param result Whether the contact(s) confirmed or declined the appointment.
     * @param respondents Which contacts responded.
     * @param isFinal Whether the engagement process will stop (e.g. the contact declined, so we want to send no further messages about this appointment).
     * @param messageId The ID of the message where a contact responded.
     * @return The resulting response.
     */
    fun recordConfirmation(
        engagement: Engagement,
        result: ConfirmationStatus,
        respondents: Set<ContactResource>,
        isFinal: Boolean,
        messageId: String?
    ): ConfirmationResponse

    /**
     * Records a response that the engagement process has completed.
     * @param engagement The engagement that has completed.
     * @return The resulting response.
     */
    fun recordComplete(
        engagement: Engagement
    ): CompleteResponse

    fun recordCancellation(
        engagement: Engagement
    ): CancellationResponse

    fun recordContactUnreachable(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        contactErrors: EngagementMessageResult.ContactErrors,
    ): MessageResponse

    fun recordMessageResponse(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        contact: ContactResource?,
        status: MessageStatus,
        messageId: String? = null,
        errors: List<ErrorDto>? = null,
        message: String?,
        altMessage: String?,
        subject: String?
    ): MessageResponse

    fun recordCheckIn(
        engagement: Engagement,
        respondents: Set<ContactResource>,
        appointments: Set<AppointmentResource>,
        result: CheckInStatus
    ): CheckInResponse
}