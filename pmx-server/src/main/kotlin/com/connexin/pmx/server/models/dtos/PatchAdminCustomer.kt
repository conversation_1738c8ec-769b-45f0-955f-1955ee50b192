package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.DeliveryDay
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Duration
import java.time.LocalTime

@Schema(
    description = "Customer settings that can be patched by an admin."
)
data class PatchAdminCustomer(
    val status: CustomerStatus,
    val name: String,
    val legacyUsername: String?,
    val legacyPassword: String?,
    val appointmentTimeDisplayOffset: Duration?,
    val cancellationDeadline: Duration?,
    val deliveryDays: MutableSet<DeliveryDay>,
    val deliveryStartTime: LocalTime,
    val deliveryEndTime: LocalTime,
    val telnyxConfig: Customer.TelnyxConfig
) : PatchableCustomer {
    companion object {
        fun from(customer: Customer): PatchAdminCustomer {
            return PatchAdminCustomer(
                status = customer.status,
                name = customer.name,
                legacyUsername = customer.legacyUsername,
                legacyPassword = customer.legacyPassword,
                appointmentTimeDisplayOffset = customer.appointmentTimeDisplayOffset,
                cancellationDeadline = customer.cancellationDeadline,
                deliveryDays = customer.deliveryDays,
                deliveryStartTime = customer.deliveryStartTime,
                deliveryEndTime = customer.deliveryEndTime,
                telnyxConfig = customer.telnyxConfig
            )
        }
    }
}
