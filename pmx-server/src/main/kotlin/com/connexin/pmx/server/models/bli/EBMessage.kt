package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Orders")
data class EBMessages(   // email messages (broadcast)

    @JacksonXmlProperty(localName = "Order")
    @JacksonXmlElementWrapper(useWrapping = false)
    var order: EBMessage

)

@JsonRootName("Order")
data class EBMessage(

    @JacksonXmlProperty(isAttribute = true, localName = "Type")
    var type: String?,

    @JacksonXmlProperty(localName = "Project")
    var project: String?,

    @JacksonXmlProperty(localName = "BillCode")
    var billCode: String?,

    @JacksonXmlProperty(localName = "Date")
    var date: String?,

    @JacksonXmlProperty(localName = "Time")
    var time: String?,

    @JacksonXmlProperty(localName = "DisplayName")
    var displayName: String?,

    @JacksonXmlProperty(localName = "From")
    var from: String?,

    @JacksonXmlProperty(localName = "Subject")
    var subject: String?,

    @JacksonXmlProperty(localName = "ReplyTo")
    var replyTo: String?,

    @JacksonXmlProperty(localName = "Forward")
    var forward: String?,

    @JacksonXmlProperty(localName = "ReplaceLink")
    var replaceLink: String?,

    @JacksonXmlProperty(localName = "Unsubscribe")
    var unsubscribe: String?,

    @JacksonXmlProperty(localName = "NumberOfRedials")
    var numberOfRedials: String?,

    @JacksonXmlProperty(localName = "NumberOfResends")
    var numberOfResends: String?,

    @JacksonXmlProperty(localName = "ResendInterval")
    var resendInterval: String?,

    @JacksonXmlProperty(localName = "ListID")
    var listID: String?,

    @JacksonXmlProperty(localName = "ListName")
    var listName: String?,

    @JacksonXmlProperty(localName = "ListBinary")
    var listBinary: String?,

    @JacksonXmlProperty(localName = "EmailField")
    var emailField: String?,

    @JacksonXmlProperty(localName = "HtmlFile")
    var htmlFile: String?,

    @JacksonXmlProperty(localName = "HtmlID")
    var htmlID: String?,

    @JacksonXmlProperty(localName = "HTMLBinary")
    var htmlBinary: String?,

    @JacksonXmlProperty(localName = "TextFile")
    var textFile: String?,

    @JacksonXmlProperty(localName = "TextID")
    var textID: String?,

    @JacksonXmlProperty(localName = "TextBinary")
    var textBinary: String?,

    @JacksonXmlProperty(localName = "RtfFile")
    var rtfFile: String?,

    @JacksonXmlProperty(localName = "RtfID")
    var rtfID: String?,

    @JacksonXmlProperty(localName = "EnrichedFile")
    var enrichedFile: String?,

    @JacksonXmlProperty(localName = "EnrichedID")
    var enrichedID: String?,

    @JacksonXmlProperty(localName = "XmlFile")
    var xmlFile: String?,

    @JacksonXmlProperty(localName = "XmlID")
    var xmlID: String?,

    @JacksonXmlProperty(localName = "Attachments")
    var ebAttachments: List<EBAttachment> = ArrayList(),

    @JacksonXmlProperty(localName = "Proofs")
    var ebProofs: List<EBProof> = ArrayList(),

    @JacksonXmlProperty(localName = "AutoLaunch")
    var autoLaunch: String?
)

@JsonRootName("Attachment")
data class EBAttachment(

    @JacksonXmlProperty(localName = "AttachmentID")
    var attachmentID: String?,

    @JacksonXmlProperty(localName = "AttachmentName")
    var attachmentName: String?,

    @JacksonXmlProperty(localName = "AttachmentBinary")
    var attachmentBinary: String?

)

@JsonRootName("Proof")
data class EBProof(

    @JacksonXmlProperty(localName = "Proof")
    var proof: String?

)