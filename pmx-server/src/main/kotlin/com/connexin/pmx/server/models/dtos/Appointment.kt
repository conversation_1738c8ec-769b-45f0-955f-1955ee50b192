package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.CheckInStatus
import com.connexin.pmx.server.models.ConfirmationStatus
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotNull
import org.valiktor.functions.validate
import java.time.Instant
import java.time.LocalDateTime

@Schema(
    description = "A scheduled appointment for a patient."
)
data class Appointment(
    @field:Schema(
        description = "The appointment's ID in the customer system."
    )
    val id: String,
    @field:Schema(
        description = "The appointment type ID in the customer system."
    )
    val appointmentTypeId: String,
    @field:Schema(
        description = "The scheduled start date/time of the appointment represented as an instant. Must be provided if localStartTime is null."
    )
    val startTime: Instant? = null,
    @field:Schema(
        description = "The scheduled start date/time of the appointment represented as a date-time local to the appointment location. Must be provided if startTime is null.",
        type = "string",
        pattern = "\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d",
        example = "2023-07-13T08:30",
        required = false
    )
    val localStartTime: LocalDateTime? = null,
    @field:Schema(
        description = "The patient for which this appointment was created."
    )
    val patient: Patient,
    @field:Schema(
        description = "The practice where the appointment will occur."
    )
    val practice: Practice? = null,
    @field:Schema(
        description = "The practice location where the appointment will occur."
    )
    val location: Location,
    @field:Schema(
        description = "The provider the appointment is scheduled with."
    )
    val staff: Staff? = null,
    @field:Schema(
        description = "Whether the appointment has been cancelled."
    )
    val cancelled: Boolean = false,
    @field:Schema(
        description = "The reason for the appointment."
    )
    val reason: String,
    @field:Schema(
        description = "The confirmation status of the appointment."
    )
    val confirmationStatus: ConfirmationStatus = ConfirmationStatus.UNCONFIRMED,
    @field:Schema(
        description = "The check-in status of the appointment"
    )
    val checkInStatus: CheckInStatus = CheckInStatus.NOT_CHECKED_IN
) {
    fun validate() {
        org.valiktor.validate(this) {
            // must provide one of startTime or localStartTime
            if (startTime == null) {
                validate(Appointment::localStartTime).isNotNull()
            }
            if (localStartTime == null) {
                validate(Appointment::startTime).isNotNull()
            }

            validate(Appointment::location).validate {
                this.validate()
            }
        }
    }
}