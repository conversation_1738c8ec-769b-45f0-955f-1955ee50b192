package com.connexin.pmx.server.config

import com.mongodb.client.MongoClient
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.mongo.MongoLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableScheduling

@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT5M")
@ConditionalOnProperty(value = ["op.pmx.scheduling.enabled"], havingValue = "true", matchIfMissing = true)
class SchedulingConfig {
    @Bean
    fun lockProvider(mongo: MongoClient): LockProvider {
        return MongoLockProvider(mongo.getDatabase("pmx"))
    }
}