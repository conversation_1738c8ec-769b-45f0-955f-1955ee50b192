package com.connexin.pmx.server.services.impl

import com.amazonaws.services.simpleemailv2.AmazonSimpleEmailServiceV2
import com.amazonaws.services.simpleemailv2.model.*
import com.connexin.pmx.server.models.BulkEmailMessage
import com.connexin.pmx.server.models.EmailMessage
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.services.SendBulkEmailResult
import com.connexin.pmx.server.services.SendEmailResult
import com.connexin.pmx.server.utils.CacheKeys
import io.github.resilience4j.ratelimiter.RateLimiter
import io.github.resilience4j.ratelimiter.RateLimiterConfig
import io.github.resilience4j.ratelimiter.RequestNotPermitted
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import java.time.Duration
import java.util.concurrent.Callable

@Service
class SesEmailServiceImpl(
    private val ses: AmazonSimpleEmailServiceV2,
    @Value("\${op.pmx.default-from-email-address}") private val defaultFromEmailAddress: String,
    @Qualifier("sesConfigSetName") private val configSetName: String?,
    private val rateLimiters: RateLimiters
) : EmailService, RateLimitedService {

    companion object {
        private val log = LoggerFactory.getLogger(SesEmailServiceImpl::class.java)
        private val retryStatuses = setOf(
            BulkEmailStatus.ACCOUNT_THROTTLED,
            BulkEmailStatus.ACCOUNT_DAILY_QUOTA_EXCEEDED
        )
        private val throttleRetryDelay = Duration.ofSeconds(1)
        private val quotaRetryDelay = Duration.ofHours(1)
    }

    override fun sendEmail(email: EmailMessage, tags: Map<String, String>?): SendEmailResult {
        val request = SendEmailRequest()
            .withFromEmailAddress(email.fromAddress ?: defaultFromEmailAddress)
            .withDestination(
                Destination()
                    .withToAddresses(email.toAddresses)
                    .withCcAddresses(email.ccAddresses)
                    .withBccAddresses(email.bccAddresses)
            )
            .withContent(
                EmailContent().withSimple(
                    Message()
                        .withSubject(Content().withData(email.subject))
                        .withBody(
                            if (email.isHtml) {
                                Body().withHtml(Content().withData(email.body))
                            } else {
                                Body().withText(Content().withData(email.body))
                            }
                        )
                )
            )
        if (email.replyToAddress != null) {
            request.setReplyToAddresses(setOf(email.replyToAddress))
        }
        if (configSetName != null) {
            request.configurationSetName = configSetName
        }
        if (tags != null) {
            request.setEmailTags(tags.map { MessageTag().withName(it.key).withValue(it.value) })
        }

        val rateLimitedSendEmail = applyRateLimits {
            ses.sendEmail(request)
        }

        return try {
            val result = rateLimitedSendEmail.call()
            SendEmailResult(success = true, messageId = result.messageId)
        } catch (ex: Exception) {
            log.error("Failed to send email", ex)
            val retryDelay = getRetryDelayForException(ex)
            val retry = retryDelay != null
            SendEmailResult(
                success = false,
                retry = retry,
                retryDelay = retryDelay,
                error = when {
                    retry -> null
                    ex is AmazonSimpleEmailServiceV2Exception -> ex.errorMessage
                    else -> ex.message
                }
            )
        }
    }

    override fun sendBulkEmail(
        email: BulkEmailMessage,
        tags: Map<String, String>?,
        onBatchSent: ((List<SendBulkEmailResult.EntryResult>) -> Unit)?
    ): SendBulkEmailResult {
        if (email.entries.isEmpty()) {
            return SendBulkEmailResult(success = false, error = "No email recipients")
        }

        val template = email.template
        var templateCreated = false
        if (template.isTransient) {
            try {
                createEmailTemplate(template.name, template.subject, template.htmlContent, template.textContent)
                log.info("Created email template: {}", template.name)
                templateCreated = true
            } catch (ex: AmazonSimpleEmailServiceV2Exception) {
                return SendBulkEmailResult(
                    success = false,
                    error = "Failed to create email template"
                )
            }
        }

        log.info("Preparing to send bulk email to {} recipients", email.entries.size)

        val baseRequest = buildBulkEmailRequest(email, tags)

        // SES allows a maximum of 50 recipients per call,
        // but we also need to consider our sending rate limit, which is likely less than 50 emails/second;
        // we'll use the lesser of the two for our batch size
        val batches = email.entries.chunked(minOf(getRateLimits().maxPerSecond, 50))
        val entryResults = try {
            runBlocking {
                val deferredBatchResults = batches.mapIndexed { index, batch ->
                    async(start = CoroutineStart.LAZY) {
                        val batchNumber = index + 1
                        val batchRequest = buildRequestForBatch(baseRequest, batch)
                        // wrap service call in rate-limiter
                        val rateLimitedSend = applyRateLimits {
                            ses.sendBulkEmail(batchRequest)
                        }
                        log.debug("Sending bulk email batch {} of {}", batchNumber, batches.size)
                        try {
                            // make the service call
                            val batchResult = rateLimitedSend.call()

                            log.debug("batchResult={}", batchResult)

                            batchResult.bulkEmailEntryResults.mapIndexed { entryIndex, entryResult ->
                                val status = BulkEmailStatus.fromValue(entryResult.status)
                                SendBulkEmailResult.EntryResult(
                                    success = status == BulkEmailStatus.SUCCESS,
                                    toAddress = batch[entryIndex].toAddress,
                                    error = entryResult.error,
                                    messageId = entryResult.messageId,
                                    retry = retryStatuses.contains(status),
                                    retryDelay = getRetryDelayForStatus(status)
                                )
                            }
                        } catch (ex: RequestNotPermitted) {
                            log.error("Failed to send bulk email batch {} of {}", batchNumber, batches.size, ex)
                            batch.map {
                                SendBulkEmailResult.EntryResult(
                                    success = false,
                                    toAddress = it.toAddress,
                                    retry = true,
                                    retryDelay = getRetryDelayForException(ex)
                                )
                            }
                        } catch (ex: AmazonSimpleEmailServiceV2Exception) {
                            log.error("Failed to send bulk email batch {} of {}", batchNumber, batches.size, ex)
                            batch.map {
                                SendBulkEmailResult.EntryResult(
                                    success = false,
                                    toAddress = it.toAddress,
                                    error = ex.errorMessage
                                )
                            }
                        }
                    }
                }

                deferredBatchResults.flatMap {
                    // delay batch execution by 1 sec to remain within rate limit
                    delay(1000)

                    // since we used lazy async it must be started
                    it.start()
                    val batchEntryResults = it.await()

                    if (onBatchSent != null) onBatchSent(batchEntryResults)

                    batchEntryResults
                }
            }
        } finally {
            if (templateCreated) {
                deleteEmailTemplate(template.name)
            }
        }

        return SendBulkEmailResult(
            success = entryResults.all { it.success },
            entryResults = entryResults
        )
    }

    @Cacheable(CacheKeys.sesSendQuota)
    override fun getRateLimits(): RateLimits {
        val quota = ses.getAccount(GetAccountRequest()).sendQuota

        log.info("Current SES sending limits: {}", quota)

        return RateLimits(
            maxPerDay = quota.max24HourSend.toInt(),
            maxPerSecond = quota.maxSendRate.toInt()
        )
    }

    private fun getRetryDelayForException(ex: Exception) = when (ex) {
        is TooManyRequestsException -> throttleRetryDelay
        is LimitExceededException -> quotaRetryDelay
        is RequestNotPermitted -> if (ex.message?.contains(RateLimiterConstants.EMAILS_PER_DAY) == true) {
            quotaRetryDelay
        } else {
            throttleRetryDelay
        }

        else -> null
    }

    private fun getRetryDelayForStatus(status: BulkEmailStatus) = when (status) {
        BulkEmailStatus.ACCOUNT_THROTTLED -> throttleRetryDelay
        BulkEmailStatus.ACCOUNT_DAILY_QUOTA_EXCEEDED -> quotaRetryDelay
        else -> null
    }

    private fun buildBulkEmailRequest(email: BulkEmailMessage, tags: Map<String, String>?): SendBulkEmailRequest {
        val template = email.template
        val request = SendBulkEmailRequest()
            .withFromEmailAddress(email.fromAddress ?: defaultFromEmailAddress)
            .withDefaultContent(
                BulkEmailContent()
                    .withTemplate(
                        Template()
                            .withTemplateName(template.name)
                            .withTemplateData(template.defaultReplacementValues.toString())
                    )
            )
        if (email.replyToAddress != null) {
            request.setReplyToAddresses(setOf(email.replyToAddress))
        }
        if (configSetName != null) {
            request.configurationSetName = configSetName
        }
        if (tags != null) {
            request.setDefaultEmailTags(tags.map { MessageTag().withName(it.key).withValue(it.value) })
        }

        return request
    }

    private fun buildRequestForBatch(
        request: SendBulkEmailRequest,
        batch: List<BulkEmailMessage.Entry>
    ): SendBulkEmailRequest {
        return request.clone()
            .withBulkEmailEntries(
                batch.map {
                    BulkEmailEntry()
                        .withDestination(Destination().withToAddresses(it.toAddress))
                        .withReplacementEmailContent(
                            ReplacementEmailContent()
                                .withReplacementTemplate(
                                    ReplacementTemplate()
                                        .withReplacementTemplateData(
                                            it.replacementValues
                                                .put("UnsubscribeLink", it.unsubscribeLink)
                                                .toString()
                                        )
                                )
                        )
                }
            )
    }

    private fun createEmailTemplate(
        templateName: String,
        subject: String? = null,
        html: String? = null,
        text: String? = null,
        includeUnsubscribeFooter: Boolean = true
    ): CreateEmailTemplateResult? {
        val content = EmailTemplateContent()
        if (subject != null) {
            content.subject = subject
        }
        if (text != null) {
            content.text = text
        }
        if (html != null) {
            content.html = html
            if (includeUnsubscribeFooter) {
                content.html += "<p>If you would prefer not to receive further messages from this sender, " +
                        "please <a href=\"{{UnsubscribeLink}}\">click here</a> and confirm your request.</p>"
            }
        }

        log.info("Creating email template: {}", templateName)

        return ses.createEmailTemplate(
            CreateEmailTemplateRequest()
                .withTemplateName(templateName)
                .withTemplateContent(content)
        )
    }

    private fun deleteEmailTemplate(templateName: String): Boolean {
        log.info("Deleting email template: {}", templateName)

        return try {
            ses.deleteEmailTemplate(
                DeleteEmailTemplateRequest()
                    .withTemplateName(templateName)
            )
            log.info("Deleted email template: {}", templateName)
            true
        } catch (ex: AmazonSimpleEmailServiceV2Exception) {
            log.error("Failed to delete email template: {}", templateName, ex)
            false
        }
    }

    private fun <T> applyRateLimits(serviceMethod: () -> T): Callable<T> {
        val rateLimits = getRateLimits()

        val emailsPerDay = rateLimiters.getByName(
            RateLimiterConstants.EMAILS_PER_DAY, RateLimiterConfig.custom()
                .limitForPeriod(rateLimits.maxPerDay)
                .limitRefreshPeriod(Duration.ofDays(1))
                .timeoutDuration(Duration.ofMillis(100))
                .build()
        )
        if (rateLimits.maxPerDay != emailsPerDay.rateLimiterConfig.limitForPeriod) {
            log.info(
                "[old={}, new={}] Applying change to per day limit",
                emailsPerDay.rateLimiterConfig.limitForPeriod,
                rateLimits.maxPerDay
            )
            emailsPerDay.changeLimitForPeriod(rateLimits.maxPerDay)
        }

        val emailsPerSecond = rateLimiters.getByName(
            RateLimiterConstants.EMAILS_PER_SECOND, RateLimiterConfig.custom()
                .limitForPeriod(rateLimits.maxPerSecond)
                .limitRefreshPeriod(Duration.ofSeconds(1))
                .timeoutDuration(Duration.ofMillis(100))
                .build()
        )
        if (rateLimits.maxPerSecond != emailsPerSecond.rateLimiterConfig.limitForPeriod) {
            log.info(
                "[old={}, new={}] Applying change to per second limit",
                emailsPerSecond.rateLimiterConfig.limitForPeriod,
                rateLimits.maxPerSecond
            )
            emailsPerSecond.changeLimitForPeriod(rateLimits.maxPerSecond)
        }

        return RateLimiter.decorateCallable(emailsPerDay, RateLimiter.decorateCallable(emailsPerSecond) {
            serviceMethod.invoke()
        })
    }
}
