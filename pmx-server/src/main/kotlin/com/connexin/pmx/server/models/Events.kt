package com.connexin.pmx.server.models

import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.statemachine.Event

data class TransitionEvent(
    override val event: EngagementEvent
) : Event<EngagementEvent>

abstract class EventMessage(
    override val event: EngagementEvent,
    val engagement: Engagement,
    val customer: Customer
) : Event<EngagementEvent>

class CheckpointEvent(
    engagement: Engagement,
    customer: Customer
) : EventMessage(event = EngagementEvent.CHECKPOINT, engagement = engagement, customer = customer)

abstract class ResponseEvent(
    event: EngagementEvent,
    engagement: Engagement,
    customer: Customer
) : EventMessage(event = event, engagement = engagement, customer = customer)

class ConfirmationResponseEvent(
    event: EngagementEvent,
    engagement: Engagement,
    customer: Customer,
    val result: ConfirmationStatus,
    val respondents: Set<ContactResource> = emptySet(),
    val messageId: String? = null
) : ResponseEvent(event = event, engagement = engagement, customer = customer) {
    companion object {
        fun confirmed(engagement: Engagement, customer: Customer) = ConfirmationResponseEvent(
            EngagementEvent.CONFIRMED,
            engagement,
            customer,
            ConfirmationStatus.CONFIRMED
        )
    }
}
class CheckInResponseEvent(
    engagement: Engagement,
    customer: Customer,
    val respondents: Set<ContactResource> = emptySet(),
    val appointments: Set<AppointmentResource> = emptySet()
) : ResponseEvent(event = EngagementEvent.CHECKED_IN, engagement = engagement, customer = customer)

class CheckedInEvent(
    engagement: Engagement,
    customer: Customer,
    val respondents: Set<ContactResource> = emptySet(),
    val appointments: Set<AppointmentResource> = emptySet()
) : ResponseEvent(event = EngagementEvent.CHECKIN_DONE, engagement = engagement, customer = customer)

class ErrorEvent(
    engagement: Engagement,
    customer: Customer,
    val errors: List<ErrorDto>
): EventMessage(event = EngagementEvent.ERROR, engagement, customer)

class CancellationEvent(
    engagement: Engagement,
    customer: Customer
): EventMessage(
    engagement = engagement,
    customer = customer,
    event = EngagementEvent.CANCELLED
)