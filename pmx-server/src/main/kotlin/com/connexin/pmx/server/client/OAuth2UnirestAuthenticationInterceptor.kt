package com.connexin.pmx.server.client

import com.connexin.authentication.service.ManagedToken
import kong.unirest.Config
import kong.unirest.HttpRequest
import kong.unirest.Interceptor
import lombok.RequiredArgsConstructor
import org.slf4j.LoggerFactory
import org.springframework.util.StringUtils

@RequiredArgsConstructor
class OAuth2UnirestAuthenticationInterceptor(
    private val token: ManagedToken
) : Interceptor {
    override fun onRequest(request: HttpRequest<*>, config: Config) {
        val accessToken = token.accessToken
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("No access token for client")
        } else {
            request.header("Authorization", "Bearer $accessToken")
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(OAuth2UnirestAuthenticationInterceptor::class.java)
    }
}
