package com.connexin.pmx.server.config

import com.connexin.pmx.server.filters.Ed25519SignedRequestFilter
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.services.impl.TelnyxSmsMessageDispatcher
import com.connexin.pmx.server.services.impl.TelnyxVoiceMessageDispatcher
import com.connexin.pmx.server.services.impl.WebhookDeduperImpl
import com.connexin.pmx.server.utils.Headers
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.cfg.CoercionAction
import com.fasterxml.jackson.databind.cfg.CoercionInputShape
import com.fasterxml.jackson.databind.type.LogicalType
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.telnyx.sdk.ApiClient
import com.telnyx.sdk.api.*
import com.telnyx.sdk.auth.HttpBearerAuth
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import java.security.Security
import javax.annotation.PostConstruct

@Configuration
@EnableConfigurationProperties(TelnyxProperties::class)
class TelnyxConfig {

    @Autowired
    private lateinit var properties: TelnyxProperties

    @Bean
    fun defaultClient(): ApiClient {
        val defaultClient = com.telnyx.sdk.Configuration.getDefaultApiClient()
        val auth = defaultClient.getAuthentication("bearerAuth") as HttpBearerAuth
        auth.bearerToken = properties.apiKey

        return defaultClient
    }

    @Bean
    @Primary
    fun objectMapper(client: ApiClient): ObjectMapper {
        val mapper = client.json.mapper

        mapper.coercionConfigFor(LogicalType.Integer).setCoercion(CoercionInputShape.String, CoercionAction.TryConvert)
        return mapper.registerKotlinModule()
    }

    @Bean
    fun outboundVoiceProfilesApi(apiClient: ApiClient): OutboundVoiceProfilesApi {
        return OutboundVoiceProfilesApi(apiClient)
    }

    @Bean
    fun messagingProfilesApi(apiClient: ApiClient): MessagingProfilesApi {
        return MessagingProfilesApi(apiClient)
    }

    @Bean
    fun callControlApplicationsApi(apiClient: ApiClient): CallControlApplicationsApi {
        return CallControlApplicationsApi(apiClient)
    }

    @Bean
    fun numberConfigurationsApi(apiClient: ApiClient): NumberConfigurationsApi {
        return NumberConfigurationsApi(apiClient)
    }

    @Bean
    fun numbersOrderApi(apiClient: ApiClient): NumberOrdersApi {
        return NumberOrdersApi(apiClient)
    }

    @Bean
    fun smsMessageDispatcher(
        apiClient: ApiClient,
        urlGenerator: UrlGenerator,
        telnyxService: TelnyxService,
        deduperService: WebhookDeduperImpl
    ): SmsMessageDispatcher {
        return TelnyxSmsMessageDispatcher(
            telnyxService,
            apiClient.json.mapper,
            urlGenerator,
            deduperService,
            properties.engagement
        )
    }

    @Bean
    fun voiceMessageDispatcher(
        apiClient: ApiClient,
        urlGenerator: UrlGenerator,
        uuidSource: UUIDSource,
        @Value("\${op.pmx.call-timeout}") callTimeout: Int,
        @Value("\${op.pmx.call-time-limit}") callTimeLimit: Int,
        deduperService: WebhookDeduperImpl,
        pmxMessageService: PmxMessageService
        ): VoiceMessageDispatcher {
        val api = CallCommandsApi(apiClient)
        return TelnyxVoiceMessageDispatcher(
            api,
            apiClient.json.mapper,
            uuidSource,
            urlGenerator,
            callTimeout,
            callTimeLimit,
            deduperService,
            pmxMessageService,
            properties.engagement
        )
    }

    @Bean
    fun telnyxWebhookFilter(@Value("\${telnyx.public-key}") publicKey: String): FilterRegistrationBean<Ed25519SignedRequestFilter> {
        val registration = FilterRegistrationBean<Ed25519SignedRequestFilter>()
        val filter = Ed25519SignedRequestFilter(Headers.TELNYX_SIGNATURE_ED25519, Headers.TELNYX_TIMESTAMP, publicKey)

        registration.filter = filter
        registration.order = 1
        registration.addUrlPatterns("/webhooks/pmx/voice/*", "/webhooks/pmx/messaging/*")

        return registration
    }

    @PostConstruct
    fun init() {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }
    }
}

@ConstructorBinding
@ConfigurationProperties(prefix = "telnyx")
data class TelnyxProperties(
    val apiKey: String,
    val publicKey: String,
    val engagement: Engagement
) {
    data class Engagement(
        val englishMessagingProfileId: String,
        val spanishMessagingProfileId: String,
        val englishVoiceNumbers: String,
        val spanishVoiceNumbers: String,
        val callControlConnectionId: String
    )
}