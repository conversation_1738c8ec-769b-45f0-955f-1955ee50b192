package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.*
import com.connexin.pmx.server.services.BLIMappingService
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.UUIDSource
import com.fasterxml.jackson.core.JsonProcessingException
import io.swagger.annotations.ApiOperation
import io.swagger.v3.oas.annotations.Hidden
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("PostAPI")
@Hidden
@ApiOperation(value = "Provides legacy message delivery APIs for PMX", hidden = true)
class BLIController(
    private val pmxMessageService: PmxMessageService,
    private val customerService: CustomerService,
    private val mappingService: BLIMappingService,
    private val uuidSource: UUIDSource
) {
    companion object {
        private val log = LoggerFactory.getLogger(BLIController::class.java)
    }

    // Message endpoints

    @PostMapping(
        value = ["VTNew.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createVoiceMessage(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("XMLPost") xmlPost: String,
        @RequestParam("PostWay") postWay: String
    ): ResponseEntity<PostAPIResponse> {
        addTraceId()
        try {
            log.debug("Received POST to /PostAPI/VTNew.aspx with payload: {}", xmlPost)
            return createMessageResponse(MessageType.VOICE, userName, userPassword, xmlPost)
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["ETNew.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createEmailMessage(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("XMLPost") xmlPost: String,
        @RequestParam("PostWay") postWay: String
    ): ResponseEntity<PostAPIResponse> {
        addTraceId()
        try {
            log.debug("Received POST to /PostAPI/ETNew.aspx with payload: {}", xmlPost)
            return createMessageResponse(MessageType.EMAIL, userName, userPassword, xmlPost)
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["EBNew.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createEmailBroadcastMessage(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("XMLPost") xmlPost: String,
        @RequestParam("PostWay") postWay: String
    ): ResponseEntity<PostAPIResponse> {
        addTraceId()
        try {
            log.debug("Received POST to /PostAPI/EBNew.aspx with payload: {}", xmlPost)
            return createMessageResponse(MessageType.EMAIL_BROADCAST, userName, userPassword, xmlPost)
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["MTNew.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createTextMessage(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("XMLPost") xmlPost: String,
        @RequestParam("PostWay") postWay: String
    ): ResponseEntity<PostAPIResponse> {
        addTraceId()
        try {
            log.debug("Received POST to /PostAPI/MTNew.aspx with payload: {}", xmlPost)
            return createMessageResponse(MessageType.SMS, userName, userPassword, xmlPost)
        } finally {
            MDC.clear()
        }
    }

    // Report endpoints

    @PostMapping(
        value = ["VTReport.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createVoiceReport(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("ReturnType") returnType: String,
        @RequestParam("Unqid") uniqueId: String
    ): ResponseEntity<VTReports> {
        MDC.put("key", uniqueId)
        log.debug("Received POST to /PostAPI/VTReport.aspx")

        try {
            authenticateRequest(userName, userPassword)
            val pmxMessage = getMessageById(uniqueId)
            return ResponseEntity.ok(mappingService.mapToVoiceMessageReport(pmxMessage))
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["ETReport.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createEmailReport(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("ReturnType") returnType: String,
        @RequestParam("Unqid") uniqueId: String
    ): ResponseEntity<ETReports> {
        MDC.put("key", uniqueId)
        log.debug("Received POST to /PostAPI/ETReport.aspx")

        try {
            authenticateRequest(userName, userPassword)
            val pmxMessage = getMessageById(uniqueId)
            return ResponseEntity.ok(mappingService.mapToEmailReport(pmxMessage))
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["EBReport.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createEmailBroadcastReport(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("ReturnType") returnType: String,
        @RequestParam("OrderID") orderId: String
    ): ResponseEntity<EBReports> {
        MDC.put("key", orderId)
        log.debug("Received POST to /PostAPI/EBReport.aspx")

        try {
            authenticateRequest(userName, userPassword)
            val pmxMessage = getMessageById(orderId)
            return ResponseEntity.ok(mappingService.mapToEmailBroadcastReport(pmxMessage))
        } finally {
            MDC.clear()
        }
    }

    @PostMapping(
        value = ["MTReport.aspx"],
        consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE],
        produces = [MediaType.TEXT_XML_VALUE]
    )
    fun createTextMessageReport(
        @RequestParam("UserName") userName: String,
        @RequestParam("UserPassword") userPassword: String,
        @RequestParam("ReturnType") returnType: String,
        @RequestParam("Unqid") uniqueId: String
    ): ResponseEntity<MTReports> {
        MDC.put("key", uniqueId)
        log.debug("Received POST to /PostAPI/MTReport.aspx")

        try {
            authenticateRequest(userName, userPassword)
            val pmxMessage = getMessageById(uniqueId)
            return ResponseEntity.ok(mappingService.mapToTextMessageReport(pmxMessage))
        } finally {
            MDC.clear()
        }
    }

    //==========================================================================================
    // supporting methods

    private fun createMessageResponse(
        messageType: MessageType,
        userName: String,
        userPassword: String,
        xmlPost: String
    ): ResponseEntity<PostAPIResponse> {
        val customer = authenticateRequest(userName, userPassword)

        val messageRequest = when (messageType) {
            MessageType.VOICE -> {
                val vtMessage = mappingService.parseXml(xmlPost, VTMessages::class.java)
                mappingService.mapToCreateMessageRequest(vtMessage, customer.id!!)
            }
            MessageType.EMAIL -> {
                val etMessage = mappingService.parseXml(xmlPost, ETMessages::class.java)
                mappingService.mapToCreateMessageRequest(etMessage, customer.id!!)
            }
            MessageType.EMAIL_BROADCAST -> {
                val ebMessage = mappingService.parseXml(xmlPost, EBMessages::class.java)
                mappingService.mapToCreateMessageRequest(ebMessage, customer.id!!)
            }
            MessageType.SMS -> {
                val mtMessage: MTMessages = mappingService.parseXml(xmlPost, MTMessages::class.java)
                mappingService.mapToCreateMessageRequest(mtMessage, customer.id!!)
            }
        }

        val createResponse = pmxMessageService.create(messageRequest)
        if (createResponse.success) {
            return ResponseEntity.ok(
                PostAPIResponse(
                    SaveTransactionalOrderResult(transactionID = createResponse.message!!.id)
                )
            )
        }

        log.warn("Bad Request [{}]", createResponse)

        return ResponseEntity.badRequest().body(
            PostAPIResponse(
                SaveTransactionalOrderResult(exception = createResponse.errors?.joinToString { it.message })
            )
        )
    }

    @ExceptionHandler
    fun handleException(ex: Exception): ResponseEntity<PostAPIResponse> {
        log.warn("An exception occurred", ex)
        val status = when (ex) {
            is AuthenticationException -> HttpStatus.UNAUTHORIZED
            is MessageNotFoundException -> HttpStatus.NOT_FOUND
            is JsonProcessingException -> HttpStatus.BAD_REQUEST
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }
        return ResponseEntity
            .status(HttpStatus.OK) // PMX craps out if BLI returns a non-OK status, even if it's really an error
            .contentType(MediaType.TEXT_XML)
            .body(
                PostAPIResponse(
                    SaveTransactionalOrderResult(error = status.value().toString(), exception = ex.message)
                )
            )
    }

    private fun authenticateRequest(username: String, password: String): Customer {
        val customer = customerService.authenticate(username, password)
        if (customer != null) {
            MDC.put("opmedId", customer.id)
            return customer
        }

        throw AuthenticationException("Unable to authenticate request.")
    }

    private fun getMessageById(id: String): PmxMessage {
        return pmxMessageService.getById(id)
            ?: throw MessageNotFoundException("Job with id $id not found.")
    }

    private fun addTraceId() {
        MDC.put("traceId", uuidSource.randomUUIDString())
    }
}

class AuthenticationException(message: String) : RuntimeException(message)
class MessageNotFoundException(message: String) : RuntimeException(message)