package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.SubscriptionPreference
import com.connexin.pmx.server.utils.CacheKeys
import org.springframework.cache.annotation.CacheConfig
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.CachePut
import org.springframework.cache.annotation.Caching
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant

interface SubscriptionPreferenceRepository : MongoRepository<SubscriptionPreference, String>,
    CustomizedSubscriptionPreferenceRepository<SubscriptionPreference, String>

interface CustomizedSubscriptionPreferenceRepository<T, ID> {
    fun <S : T?> save(entity: S): S
}

@Repository
@CacheConfig(
    cacheNames = [CacheKeys.subscriptionsById]
)
class CustomizedSubscriptionPreferenceRepositoryImpl(private val mongoTemplate: MongoTemplate) :
    CustomizedSubscriptionPreferenceRepository<SubscriptionPreference, String> {
    @Caching(
        put = [
            CachePut(
                key = "#entity.id",
                condition = "#entity != null",
                unless = "#result == null"
            )
        ],
        evict = [
            CacheEvict(
                value = [CacheKeys.subscriptionsById],
                allEntries = true
            )
        ]
    )
    override fun <S : SubscriptionPreference?> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        entity!!.createdAt = entity.createdAt ?: Instant.now()
        entity.updatedAt = Instant.now()

        return mongoTemplate.save(entity)
    }
}