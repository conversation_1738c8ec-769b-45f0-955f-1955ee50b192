package com.connexin.pmx.server.services

import com.connexin.pmx.server.annotation.TraceExecutionTime
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.CreateMessageResponse
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.repositories.PmxMessageRepository
import com.connexin.pmx.server.utils.CacheKeys
import com.connexin.pmx.server.utils.EmailUtil
import com.connexin.pmx.server.utils.PhoneNumberUtil
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.CachePut
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.valiktor.ConstraintViolationException
import org.valiktor.i18n.mapToMessage
import java.time.Instant
import java.util.*


@Service
class PmxMessageService(
    private val repository: PmxMessageRepository,
    private val customerService: CustomerService,
    private val meterService: MeterService
) {
    fun create(request: CreateMessageRequest): CreateMessageResponse {
        log.debug("Creating message {}", request)
        val sample = meterService.startTimer()

        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            sample.stop(meterService.getCreateMessageTimer(request.customerId, request.type, false))
            log.debug("CreateMessageRequest failed validation", ex)
            return CreateMessageResponse(success = false, errors = ex.constraintViolations
                .mapToMessage(baseName = "messages", locale = Locale.ENGLISH)
                .map { ErrorDto(path = it.property, message = it.message, errorCode = Errors.VALIDATION_FAILED.code) })
        }

        val customer = request.customer ?: customerService.getById(request.customerId)

        if (customer == null) {
            sample.stop(meterService.getCreateMessageTimer(request.customerId, request.type, false))
            log.error("Could not create message because it referenced an unknown customer {}", request.customerId)
            return CreateMessageResponse(
                success = false,
                errors = mutableListOf(ErrorDto(path = "opmedId", "Unknown customer ${request.customerId}", errorCode = Errors.NOT_FOUND.code))
            )
        }

        val normalized = normalize(
            PmxMessage(
                type = request.type,
                status = MessageStatus.QUEUED,
                customerId = customer.id!!,
                sendAfter = request.sendAfter ?: Instant.now(),
                sendWindow = request.getSendWindow(),
                to = if (request.type == MessageType.EMAIL_BROADCAST) null else request.to.first(),
                message = request.message,
                altMessage = request.altMessage,
                replyTo = request.replyTo,
                confirmationStatus = if (request.type == MessageType.VOICE) ConfirmationStatus.UNCONFIRMED else ConfirmationStatus.NOT_APPLICABLE,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                subject = request.subject,
                engagementId = request.engagementId,
                engagementRuleId = request.engagementRuleId,
                language = request.language,
                instructions = request.instructions,
                emailRecipients = if (request.type == MessageType.EMAIL_BROADCAST) request.to.filter {
                    EmailUtil.isValid(it)
                }.map {
                    PmxMessage.EmailRecipient(
                        address = it,
                        status = MessageStatus.QUEUED
                    )
                } else null
            )
        )

        val message = repository.save(normalized)
        sample.stop(meterService.getCreateMessageTimer(request.customerId, request.type, true))
        log.debug("Created message {}", message)

        return CreateMessageResponse(success = true, message = message)
    }

    fun normalize(message: PmxMessage): PmxMessage {
        return when (message.type) {
            MessageType.SMS, MessageType.VOICE -> {
                message.copy(
                    message = if (message.type == MessageType.SMS) replaceSmsMessage(message.message.trim()) else message.message.trim(),
                    altMessage = message.altMessage?.trim(),
                    to = PhoneNumberUtil.canonicalize(message.to)!!,
                    replyTo = PhoneNumberUtil.canonicalize(message.replyTo)
                )
            }
            else -> {
                message.copy(
                    message = message.message.trim(),
                    altMessage = message.altMessage?.trim(),
                    subject = message.subject?.trim(),
                    to = message.to?.trim(),
                    replyTo = message.replyTo?.trim()
                )
            }
        }
    }

    fun replaceSmsMessage(original: String): String {
        var message = original
        // look for canned confirmation text w/ link and strip it
        message = message.replace(CONFIRM_PATTERN, "")

        return message
    }

    @CachePut(
        value = [CacheKeys.messagesByRemoteId],
        key = "#message.remoteId",
        condition = "#message.remoteId != null",
        unless = "#result == null"
    )
    fun update(message: PmxMessage): PmxMessage {
        val sample = meterService.startTimer()
        val saved = repository.save(message)
        sample.stop(meterService.getMessageUpdateTimer())

        return saved
    }

    @CacheEvict(
        value = [CacheKeys.messagesByRemoteId],
        key = "#result.remoteId",
        condition = "#result.remoteId != null"
    )
    fun patch(messageId: String, fields: Map<String, Any?>): PmxMessage? {
        val sample = meterService.startTimer()
        val modified = repository.updateFields(messageId, fields)
        sample.stop(meterService.getMessageUpdateTimer())

        return modified
    }

    @Cacheable(
        value = [CacheKeys.messagesByRemoteId],
        key = "#remoteId",
        condition = "#remoteId != null",
        unless = "#result == null"
    )
    fun getByRemoteId(remoteId: String): PmxMessage? {
        val message = repository.findByRemoteId(remoteId)

        return if (message.isPresent) message.get() else null
    }

    fun getById(id: String): PmxMessage? {
        val message = repository.findById(id)

        return if (message.isPresent) message.get() else null
    }

    /**
     * Finds the next queued messages, sorting by sendAfter ascending.
     * @param now The current instant, used find messages ready to send at this moment.
     * @param page The page request details.
     * @return A list of PmxMessages ready to send.
     */
    fun findReadyToDispatch(now: Instant, page: PageRequest): List<PmxMessage> {
        val sample = meterService.startTimer()
        val results = repository.findReadyToDispatch(now, page.withSort(Sort.Direction.ASC, "sendAfter"))
        sample.stop(meterService.getFindQueuedTimer())
        return results
    }

    fun findQueuedForEngagement(engagementId: String): List<PmxMessage> {
        return repository.findQueuedByEngagementId(engagementId)
    }

    @TraceExecutionTime
    fun getMostRecentlyDeliveredEngagementSmsMessage(to: String): PmxMessage? {
        val sample = meterService.startTimer()
        val message = repository.getMostRecentlyDeliveredEngagementSmsMessage(to)
        sample.stop(meterService.getFindMostRecentDeliveredTimer())

        return message
    }

    @TraceExecutionTime
    fun findForEngagementIdAndContact(engagementId: String, contact: String): PmxMessage? {
        return repository.getMostRecentByEngagementIdAndContact(engagementId, contact)
    }

    @CacheEvict(
        value = [CacheKeys.messagesByRemoteId],
        key = "#message.remoteId",
        condition = "#message.remoteId != null"
    )
    fun delete(message: PmxMessage) {
        log.debug("Received request to delete message {}", message.id)
        repository.delete(message)
    }

    companion object {
        private val log = LoggerFactory.getLogger(PmxMessageService::class.java)

        val CONFIRM_PATTERN = Regex("""(confirm:?)?\s*https://patients(.dev)?.op.health/confirm/[A-Z0-9]+/?""", setOf(RegexOption.IGNORE_CASE, RegexOption.MULTILINE))
    }
}