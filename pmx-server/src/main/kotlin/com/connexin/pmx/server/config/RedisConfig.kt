package com.connexin.pmx.server.config

import io.lettuce.core.ReadFrom
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.connection.RedisPassword
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import java.net.URI

@Configuration
@EnableConfigurationProperties(value = [CacheProperties::class, RedisProperties::class])
@ConditionalOnProperty(name = ["redis.enabled"], havingValue = "true")
class RedisConfig {

    init {
        log.info("Configuring Redis")
    }

    companion object {
        private val log = LoggerFactory.getLogger(RedisConfig::class.java)
    }

    @Bean
    fun redisConnectionFactory(redisProperties: RedisProperties): RedisConnectionFactory {
        if (redisProperties.nodes.isEmpty()) {
            throw IllegalStateException("Missing Redis node addresses")
        }

        // master node
        val master = redisProperties.nodes.first()
        val redisConfig = RedisStaticMasterReplicaConfiguration(master.host, master.port)

        // replica nodes
        for (i in 1 until redisProperties.nodes.size) {
            redisConfig.addNode(redisProperties.nodes[i].host, redisProperties.nodes[i].port)
        }

        if (!redisProperties.password.isNullOrEmpty()) {
            redisConfig.password = RedisPassword.of(redisProperties.password)
        }

        val clientConfig = LettuceClientConfiguration.builder()
            .readFrom(ReadFrom.REPLICA_PREFERRED)

        if (master.scheme.equals("rediss", true)) {
            clientConfig.useSsl()
        }

        return LettuceConnectionFactory(redisConfig, clientConfig.build())
    }
}

@ConstructorBinding
@ConfigurationProperties("redis")
data class RedisProperties(
    val enabled: Boolean = false,
    val nodes: MutableList<URI> = mutableListOf(),
    val password: String?
)