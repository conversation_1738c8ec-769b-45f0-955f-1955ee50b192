package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    enumAsRef = true
)
enum class TemplateScenario {
    /**
     * The default template scenario.
     */
    DEFAULT,
    /**
     * The deadline before which a contact must confirm or decline an appointment has passed.
     */
    CONFIRMATION_DEADLINE_PASSED,
    /**
     * Contact is attempting to confirm a previously declined/cancelled appointment.
     */
    CONFIRMATION_CANNOT_CHANGE_RESPONSE,

    /**
     * The contact has confirmed and should get an immediate check-in message
     */
    CONFIRMED_CHECK_IN,

    /**
     * Performing a same-day check-in
     */
    SAME_DAY_CHECK_IN
}