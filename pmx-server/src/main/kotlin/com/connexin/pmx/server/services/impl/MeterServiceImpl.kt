package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.services.MeterService
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

/**
 * A concrete service for creating meters.
 */
@Service
class MeterServiceImpl(
    private val registry: MeterRegistry
): MeterService {
    private val dispatchInitiateTimer = ConcurrentHashMap<String, Timer>()
    private val dispatchRespondTimer = ConcurrentHashMap<String, Timer>()
    private val createMessageTimer = ConcurrentHashMap<String, Timer>()
    private val dispatchScheduledTimer = Timer.builder("op.pmx.dispatch.scheduled.time")
        .description("Total time taken to dispatch a batch of queued messages.")
        .register(registry)
    private val updateMessageTimer = Timer.builder("op.pmx.message.update.time")
        .description("Total time taken to update messages.")
        .register(registry)
    private val findQueuedTimer = Timer.builder("op.pmx.message.find.queued.time")
        .description("Time taken to query for queued messages.")
        .register(registry)
    private val findProvisioningQueuedTimer = Timer.builder("op.pmx.provisioning.find.queued.time")
        .description("Time taken to query for queued provisioning orders")
        .register(registry)
    private val findDeliveredTimer = Timer.builder("op.pmx.message.find.delivered.time")
        .description("Time taken to query for delivered messages.")
        .register(registry)

    /**
     * Returns the timer for metering initiate calls on the dispatcher.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    override fun getDispatchInitiateTimer(customerId: String, type: MessageType, success: Boolean): Timer {
        val key = "${customerId}_${type}_$success"

        return dispatchInitiateTimer.getOrPut(key) {
            Timer.builder("op.pmx.message.dispatch.initiate.time")
                .description("Time taken for the dispatcher to initiate a message.")
                .tags("opmedId", customerId, "type", type.toString(), "success", success.toString())
                .register(registry)
        }
    }

    /**
     * Returns the timer for metering respond calls on the dispatcher.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    override fun getDispatchRespondTimer(customerId: String, type: MessageType, success: Boolean): Timer {
        val key = "${customerId}_${type}_$success"

        return dispatchRespondTimer.getOrPut(key) {
            Timer.builder("op.pmx.message.dispatch.respond.time")
                .description("Time taken for the dispatcher to respond to the vendor update regarding a message.")
                .tags("opmedId", customerId, "type", type.toString(), "success", success.toString())
                .register(registry)
        }
    }

    /**
     * Returns the timer for metering respond calls to create new messages.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    override fun getCreateMessageTimer(customerId: String, type: MessageType, success: Boolean): Timer {
        val key = "${customerId}_${type}_$success"

        return createMessageTimer.getOrPut(key) {
            Timer.builder("op.pmx.message.create.time")
                .description("Time taken for to create a new message.")
                .tags("opmedId", customerId, "type", type.toString(), "success", success.toString())
                .register(registry)
        }
    }

    /**
     * Returns a timer for metering the total time it takes to dispatch a batch of scheduled messages.
     * @return The timer
     */
    override fun getDispatchScheduledTimer(): Timer {
        return dispatchScheduledTimer
    }

    /**
     * Returns a timer for metering how long it takes to update a message.
     * @return The timer
     */
    override fun getMessageUpdateTimer(): Timer {
        return updateMessageTimer
    }

    /**
     * Returns a timer for metering how long it takes to find queued messages.
     * @return The timer
     */
    override fun getFindQueuedTimer(): Timer {
        return findQueuedTimer
    }

    /**
     * Returns a timer for metering how long it takes to find queued provisioning orders.
     */
    override fun getFindProvisioningQueuedTimer(): Timer {
        return findProvisioningQueuedTimer
    }

    /**
     * Returns a timer for metering how long it takes to find the most recent delivered message by message type.
     */
    override fun getFindMostRecentDeliveredTimer(): Timer {
        return findDeliveredTimer
    }

    /**
     * Starts a timer on the registry and returns a Sample
     * @return A sample which can be used to record duration for a Timer.
     */
    override fun startTimer(): Timer.Sample {
        return Timer.start(registry)
    }
}