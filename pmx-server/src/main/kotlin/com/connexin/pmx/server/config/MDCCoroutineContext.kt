package com.connexin.pmx.server.config

import kotlinx.coroutines.ThreadContextElement
import org.slf4j.MDC
import kotlin.coroutines.CoroutineContext

/**
 * A custom coroutine context element for propagating MDC (Mapped Diagnostic Context) data
 * across coroutine boundaries. This ensures that the MDC context from the parent thread
 * is available within the coroutine's execution and is restored when the coroutine completes.
 *
 * https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-core/kotlinx.coroutines/-thread-context-element/
 *
 * ## How to Use
 *
 * 1. Capture the current MDC context using `MDC.getCopyOfContextMap()` before launching the coroutine.
 * 2. Pass the captured context to `MDCCoroutineContext` and include it in the coroutine's context.
 * 3. Inside the coroutine, you can safely access the MDC values as they are propagated.
 * 4. The MDC context will automatically be restored to its original state after the coroutine completes.
 *
 * Example:
 *  runBlocking {
 *     val job = launch(Dispatchers.IO + MDCCoroutineContext(MDC.getCopyOfContextMap())) {
 *         println(MDC.get("key"))
 *     }
 *     job.join()
 * }
 * // After the coroutine completes, the original MDC context is restored
 *
 * @property contextMap A map containing the MDC context to propagate to the coroutine.
 */

class MDCCoroutineContext(private val contextMap: Map<String, String>?) : ThreadContextElement<Map<String, String>?> {

    // declare companion object for a key of this element in coroutine context
    companion object Key : CoroutineContext.Key<MDCCoroutineContext>

    // provide the key of the corresponding context element
    override val key: CoroutineContext.Key<*>
        get() = Key

    // this is invoked before coroutine is resumed on current thread
    override fun updateThreadContext(context: CoroutineContext): Map<String, String>? {
        val oldContext = MDC.getCopyOfContextMap()
        MDC.setContextMap(contextMap ?: emptyMap())
        return oldContext
    }

    // this is invoked after coroutine has suspended on current thread
    override fun restoreThreadContext(context: CoroutineContext, oldState: Map<String, String>?) {
        if (oldState != null) {
            MDC.setContextMap(oldState)
        } else {
            MDC.clear()
        }
    }
}