package com.connexin.pmx.server.models

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.swagger.v3.oas.annotations.media.Schema

/**
 * A contract for all types that are resources.
 */
@Schema(
    description = "A resource that engagement references for more details when interacting with patients and their contacts.",
    oneOf = [
        ContactResource::class,
        PatientResource::class,
        PracticeResource::class,
        LocationResource::class,
        StaffResource::class,
        AppointmentResource::class
    ]
)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = Constants.TYPE_PROPERTY,
    visible = true
)
@JsonSubTypes(
    JsonSubTypes.Type(value = ContactResource::class, name = Constants.CONTACT_RESOURCE),
    JsonSubTypes.Type(value = PatientResource::class, name = Constants.PATIENT_RESOURCE),
    JsonSubTypes.Type(value = PracticeResource::class, name = Constants.PRACTICE_RESOURCE),
    JsonSubTypes.Type(value = LocationResource::class, name = Constants.LOCATION_RESOURCE),
    JsonSubTypes.Type(value = StaffResource::class, name = Constants.STAFF_RESOURCE),
    JsonSubTypes.Type(value = AppointmentResource::class, name = Constants.APPOINTMENT_RESOURCE),
)
interface Resource {
    /**
     * The ID of the resource.
     */
    val id: String

    /**
     * The type of the resource.
     */
    val type: ResourceType
}