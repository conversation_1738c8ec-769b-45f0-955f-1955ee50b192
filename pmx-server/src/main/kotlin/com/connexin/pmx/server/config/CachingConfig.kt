package com.connexin.pmx.server.config

import com.connexin.pmx.server.utils.CacheKeys
import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Ticker
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.cache.RedisCacheManagerBuilderCustomizer
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.support.SimpleCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.cache.RedisCacheManager
import java.time.Duration
import java.util.concurrent.TimeUnit


@ConstructorBinding
@ConfigurationProperties("op.pmx.cache")
data class CacheProperties(
    val customersDuration: Long = 60,
    val customersMax: Long = 500,
    val customersByCredentialsDuration: Long = 60,
    val customersByCredentialsMax: Long = 500,
    val messagesByRemoteIdDuration: Long = 5,
    val messagesByRemoteIdMax: Long = 1000,
    val sesSendQuotaDuration: Long = 30,
    val webhooksDuration: Long = 60,
    val webhooksMax: Long = 2000,
    val templatesByIdDuration: Long = 60,
    val templatesByIdMax: Long = 2000,
    val zipCodesDuration: Long = 60,
    val zipCodesMax: Long = 2000
)

@Configuration
@EnableConfigurationProperties(CacheProperties::class)
@EnableCaching
@ConditionalOnExpression("'\${spring.cache.type}' != 'none'")
class CachingConfig {

    @Bean
    @ConditionalOnProperty(name = ["spring.cache.type"], havingValue = "caffeine")
    fun cacheManager(ticker: Ticker, cacheProperties: CacheProperties): CacheManager? {
        val customersCache = buildCaffeineCache(
            CacheKeys.customers, ticker,
            durationInMinutes = cacheProperties.customersDuration,
            max = cacheProperties.customersMax
        )
        val customersByCredentialsCache = buildCaffeineCache(
            CacheKeys.customersByCredentials, ticker,
            durationInMinutes = cacheProperties.customersByCredentialsDuration,
            max = cacheProperties.customersByCredentialsMax
        )
        val messagesByRemoteIdCache = buildCaffeineCache(
            CacheKeys.messagesByRemoteId, ticker,
            durationInMinutes = cacheProperties.messagesByRemoteIdDuration,
            max = cacheProperties.messagesByRemoteIdMax
        )
        val sesSendQuotaCache = buildCaffeineCache(
            CacheKeys.sesSendQuota, ticker,
            durationInMinutes = cacheProperties.sesSendQuotaDuration,
            max = 1L
        )
        val webhooksCache = buildCaffeineCache(
            CacheKeys.webhooks, ticker,
            durationInMinutes = cacheProperties.webhooksDuration,
            max = cacheProperties.webhooksMax
        )
        val templatesByIdCache = buildCaffeineCache(
            CacheKeys.templatesById, ticker,
            durationInMinutes = cacheProperties.templatesByIdDuration,
            max = cacheProperties.templatesByIdMax
        )

        val zipCodesCache = buildCaffeineCache(
            CacheKeys.zipCodes, ticker,
            durationInMinutes = cacheProperties.zipCodesDuration,
            max = cacheProperties.zipCodesMax
        )
        val manager = SimpleCacheManager()
        manager.setCaches(
            listOf(
                customersCache, customersByCredentialsCache, messagesByRemoteIdCache,
                sesSendQuotaCache, webhooksCache, templatesByIdCache, zipCodesCache
            )
        )
        return manager
    }

    @Bean
    @ConditionalOnProperty(name = ["spring.cache.type"], havingValue = "redis")
    fun redisCacheManagerBuilderCustomizer(
        cacheProperties: CacheProperties,
        @Value("\${spring.cache.redis.key-prefix}") cachePrefix: String
    ): RedisCacheManagerBuilderCustomizer {
        return RedisCacheManagerBuilderCustomizer { builder: RedisCacheManager.RedisCacheManagerBuilder ->
            // prefixing all entity cache names to avoid possible serialization exceptions
            builder
                .withCacheConfiguration(
                    CacheKeys.customers,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.customersDuration))
                        .prefixCacheNameWith(cachePrefix)
                )
                .withCacheConfiguration(
                    CacheKeys.customersByCredentials,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.customersByCredentialsDuration))
                        .prefixCacheNameWith(cachePrefix)
                )
                .withCacheConfiguration(
                    CacheKeys.messagesByRemoteId,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.messagesByRemoteIdDuration))
                        .prefixCacheNameWith(cachePrefix)
                )
                .withCacheConfiguration(
                    CacheKeys.templatesById,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.templatesByIdDuration))
                        .prefixCacheNameWith(cachePrefix)
                )
                .withCacheConfiguration(
                    CacheKeys.sesSendQuota,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.sesSendQuotaDuration))
                )
                .withCacheConfiguration(
                    CacheKeys.webhooks,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.webhooksDuration))
                )
                .withCacheConfiguration(
                    CacheKeys.zipCodes,
                    RedisCacheConfiguration.defaultCacheConfig()
                        .entryTtl(Duration.ofMinutes(cacheProperties.zipCodesDuration))
                )
        }
    }

    private fun buildCaffeineCache(
        name: String,
        ticker: Ticker,
        durationInMinutes: Long = 5,
        max: Long = 500
    ): CaffeineCache {
        return CaffeineCache(
            name, Caffeine.newBuilder()
                .expireAfterWrite(durationInMinutes, TimeUnit.MINUTES)
                .maximumSize(max)
                .ticker(ticker)
                .build()
        )
    }

    @Bean
    fun ticker(): Ticker? {
        return Ticker.systemTicker()
    }
}