package com.connexin.pmx.server.services

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.util.UriComponentsBuilder
import java.net.URL
import java.net.URLEncoder

@Component
class UrlGenerator(
    @Value("\${op.pmx.base-url}")
    base: String,
    @Value("\${op.pmx.confirmation-base-url}")
    confirmationBase: String
) {
    private val baseURL: URL
    private val inboundVoicePmxWebhookURL: URL
    private val statusMessagingPmxWebhookURL: URL
    private val eventsEmailPmxWebhookURL: URL
    private val unsubscribeUrl: URL
    private val confirmUrl: URL
    init {
        baseURL = URL(base)
        inboundVoicePmxWebhookURL = UriComponentsBuilder.fromHttpUrl(base)
            .pathSegment("webhooks/pmx/voice/incoming")
            .build().toUri().toURL()
        statusMessagingPmxWebhookURL = UriComponentsBuilder.fromHttpUrl(base)
            .pathSegment("webhooks/pmx/messaging/status")
            .build().toUri().toURL()
        eventsEmailPmxWebhookURL = UriComponentsBuilder.fromHttpUrl(base)
            .pathSegment("webhooks/pmx/email/events")
            .build().toUri().toURL()
        unsubscribeUrl = UriComponentsBuilder.fromHttpUrl(base)
            .pathSegment("unsubscribe")
            .build().toUri().toURL()
        confirmUrl = UriComponentsBuilder.fromHttpUrl(confirmationBase)
            .pathSegment("confirm")
            .build().toUri().toURL()
    }

    fun pmxVoiceWebhook(): URL = inboundVoicePmxWebhookURL

    fun pmxMessagingWebhook(): URL = statusMessagingPmxWebhookURL

    fun pmxEmailWebhook(): URL = eventsEmailPmxWebhookURL

    fun unsubscribe(params: Map<String, String>): URL {
        var builder = UriComponentsBuilder.fromUri(unsubscribeUrl.toURI())

        params.forEach { (key, value) -> builder = builder.queryParam(key, URLEncoder.encode(value, Charsets.UTF_8)) }

        return builder.build(true).toUri().toURL()
    }

    fun confirm(params: Map<String, String>): URL {
        var builder = UriComponentsBuilder.fromUri(confirmUrl.toURI())

        params.forEach { (key, value) -> builder = builder.queryParam(key, value) }

        return builder.build().toUri().toURL()
    }
}