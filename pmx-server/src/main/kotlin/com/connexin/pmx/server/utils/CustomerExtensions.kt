package com.connexin.pmx.server.utils

import com.connexin.pmx.server.models.*

/**
 * Returns a set of applicable rules to the engagement.
 * @param engagement The engagement to check.
 * @return A set of all enabled rules that apply to the appointments in the engagement.
 */
fun Customer.getApplicableRules(engagement: Engagement): MutableSet<EngagementRule>? {
    val appointments = engagement.resources
        .filterIsInstance(AppointmentResource::class.java)

    if (appointments.isEmpty()) return null

    val practices = mutableSetOf<String>()
    val apptTypes = mutableSetOf<String>()
    val staff = mutableSetOf<String>()
    val locations = mutableSetOf<String>()
    val startTime = appointments.first().startTime

    appointments.forEach {
        if (it.practice != null) {
            practices.add(it.practice)
        }
        apptTypes.add(it.appointmentType)
        if (it.staff != null) {
            staff.add(it.staff)
        }
        locations.add(it.location)
    }

    return this.engagementRules.filter {
        val practiceRule =
            it.practiceLocations?.firstOrNull { p -> practices.contains(p.practiceId) }

        // rule must be:
        // enabled
        // and appt start time within rule's effective window
        // and accepts all types or appointment's type matches
        // and accepts all staff or appointment's staff matches
        // and accepts all practice locations or the appt's type practice/location matches a practice rule
        it.enabled &&
                startTime.isWithin(it.startDate, it.endDate)
                && (
                it.allAppointmentTypes
                        || it.appointmentTypes == null
                        || it.appointmentTypes.intersect(apptTypes).isNotEmpty()
                )
                && (
                it.allStaff
                        || it.staff == null
                        || it.staff.intersect(staff).isNotEmpty()
                )
                && (
                it.allPracticeLocations
                        || it.practiceLocations == null
                        || (
                        practiceRule != null
                                && (
                                practiceRule.allLocations
                                        || practiceRule.locations == null
                                        || practiceRule.locations!!.intersect(locations).isNotEmpty())
                        )
                )
    }.toMutableSet()
}

/**
 * Returns whether there are any active rules that apply to the appointment.
 * @param engagement The engagement to check.
 * @return True if there is at least one applicable rule; otherwise, false.
 */
fun Customer.hasApplicableRule(engagement: Engagement): Boolean {
    val rules = this.getApplicableRules(engagement)

    return !rules.isNullOrEmpty()
}

/**
 * Gathers all applicable rules to the engagement, and suggests the first rule per workflow.
 * @param engagement The engagement to check.
 * @return A map of workflows to applicable rules.
 */
fun Customer.getApplicableRulesByWorkflow(engagement: Engagement): Map<EngagementWorkflow, EngagementRule>? {
    val applicable = this.getApplicableRules(engagement) ?: return null

    val surveyLinkWorkflows = setOf(EngagementWorkflow.APPT_SURVEY_EXTERNAL, EngagementWorkflow.APPT_SURVEY_INTERNAL)
    val surveyLinkRules = applicable.filter { surveyLinkWorkflows.contains(it.workflow) }

    // If we found any survey link rule, we synthetically add an engagement rule of APPOINTMENT_SURVEY so it generalizes the usage of sending
    // appointment surveys and process them and send for the desired contact. We do this since we will have 2 templates, one for internal
    // surveys and external surveys, as the process is the same for getting them we need to generalize it, know we should process it
    // on our state machine and send 1 or both types from one single go
    if (!surveyLinkRules.isEmpty()) {
        val syntheticId = "${surveyLinkRules.first().id}-s"
        applicable.add(EngagementRule(syntheticId, workflow = EngagementWorkflow.APPOINTMENT_SURVEY))
    }

    return applicable.groupBy { it.workflow }
        .mapValues { it.value.first() }
}