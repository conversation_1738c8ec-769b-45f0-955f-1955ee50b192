package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.*
import java.io.Serializable
import java.time.Instant

@Schema(
    description = "A rule that determines which workflows and which templates should be used when engaging a contact for an appointment that matches a set of criteria."
)
class EngagementRule(
    @field:Schema(description = "The vendor's ID for the rule." )
    var id: String,
    @field:Schema(description = "Whether or not the rule should be enabled.")
    var enabled: Boolean = true,
    @field:Schema(
        description = "Determines which workflow should be activated if an appointment matches this rule."
    )
    var workflow: EngagementWorkflow,
    @field:Schema(description = "Whether the rule should match any appointment type.")
    var allAppointmentTypes: Boolean = false,
    @field:Schema(description = "Whether the rule should match any staff member.")
    var allStaff: Boolean = false,
    @field:Schema(description = "Whether the rule should match any location in all practices.")
    val allPracticeLocations: Boolean = false,
    @field:Schema(description = "The starting date and time (in UTC) when the rule should take effect.")
    val startDate: Instant? = null,
    @field:Schema(description = "The ending date and time (in UTC) when the rule should no longer be in effect.")
    val endDate: Instant? = null,
    @field:Schema(description = "Overridden templates that can be used for EMAIL or VOICE messages.")
    val templateOverrides: Map<TemplateScenario, Map<MessageType, Map<Language, Template.Segments>>>? = null,
    @Deprecated("Use templateId instead")
    @field:Schema(description = "A map of scenarios to the desired standard template ID.", deprecated = true)
    val templateIds: Map<TemplateScenario, String> = emptyMap(),
    @field:Schema(description = "If allAppointmentTypes is false, a set of vendor appointment type IDs that the rule applies to.")
    val appointmentTypes: Set<String>? = null,
    @field:Schema(description = "If allPracticeLocations is false, a set of practices and locations the rule applies to.")
    val practiceLocations: Set<PracticeLocationRule>? = null,
    @field:Schema(description = "If allStaff is false, a set of staff IDs the rule applies to.")
    val staff: Set<String>? = null,
    @field:Schema(description = "The schedule at which this rule will send and check for responses.")
    var schedule: Schedule? = null,
    @field:Schema(description = "The template ID associated with the rule")
    var templateId: String? = templateIds[TemplateScenario.DEFAULT]
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EngagementRule

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    fun validate() {
        org.valiktor.validate(this) {
            validate(EngagementRule::id).isNotBlank()

            // continue validating templateIds until deprecated field is removed
            if (templateId.isNullOrEmpty()) {
                validate(EngagementRule::templateIds).isNotEmpty()
            }
            if (templateIds.isEmpty()) {
                validate(EngagementRule::templateId).isNotBlank()
            }

            if (endDate != null) {
                validate(EngagementRule::startDate).isNotNull()

                if (startDate != null) {
                    validate(EngagementRule::endDate).isGreaterThan(startDate)
                }
            }

            if (templateOverrides != null) {
                validate(EngagementRule::templateOverrides).isNotEmpty()
                validate(EngagementRule::templateOverrides).containsKey(TemplateScenario.DEFAULT)
            }
        }
    }

    override fun toString(): String {
        return "EngagementRule(id='$id', enabled=$enabled, workflow=$workflow, allAppointmentTypes=$allAppointmentTypes, allStaff=$allStaff, allPracticeLocations=$allPracticeLocations, startDate=$startDate, endDate=$endDate, templateOverrides=$templateOverrides, templateIds=$templateIds, appointmentTypes=$appointmentTypes, practiceLocations=$practiceLocations, staff=$staff, schedule=$schedule, templateId=$templateId)"
    }
}

@Schema(
    description = "Describes how to match a practice and its locations to a rule."
)
class PracticeLocationRule(
    @field:Schema(
        description = "The vendor's ID for the practice."
    )
    val practiceId: String,
    @field:Schema(
        description = "Whether the rule should apply to all locations in the practice."
    )
    var allLocations: Boolean = false,
    @field:Schema(
        description = "If allLocations is false, the locations in the practice the rule applies to."
    )
    var locations: Set<String>? = null
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PracticeLocationRule

        if (practiceId != other.practiceId) return false

        return true
    }

    override fun hashCode(): Int {
        return practiceId.hashCode()
    }
}
