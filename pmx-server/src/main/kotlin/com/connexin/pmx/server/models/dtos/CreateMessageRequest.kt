package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.constraints.eachIsEmail
import com.connexin.pmx.server.constraints.eachIsPhoneNumber
import com.connexin.pmx.server.constraints.isPhoneNumber
import com.connexin.pmx.server.constraints.isEmail
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.Language
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.PmxMessage
import com.connexin.pmx.server.utils.InstantUtil
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.hasSize
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotEmpty
import org.valiktor.functions.isNotNull
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetTime
import org.valiktor.validate as _validate

@Schema(
    description = "Describes the parameters used when creating a new PMX Message."
)
data class CreateMessageRequest(
    /**
     * The ID of the customer sending the message.
     */
    @field:Schema(
        description = "The ID of the customer sending the message.",
        hidden = true
    )
    val customerId: String,
    @field:Schema(
        description = "The customer instance to work on",
        hidden = true
    )
    val customer: Customer? = null,
    /**
     * The type of message.
     */
    @field:Schema(
        description = "TThe type of message."
    )
    val type: MessageType,
    /**
     * The set of recipients (email address or phone number) that will receive the message.
     */
    @field:Schema(
        description = "The set of recipients (email address or phone number) that will receive the message."
    )
    val to: Set<String>,
    /**
     * The message text to send.
     */
    @field:Schema(
        description = "The message text to send."
    )
    val message: String,
    /**
     * The alternative message to send. Currently, this is the message to be played to the recipient's voice mail if type = VOICE.
     */
    @field:Schema(
        description = "The alternative message to send. Currently, this is the message to be played to the recipient's voice mail if type = VOICE."
    )
    val altMessage: String? = null,
    /**
     * The subject of the message, used by type = EMAIL and EMAIL_BROADCAST
     */
    @field:Schema(
        description = "The subject of the message, used by type = EMAIL and EMAIL_BROADCAST"
    )
    val subject: String? = null,
    /**
     * The phone number or email address the recipient can reply to. Used by EMAIL and VOICE.
     */
    @field:Schema(
        description = "The phone number or email address the recipient can reply to. Used by EMAIL and VOICE."
    )
    val replyTo: String? = null,
    /**
     * The effective date/time (UTC) after which the message can be sent. If left null, it will schedule the message to be sent ASAP.
     */
    @field:Schema(
        description = "The effective date/time (UTC) after which the message can be sent. If left null, it will schedule the message to be sent ASAP."
    )
    val sendAfter: Instant? = null,
    /**
     * Defines an opening of a window during the day within which the message can be sent.
     * If specified, sendUntil must also be provided.
     */
    @field:Schema(
        description = "Defines an opening of a window during the day within which the message can be sent. If specified, sendUntil must also be provided."
    )
    val sendFrom: OffsetTime? = null,
    /**
     * Defines a closing of a window during the day within which the message can be sent.
     * If specified, sendFrom must also be provided. Must be later than sendFrom.
     */
    @field:Schema(
        description = "Defines a closing of a window during the day within which the message can be sent. If specified, sendFrom must also be provided. Must be later than sendFrom."
    )
    val sendUntil: OffsetTime? = null,
    /**
     * The ID of the engagement that triggered this message, or null for legacy PMX messages.
     */
    @field:Schema(
        description = "The ID of the engagement that triggered this message, or null for legacy PMX messages."
    )
    val engagementId: String? = null,
    /**
     * The ID of the engagement rule that triggered this message, or null for legacy PMX messages.
     */
    @field:Schema(
        description = "The ID of the engagement rule that triggered this message, or null for legacy PMX messages."
    )
    val engagementRuleId: String? = null,
    /**
     * Instructions/menu option text to play for PMX+ voice messages
     */
    @field:Schema(
        description = "Instructions/menu option text to play for PMX+ voice messages"
    )
    val instructions: String? = null,
    @field:Schema(
        description = "The preferred language of the recipient."
    )
    val language: Language = Language.ENGLISH
) {
    fun validate() {
        _validate(this) {
            validate(CreateMessageRequest::customerId).isNotBlank()
            validate(CreateMessageRequest::message).isNotBlank()
            validate(CreateMessageRequest::to).isNotEmpty()

            if (sendFrom != null) {
                validate(CreateMessageRequest::sendUntil).isNotNull()
            }

            if (sendUntil != null) {
                validate(CreateMessageRequest::sendFrom).isNotNull()
            }

            when (type) {
                MessageType.SMS, MessageType.VOICE -> {
                    validate(CreateMessageRequest::to).hasSize(min = 1, max = 1).eachIsPhoneNumber()
                    validate(CreateMessageRequest::replyTo).isPhoneNumber()
                }
                MessageType.EMAIL -> {
                    validate(CreateMessageRequest::to).hasSize(min = 1, max = 1).eachIsEmail()
                    validate(CreateMessageRequest::replyTo).isEmail()
                }
                MessageType.EMAIL_BROADCAST -> {
                    validate(CreateMessageRequest::replyTo).isEmail()
                }
            }

            if (!engagementId.isNullOrEmpty()) {
                validate(CreateMessageRequest::engagementRuleId).isNotBlank()
            }
        }
    }

    fun getSendWindow(): PmxMessage.SendWindow? {
        if (this.sendFrom == null || this.sendUntil == null) {
            return null
        }

        var date = LocalDate.of(2022, 1, 1)
        if (this.sendUntil < this.sendFrom) {
            date = date.plusDays(1)
        }

        return PmxMessage.SendWindow(
            from = InstantUtil.toEpochTime(this.sendFrom),
            until = InstantUtil.toEpochTime(this.sendUntil, date)
        )
    }
}