package com.connexin.pmx.server.models

import java.io.Serializable
import java.time.DayOfWeek
import java.time.Instant
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

data class Schedule(val unit: ChronoUnit, val attempts: Set<Long>) : Serializable {
    val totalAttempts: Int get() = attempts.size

    fun next(now: ZonedDateTime, future: ZonedDateTime, days: Set<DayOfWeek>): Instant? {
        if (days.isEmpty()) {
            return null
        }

        val sorted = attempts.sortedDescending()

        for(attempt in sorted) {
            var next = future.minus(attempt, unit)

            // now, find the closest day to next that is a delivery day
            // opting for sooner than later because it would be better to get a notification
            // early than too late.
            while (!days.contains(next.dayOfWeek)) {
                next = next.minusDays(1)
            }

            if (next >= now) return next.plusSeconds(1).toInstant()
        }

        return null
    }

    companion object {
        private const val serialVersionUID: Long = 1

        val CONFIRMATION_DEFAULT = Schedule(unit = ChronoUnit.DAYS, attempts = setOf(5, 3, 1))
        val CONFIRMATION_TEST = Schedule(unit = ChronoUnit.MINUTES, attempts = setOf(5, 3, 1))
        val REMINDER_DEFAULT = Schedule(unit = ChronoUnit.DAYS, attempts = setOf(3, 1))
        val REMINDER_TEST = Schedule(unit = ChronoUnit.MINUTES, attempts = setOf(3, 1))
        val NONE = Schedule(unit = ChronoUnit.DAYS, attempts = emptySet())
    }
}