package com.connexin.pmx.server.constraints

import com.connexin.pmx.server.utils.EmailUtil
import com.connexin.pmx.server.utils.PhoneNumberUtil
import org.valiktor.Constraint
import org.valiktor.Validator

object IsPhoneNumber: Constraint

fun <E> Validator<E>.Property<String?>.isPhoneNumber() = this.validate(IsPhoneNumber) {
    it == null || PhoneNumberUtil.isValid(it)
}

object EachIsPhoneNumber: Constraint
fun <E> Validator<E>.Property<Iterable<String>?>.eachIsPhoneNumber() = this.validate(EachIsPhoneNumber) {
    it == null || it.all { value -> PhoneNumberUtil.isValid(value) }
}

object EachIsEmail: Constraint
fun <E> Validator<E>.Property<Iterable<String>?>.eachIsEmail() = this.validate(EachIsEmail) {
    it == null || it.all { value -> EmailUtil.isValid(value) }
}

object IsEmail: Constraint
fun <E> Validator<E>.Property<String?>.isEmail() = this.validate(IsEmail) {
    it == null || EmailUtil.isValid(it)
}