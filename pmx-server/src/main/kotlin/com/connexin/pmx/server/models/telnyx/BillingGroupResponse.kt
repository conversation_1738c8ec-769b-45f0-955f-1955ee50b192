package com.connexin.pmx.server.models.telnyx

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import java.time.OffsetDateTime

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder("data")
data class BillingGroupResponse(
    @JsonProperty("data")
    @JsonInclude(JsonInclude.Include.USE_DEFAULTS)
    val data: BillingGroupResult
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class BillingGroupResult(
        @JsonProperty("created_at")
        val createdAt: OffsetDateTime,
        @JsonProperty("deleted_at")
        val deletedAt: OffsetDateTime?,
        val id: String,
        val name: String,
        @JsonProperty("organization_id")
        val organizationId: String,
        @JsonProperty("record_type")
        val recordType: String,
        @JsonProperty("updated_at")
        val updatedAt: OffsetDateTime
    )
}
