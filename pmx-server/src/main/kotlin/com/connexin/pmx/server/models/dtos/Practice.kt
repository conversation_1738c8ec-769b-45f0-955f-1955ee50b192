package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.PracticeResource
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "A practice."
)
data class Practice(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The practice's ID in the customer system."
    )
    val id: String,
    @Schema(
        description = "The practice's name as it should be displayed."
    )
    val name: String
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(Practice::id).isNotBlank()
            validate(Practice::customerId).isNotBlank()
            validate(Practice::name).isNotBlank()
        }
    }

    companion object {
        fun from(resource: PracticeResource?): Practice? {
            if (resource == null || resource.id.isBlank()) return null
            return Practice(
                id = resource.id,
                name = resource.name
            )
        }
    }
}