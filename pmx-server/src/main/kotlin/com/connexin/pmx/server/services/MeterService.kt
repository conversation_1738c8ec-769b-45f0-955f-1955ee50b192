package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.MessageType
import io.micrometer.core.instrument.Timer

/**
 * A service that provides meters for recording metrics.
 */
interface MeterService {
    /**
     * Returns the timer for metering initiate calls on the dispatcher.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    fun getDispatchInitiateTimer(customerId: String, type: MessageType, success: Boolean): Timer

    /**
     * Returns the timer for metering respond calls on the dispatcher.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    fun getDispatchRespondTimer(customerId: String, type: MessageType, success: Boolean): Timer

    /**
     * Starts a timer on the registry and returns a Sample
     * @return A sample which can be used to record duration for a Timer.
     */
    fun startTimer(): Timer.Sample

    /**
     * Returns a timer for metering the total time it takes to dispatch a batch of scheduled messages.
     * @return The timer
     */
    fun getDispatchScheduledTimer(): Timer

    /**
     * Returns the timer for metering respond calls to create new messages.
     * @param customerId The customer's OPMED ID
     * @param type The message's type
     * @param success Whether the call was successful or not
     * @return The timer
     */
    fun getCreateMessageTimer(customerId: String, type: MessageType, success: Boolean): Timer

    /**
     * Returns a timer for metering how long it takes to update a message.
     * @return The timer
     */
    fun getMessageUpdateTimer(): Timer

    /**
     * Returns a timer for metering how long it takes to find queued messages.
     * @return The timer
     */
    fun getFindQueuedTimer(): Timer

    /**
     * Returns a timer for metering how long it takes to find queued provisioning orders.
     */
    fun getFindProvisioningQueuedTimer(): Timer

    /**
     * Returns a timer for metering how long it takes to find the most recent delivered message by message type.
     */
    fun getFindMostRecentDeliveredTimer(): Timer

}