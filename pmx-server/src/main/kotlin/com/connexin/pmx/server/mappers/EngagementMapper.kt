package com.connexin.pmx.server.mappers

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.ErrorResponse
import com.connexin.pmx.server.models.Response
import com.connexin.pmx.server.models.dtos.*
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.SubclassExhaustiveStrategy
import org.mapstruct.SubclassMapping

@Mapper(componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
abstract class EngagementMapper {

    @SubclassMapping(source = ErrorResponse::class, target = ErrorResponseDto::class)
    @SubclassMapping(source = ConfirmationResponse::class, target = ConfirmationResponseDto::class)
    @SubclassMapping(source = CancellationResponse::class, target = CancellationResponseDto::class)
    @SubclassMapping(source = CheckInResponse::class, target = CheckInResponseDto::class)
    @SubclassMapping(source = CompleteResponse::class, target = CompleteResponseDto::class)
    @SubclassMapping(source = MessageResponse::class, target = MessageResponseDto::class)
    abstract fun mapToDto(model: Response): ResponseDto

    @Mappings(
        Mapping(target = "isFinal", source = "final"),
        Mapping(target = "cancellationStatus", source = "result")
    )
    abstract fun mapToDto(model: CancellationResponse): CancellationResponseDto

    @Mappings(
        Mapping(target = "isFinal", source = "final"),
        Mapping(target = "confirmationStatus", source = "result")
    )
    abstract fun mapToDto(model: ConfirmationResponse): ConfirmationResponseDto

    @Mappings(
        Mapping(target = "isFinal", source = "final"),
        Mapping(target = "checkInStatus", source = "result")
    )
    abstract fun mapToDto(model: CheckInResponse): CheckInResponseDto

    @Mappings(
        Mapping(target="isFinal", source="final")
    )
    abstract fun mapToDto(model: CompleteResponse): CompleteResponseDto

    @Mappings(
        Mapping(target="isFinal", source="final"),
    )
    abstract fun mapToDto(model: MessageResponse): MessageResponseDto
}