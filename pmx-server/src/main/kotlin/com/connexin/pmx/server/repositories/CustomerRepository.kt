package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.utils.CacheKeys
import org.springframework.cache.annotation.*
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant
import java.util.*

interface CustomerRepository : MongoRepository<Customer, String>,
    CustomizedCustomerRepository<Customer, String> {
    fun findByLegacyUsername(username: String): Optional<Customer>
}

interface CustomizedCustomerRepository<T, ID> {
    fun <S : T?> save(entity: S): S
}

@Repository
@CacheConfig(
    cacheNames = [CacheKeys.customers]
)
class CustomizedCustomerRepositoryImpl(private val mongoTemplate: MongoTemplate) :
    CustomizedCustomerRepository<Customer, String> {
    @Caching(
        put = [
            CachePut(
                key = "#entity.id",
                condition = "#entity != null",
                unless = "#result == null"
            )
        ],
        evict = [
            CacheEvict(
                value = [CacheKeys.customersByCredentials],
                allEntries = true
            )
        ]
    )
    override fun <S : Customer?> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        entity!!.createdAt = entity.createdAt ?: Instant.now()
        entity.updatedAt = Instant.now()

        return mongoTemplate.save(entity)
    }

}