package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateOrEditTemplateRequest
import com.connexin.pmx.server.models.dtos.Response
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface TemplateService {
    fun findAll(pageable: Pageable): Page<Template>

    fun getById(id: String): Template?
    fun create(request: CreateOrEditTemplateRequest): Response<Template>
    fun update(id: String, request: CreateOrEditTemplateRequest): Response<Template>

    fun delete(id: String)
    fun buildSubstitutions(engagement: Engagement, languages: Set<Language>, customer: Customer? = null): Map<Language, MutableMap<String, String>>
    fun generateMessageSegments(
        rule: EngagementRule,
        contact: ContactResource,
        substitutions: Map<String, String>,
        scenario: TemplateScenario = TemplateScenario.DEFAULT
    ): MessageSegments?

    fun preview(id: String): Map<MessageType, Map<Language, MessageSegments?>>?
}