package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.ConfirmationResponse
import com.connexin.pmx.server.models.ConfirmationStatus
import com.connexin.pmx.server.models.Response
import com.connexin.pmx.server.repositories.ResponseRepository
import com.connexin.pmx.server.services.ResponseService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.Objects

@Service
class ResponseServiceImpl(
    private val repository: ResponseRepository
): ResponseService {
    override fun <R : Response> create(entity: R): R {
        log.debug("Creating response (type={})", entity.type)

        val result = if (entity is ConfirmationResponse && ConfirmationStatus.DECLINED.equals(entity.result)) {
            dedupeDeclinedResponse(entity)
        } else {
            repository.save(entity)
        }

        log.debug("Created response {} (type={})", result.id, result.type)
        return result
    }

    private fun <R : Response> dedupeDeclinedResponse(entity: R): R {

        synchronized(this) {
            val result = repository.findDeclinedResponse(entity)
            if (Objects.isNull(result.id)) {
                return repository.save(entity)
            }
            return result;
        }

    }

    companion object {
        private val log = LoggerFactory.getLogger(ResponseServiceImpl::class.java)
    }
}