package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotEmpty
import org.valiktor.functions.validate
import org.valiktor.functions.validateForEach

@Schema(
    description = "Describes a new engagement record that will be created."
)
data class UpdateEngagementRequest(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The ID of the engagement.",
        hidden = true
    )
    val id: String? = null,
    @field:Schema(
        description = "A list of appointments associated with the engagement."
    )
    val appointments: List<Appointment>,
    @field:Schema(
        description = "A list of patient contacts that will be participants in the engagement."
    )
    val contacts: List<Contact>
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(UpdateEngagementRequest::id).isNotBlank()
            validate(UpdateEngagementRequest::customerId).isNotBlank()
            validate(UpdateEngagementRequest::appointments).isNotEmpty()
                .validateForEach {
                    it.validate()
                }
        }
    }
}