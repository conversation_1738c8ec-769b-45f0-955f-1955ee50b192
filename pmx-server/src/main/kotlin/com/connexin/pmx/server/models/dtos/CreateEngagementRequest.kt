package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import org.slf4j.LoggerFactory
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotEmpty
import org.valiktor.functions.validateForEach
import kotlin.system.measureTimeMillis

@Schema(
    description = "Describes a new engagement record that will be created."
)
data class CreateEngagementRequest(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "A list of appointments associated with the engagement."
    )
    val appointments: List<Appointment>,
    @field:Schema(
        description = "A list of patient contacts that will be participants in the engagement."
    )
    val contacts: List<Contact>
) {
    fun validate() {
        val time = measureTimeMillis {
            org.valiktor.validate(this) {
                validate(CreateEngagementRequest::customerId).isNotBlank()
                validate(CreateEngagementRequest::appointments).isNotEmpty()
                    .validateForEach {
                        it.validate()
                    }
                validate(CreateEngagementRequest::contacts).validateForEach {
                    it.validate()
                }
            }
        }
        log.trace("CreateEngagementRequest.validate: took {} ms", time)
    }

    companion object {
        private val log = LoggerFactory.getLogger(CreateEngagementRequest::class.java)
    }
}