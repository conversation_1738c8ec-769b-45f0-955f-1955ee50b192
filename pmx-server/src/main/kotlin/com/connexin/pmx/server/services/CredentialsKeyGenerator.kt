package com.connexin.pmx.server.services

import com.connexin.pmx.server.utils.Base64Util
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.cache.interceptor.KeyGenerator
import org.springframework.stereotype.Component
import java.lang.reflect.Method
import java.security.MessageDigest

@Component
@Qualifier("credentialsKeyGenerator")
class CredentialsKeyGenerator: KeyGenerator {
    private val digest = MessageDigest.getInstance("SHA3-256");

    override fun generate(target: Any, method: Method, vararg params: Any?): Any {
        val hash = digest.digest(params.joinToString(separator = ":").toByteArray())
        return Base64Util.encode(hash)
    }
}