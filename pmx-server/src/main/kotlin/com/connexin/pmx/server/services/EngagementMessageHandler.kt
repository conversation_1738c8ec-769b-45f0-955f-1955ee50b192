package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*

interface EngagementMessageHandler {
    fun send(
        engagement: Engagement,
        rule: EngagementRule,
        customer: Customer,
        contacts: Set<ContactResource> = engagement.resources.filterIsInstance<ContactResource>().toSet(),
        appointments: Set<AppointmentResource> = emptySet(),
        scenario: TemplateScenario = TemplateScenario.DEFAULT,
        rules: Map<EngagementWorkflow, EngagementRule>
    ): EngagementMessageResult
}