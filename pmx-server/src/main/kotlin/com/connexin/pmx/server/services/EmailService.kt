package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.BulkEmailMessage
import com.connexin.pmx.server.models.EmailMessage
import java.time.Duration

interface EmailService {
    /**
     * Sends a standard email message.
     * @param email Represents a request to send a single email message.
     * @return The result of performing the operation.
     */
    fun sendEmail(email: EmailMessage, tags: Map<String, String>? = null): SendEmailResult

    /**
     * Composes an email message to multiple destinations.
     * @param email Represents a request to send email messages to multiple destinations.
     * @param onBatchSent A callback that is invoked after each batch of email entries is sent.
     * @return The result of performing the operation.
     */
    fun sendBulkEmail(
        email: BulkEmailMessage,
        tags: Map<String, String>? = null,
        onBatchSent: ((List<SendBulkEmailResult.EntryResult>) -> Unit)? = null
    ): SendBulkEmailResult
}

data class SendEmailResult(
    val success: Boolean,
    val messageId: String? = null,
    val error: String? = null,
    val retry: Boolean? = null,
    val retryDelay: Duration? = null
)

data class SendBulkEmailResult(
    val success: Boolean,
    val error: String? = null,
    val entryResults: List<EntryResult> = listOf()
) {
    data class EntryResult(
        val success: Boolean,
        val toAddress: String,
        val messageId: String? = null,
        val error: String? = null,
        val retry: Boolean? = null,
        val retryDelay: Duration? = null
    )
}