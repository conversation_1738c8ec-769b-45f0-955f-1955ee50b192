package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.services.DateService
import com.connexin.pmx.server.web.RestHttpClient
import org.json.JSONObject
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.ZoneId
import java.util.concurrent.ConcurrentHashMap

@Service
class DateServiceImpl(
    @Value("\${op.pmx.zipcode-url}")
    private val zipCodeInfoUrl: String,
    private val restHttpClient: RestHttpClient
): DateService {
    private val cache = ConcurrentHashMap<String, ZoneId>()
    override fun getZoneIdForLocation(zip: String?): ZoneId? {
        return when {
            zip == null -> null
            cache.containsKey(zip) -> cache[zip]
            zip == LAST_ZIP_CODE -> {
                val zoneId = ZoneId.of(DEFAULT_ZIP_ZONE)
                cache[LAST_ZIP_CODE] = zoneId
                zoneId
            }
            else -> {
                val response = restHttpClient.getAsString("$zipCodeInfoUrl/$zip", hashMapOf())
                if (response.status == 200 && response.body != null) {
                    val timezone = JSONObject(response.body).getString(TIMEZONE_FIELD_NAME)
                    val zoneId = ZoneId.of(timezone)
                    cache[zip] = zoneId
                    zoneId
                } else {
                    null
                }
            }
        }
    }


    companion object {
        private val log = LoggerFactory.getLogger(DateServiceImpl::class.java)

        private const val DEFAULT_ZIP_ZONE = "America/New_York"
        private const val LAST_ZIP_CODE = "99999"
        private const val TIMEZONE_FIELD_NAME = "timezone"
    }
}