package com.connexin.pmx.server.models

object MdcKeys {
    const val MDC_MESSAGE_ID = "messageId"
    const val MDC_CUSTOMER_ID = "customerId"
    const val MDC_REMOTE_ID = "remoteId"
    const val MDC_ENGAGEMENT_ID = "engagementId"
    const val MDC_CORRELATION_ID = "correlationId"
    const val MDC_SCHEDULE_CORRELATION_ID = "scheduleCorrelationId"
    const val MDC_APPOINTMENT_ID = "appointmentId"
    const val MDC_APPOINTMENT_IDS = "appointmentIds"
    const val MDC_ENGAGEMENT_WORKFLOW = "engagementWorkflow"
    const val MDC_ENGAGEMENT_EVENT = "engagementEvent"
    const val MDC_ENGAGEMENT_RULE_ID = "engagementRuleId"
    const val MDC_PATIENT_ID = "patientId"
    const val MDC_CONTACT_ID = "contactId"
    const val MDC_PROVISIONING_ORDER_ID = "provisioningOrderId"
    const val MDC_ORIGINAL_URL = "surveyOriginalUrl"
    const val MDC_SURVEY_GUID = "surveyGuid"
    const val MDC_SURVEY_QUEUE_ID = "surveyQueueId"
}