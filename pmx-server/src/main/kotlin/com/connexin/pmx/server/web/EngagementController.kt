package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.ConflictException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementEventHandler
import com.connexin.pmx.server.services.EngagementService
import com.connexin.pmx.server.utils.use
import com.github.fge.jsonpatch.JsonPatch
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v2/engagements")
@Tag(name = "Engagement", description = "Endpoints for managing patient engagements.")
class EngagementController(
    private val customerService: CustomerService,
    private val engagementService: EngagementService,
    private val engagementEventHandler: EngagementEventHandler
) {
    @Operation(
        summary = "Creates a new engagement.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "New engagement was created."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "409",
                description = "An engagement for the specified appointment already exists.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PostMapping(
        "",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
//    @PreAuthorize("hasAuthority('admin.engagements:write')")
    fun create(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: CreateEngagementRequest
    ): EngagementDto {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                log.trace("Received CREATE Engagement request: {}", request)

                val res = engagementService.create(request.copy(customerId = customerId))

                return if (res.success) EngagementDto.from(res.get())
                else if (res.status == HttpStatus.CONFLICT) throw ConflictException(
                    errors = res.errors,
                    message =res.errors?.map { it.message }?.firstOrNull() ?: "An engagement for the specified appointment already exists."
                )
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not create engagement."
                )
            }
    }

    @Operation(
        summary = "Updates an existing engagement.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Existing engagement was updated."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PutMapping(
        "/{id}",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
//    @PreAuthorize("hasAuthority('admin.engagements:write')")
    fun update(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable id: String,
        @RequestBody request: UpdateEngagementRequest
    ): EngagementDto {
        log.info("PUT: /api/v2/engagements/{} Update engagement request for customer with id {}", id, customerId);
        log.debug("Request body: {}", request)
        log.debug("Path variable id: {}", id)
        log.debug("Customer id: {}", customerId)
        request.appointments.forEach { appointment ->
            log.debug("Appointment[id=${appointment.id}, staffId=${appointment.staff?.id}, staffName=${appointment.staff?.name}, staffCustomerId=${appointment.staff?.customerId}]")
        }
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, id)
        )
            .use {
                log.trace("Received UPDATE Engagement request: {}", request)
                val res = engagementService.update(request.copy(customerId = customerId, id = id))

                return if (res.success) EngagementDto.from(res.get())
                else if (res.status == HttpStatus.NOT_FOUND) throw NotFoundException(
                    errors = res.errors,
                    message = NOT_FOUND
                )
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update engagement."
                )
            }
    }

    @Operation(
        summary = "Patches an existing engagement.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Existing engagement was patched."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/{id}",
        consumes = ["application/json-patch+json"],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
//    @PreAuthorize("hasAuthority('admin.engagements:write')")
    fun patch(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable id: String,
        @RequestBody
        @Parameter(
            description = "An RFC 6902 JSON Patch payload describing the changes to the engagement that you wish to make."
        )
        patch: JsonPatch
    ): EngagementDto {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, id)
        )
            .use {
                log.trace("Received PATCH Engagement request: {}", patch)
                validateCustomer(customerId)

                val existing = engagementService.getById(id)
                return if (existing != null) {
                    val patchable = PatchEngagement.from(existing)
                    val patched = engagementService.patch(existing, patchable, patch)
                    EngagementDto.from(patched)
                } else throw NotFoundException(message = NOT_FOUND)
            }
    }

    @Operation(
        summary = "Gets an engagement record.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The engagement record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
//    @PreAuthorize("hasAuthority('admin.engagements:read')")
    fun getById(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable id: String
    ): EngagementDto {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, id)
        )
            .use {
                validateCustomer(customerId)

                val engagement = engagementService.getById(id)

                return if (engagement != null) EngagementDto.from(engagement)
                else throw NotFoundException(message = NOT_FOUND)
            }
    }

    @Operation(
        summary = "Gets an engagement record for an appointment.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The engagement record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getByAppointmentId(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestParam appointmentId: String
    ): EngagementDto {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_APPOINTMENT_ID, appointmentId)
        )
            .use {
                val engagement = engagementService.getByAppointmentId(customerId, appointmentId)

                return if (engagement != null) EngagementDto.from(engagement)
                else throw NotFoundException(message = NOT_FOUND)
            }
    }

    @Operation(
        summary = "Deletes an existing engagement.",
        responses = [
            ApiResponse(
                responseCode = "204",
                description = "Existing engagement was deleted."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @DeleteMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
//    @PreAuthorize("hasAuthority('admin.engagements:write')")
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    fun delete(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable id: String
    ) {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, id)
        )
            .use {
                validateCustomer(customerId)

                val res = engagementService.deleteById(id)

                if (!res.success) throw NotFoundException(
                    errors = res.errors,
                    message = NOT_FOUND
                )
            }
    }

    @Operation(
        summary = "Triggers an engagement event.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Engagement event triggered"
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement event was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PostMapping(
        "/advance-engagement",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun advanceEngagement(
        @RequestHeader(X_OPMED, required = true) opmedId: String,
        @RequestBody request: EngagementEventDto
    ): Response<String?> {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, opmedId)
            .use {
                try {
                    log.trace("Received ADVANCE Engagement request: {}", request)
                    val engagementId = engagementEventHandler.handle(request, opmedId).get() ?: return Response.failure(status = HttpStatus.NOT_FOUND)

                    return Response.success(engagementId)
                } catch (e: Exception) {
                    log.error("Could not advance engagement event due to: {}", e.message)
                    return Response.failure(status = HttpStatus.INTERNAL_SERVER_ERROR)
                }
            }
    }

    private fun validateCustomer(customerId: String) {
        val customer = customerService.getById(customerId)
        if (customer == null || customer.status != CustomerStatus.ENABLED) {
            log.warn("Customer {} was not found or not enabled", customerId)
            throw NotFoundException(message = "Customer not found or disabled")
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementController::class.java)

        private const val NOT_FOUND = "Engagement not found."
    }
}