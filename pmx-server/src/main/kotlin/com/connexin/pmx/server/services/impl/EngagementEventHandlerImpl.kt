package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.EngagementEventDto
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.use
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Future

@Service
class EngagementEventHandlerImpl(
    private val customerService: CustomerService,
    private val engagementService: EngagementService
) : EngagementEventHandler {
    override fun handle(engagementEventDto: EngagementEventDto, opmedId: String): Future<String?> {
        val customer = customerService.getById(opmedId)

        if (customer == null || customer.status != CustomerStatus.ENABLED) {
            log.error("Customer not found or not enabled")
            return CompletableFuture.completedFuture(null)
        }
        val engagement = engagementService.getByAppointmentId(opmedId, engagementEventDto.appointmentId)

        if (engagement == null) {
            log.error("Engagement with appointment id: {} not found.", engagementEventDto.appointmentId)
            return CompletableFuture.completedFuture(null)
        }

        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, engagement.id),
            MDC.putCloseable(MdcKeys.MDC_APPOINTMENT_ID, engagementEventDto.appointmentId)
        ).use {
            try {
                when (engagementEventDto.engagementEventType) {
                    EngagementEvent.CHECKED_IN -> handleAppointmentCheckedInEvent(engagement, customer)
                    else -> {}
                }
            } catch (e: NotFoundException) {
                log.error("Appointment not found within engagement")
                return CompletableFuture.completedFuture(null)
            }
        }

        return CompletableFuture.completedFuture(engagement.id)
    }

    fun handleAppointmentCheckedInEvent(engagement: Engagement, customer: Customer) {
        log.info("Handling {} advancement for engagement", EngagementEvent.CHECKED_IN)

        val contacts = engagement.resources.filterIsInstance<ContactResource>().toSet()
        val appointment = engagement.getAppointment() ?: throw NotFoundException(message = "No appointment found for engagement")

        engagementService.sendEvent(
            CheckInResponseEvent(
                engagement,
                customer,
                contacts,
                setOf(appointment)
            )
        )

        log.info("Sent CheckInResponseEvent for engagement, total number of contacts: {}", contacts.size)
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementEventHandlerImpl::class.java)
    }
}