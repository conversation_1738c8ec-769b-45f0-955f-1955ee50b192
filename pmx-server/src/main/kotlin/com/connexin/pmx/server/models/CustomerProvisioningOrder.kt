package com.connexin.pmx.server.models

import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

/**
 * Describes the status of a provisioning order.
 */
enum class ProvisioningStatus {
    /**
     * Provisioning is in progress.
     */
    IN_PROGRESS,

    /**
     * Provisioning has completed successfully.
     */
    COMPLETED,

    /**
     * Provisioning has failed.
     */
    FAILED
}

/**
 * Describes the steps taken to provision a new customer.
 */
enum class NewCustomerProvisioningStep {
    /**
     * Creating the Telnyx Billing Group.
     */
    BILLING_GROUP,

    /**
     * Creating the Telnyx outbound voice profile.
     */
    OUTBOUND_VOICE_PROFILE,

    /**
     * Creating the Telnyx messaging profile.
     */
    MESSAGING_PROFILE,

    /**
     * Creating the Telnyx call control application.
     */
    CALL_CONTROL_CONNECTION,

    /**
     * Ordering a number from Telnyx.
     */
    ORDER_NUMBER,

    /**
     * Checking the number order statuses.
     */
    UPDATE_NUMBER_ORDER_STATUS,

    /**
     * Completing the order.
     */
    COMPLETE
}

/**
 * A base type for provisioning orders.
 */
@Document("customer_provisioning_orders")
@CompoundIndexes(
    value = [
        CompoundIndex(
            name = "pending_orders",
            def = "{'status': 1, 'createdAt': 1, 'attempts': 1}",
            unique = false
        )
    ]
)
abstract class CustomerProvisioningOrder(
    /**
     * The ID of the order.
     */
    @Id var id: String? = null,
    /**
     * Number of provisioning failure attempts until the customer is ignored.
     */
    var attempts: Int = 0,
    /**
     * Last time the order was updated.
     */
    var updatedAt: Instant? = null,
    /**
     * When the order was created.
     */
    var createdAt: Instant? = null,
    /**
     * Status of the order.
     */
    var status: ProvisioningStatus = ProvisioningStatus.IN_PROGRESS,
    /**
     * Journal of activity related to processing the order.
     */
    var activity: MutableList<Activity> = mutableListOf()
) {
    /**
     * Describes a moment of activity for an order.
     */
    data class Activity(
        /**
         * Describes the activity.
         */
        val description: String,
        /**
         * Whether the activity resulted in an error or not.
         */
        val error: Boolean = false,
        /**
         * When the activity occurred.
         */
        val occurredAt: Instant = Instant.now(),
    )

    /**
     * Adds a new activity item to the order.
     * @param description Description of the activity
     * @param error Whether the activity resulted in an error or not.
     * @param occurredAt When the activity occurred.
     * @return True if the activity was added; otherwise, false.
     */
    fun addActivity(description: String, error: Boolean = false, occurredAt: Instant = Instant.now()): Boolean {
        return activity.add(
            Activity(
                description = description,
                error = error,
                occurredAt = occurredAt
            )
        )
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CustomerProvisioningOrder

        if (id != other.id) return false
        if (attempts != other.attempts) return false
        if (updatedAt != other.updatedAt) return false
        if (createdAt != other.createdAt) return false
        if (status != other.status) return false
        if (activity != other.activity) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id?.hashCode() ?: 0
        result = 31 * result + attempts
        result = 31 * result + (updatedAt?.hashCode() ?: 0)
        result = 31 * result + (createdAt?.hashCode() ?: 0)
        result = 31 * result + status.hashCode()
        result = 31 * result + activity.hashCode()
        return result
    }

    override fun toString(): String {
        return "CustomerProvisioningOrder(id=$id, attempts=$attempts, updatedAt=$updatedAt, createdAt=$createdAt, status=$status, activity=$activity)"
    }
}

/**
 * A provisioning order that creates a new customer and completes all vendor provisioning steps.
 */
@Document("customer_provisioning_orders")
class NewCustomerProvisioningOrder(
    id: String? = null,
    var customerId: String,
    attempts: Int = 0,
    updatedAt: Instant? = null,
    createdAt: Instant? = null,
    status: ProvisioningStatus = ProvisioningStatus.IN_PROGRESS,
    var step: NewCustomerProvisioningStep = NewCustomerProvisioningStep.BILLING_GROUP
): CustomerProvisioningOrder(
    id = id,
    attempts = attempts,
    updatedAt = updatedAt,
    createdAt = createdAt,
    status = status
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as NewCustomerProvisioningOrder

        if (customerId != other.customerId) return false
        if (step != other.step) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + customerId.hashCode()
        result = 31 * result + step.hashCode()
        return result
    }

    override fun toString(): String {
        return "NewCustomerProvisioningOrder(step=$step) ${super.toString()}"
    }
}

@Document("customer_provisioning_orders")
class CsvMultiCustomerProvisioningOrder(
    id: String? = null,
    attempts: Int = 0,
    updatedAt: Instant? = null,
    createdAt: Instant? = null,
    status: ProvisioningStatus = ProvisioningStatus.IN_PROGRESS,
    @JsonIgnore
    var csvContent: String
): CustomerProvisioningOrder(
    id = id,
    attempts = attempts,
    updatedAt = updatedAt,
    createdAt = createdAt,
    status = status
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as CsvMultiCustomerProvisioningOrder

        if (csvContent != other.csvContent) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + csvContent.hashCode()
        return result
    }

    override fun toString(): String {
        return "CsvMultiCustomerProvisioningOrder(csvContent=[omitted]) ${super.toString()}"
    }
}
