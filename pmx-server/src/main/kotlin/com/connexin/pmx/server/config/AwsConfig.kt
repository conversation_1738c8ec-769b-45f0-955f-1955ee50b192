package com.connexin.pmx.server.config

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.services.simpleemailv2.AmazonSimpleEmailServiceV2
import com.amazonaws.services.simpleemailv2.AmazonSimpleEmailServiceV2ClientBuilder
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.services.impl.SesEmailMessageDispatcherImpl
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/** Configures the access to AWS SES with the required credentials */
@ConstructorBinding
@ConfigurationProperties(prefix = "op.pmx.aws")
data class AwsConfig(
    val accessKey: String,
    val secretKey: String,
    val region: String,
    val ses: SesConfig
) {

    data class SesConfig(
        val configurationSet: String
    )
}

@Configuration
@ConditionalOnProperty(
    "op.pmx.email-vendor",
    havingValue = "ses",
    matchIfMissing = false
)
@EnableConfigurationProperties(AwsConfig::class)
class AmazonSesConfig {

    @Autowired
    private lateinit var awsConfig: AwsConfig

    @Bean
    fun simpleEmailService(): AmazonSimpleEmailServiceV2 {
        return AmazonSimpleEmailServiceV2ClientBuilder.standard()
            .withCredentials(
                AWSStaticCredentialsProvider(
                    BasicAWSCredentials(
                        awsConfig.accessKey,
                        awsConfig.secretKey
                    )
                )
            )
            .withRegion(awsConfig.region)
            .build()
    }

    @Bean(name = ["sesConfigSetName"])
    fun configSetName(): String {
        return awsConfig.ses.configurationSet
    }

    @Bean
    fun emailMessageDispatcher(
        emailService: EmailService,
        objectMapper: ObjectMapper,
        subscriptionService: SubscriptionService,
        urls: UrlGenerator,
        pmxMessageService: PmxMessageService,
        engagementService: EngagementService
    ): SesEmailMessageDispatcherImpl {
        return SesEmailMessageDispatcherImpl(
            emailService,
            objectMapper,
            subscriptionService,
            urls,
            pmxMessageService
        )
    }
}