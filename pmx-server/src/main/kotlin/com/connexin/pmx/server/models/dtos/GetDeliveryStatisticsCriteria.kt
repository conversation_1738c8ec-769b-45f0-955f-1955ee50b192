package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import java.time.Instant

data class GetDeliveryStatisticsCriteria(
    val from: Instant,
    val until: Instant,
    val statuses: Array<MessageStatus>? = null,
    val types: Array<MessageType>? = null
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GetDeliveryStatisticsCriteria

        if (from != other.from) return false
        if (until != other.until) return false
        if (statuses != null) {
            if (other.statuses == null) return false
            if (!statuses.contentEquals(other.statuses)) return false
        } else if (other.statuses != null) return false
        if (types != null) {
            if (other.types == null) return false
            if (!types.contentEquals(other.types)) return false
        } else if (other.types != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = from.hashCode()
        result = 31 * result + until.hashCode()
        result = 31 * result + (statuses?.contentHashCode() ?: 0)
        result = 31 * result + (types?.contentHashCode() ?: 0)
        return result
    }
}
