package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.config.TelnyxProperties
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.BeforeRespondResult
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.services.impl.TelnyxConstants.DELIVERED
import com.connexin.pmx.server.services.impl.TelnyxConstants.EVENT_ID
import com.connexin.pmx.server.services.impl.TelnyxConstants.EVENT_TYPE_PATH
import com.connexin.pmx.server.services.impl.TelnyxConstants.ID
import com.connexin.pmx.server.services.impl.TelnyxConstants.MESSAGE_FINALIZED
import com.connexin.pmx.server.services.impl.TelnyxConstants.MESSAGE_RECEIVED
import com.connexin.pmx.server.services.impl.TelnyxConstants.MESSAGE_SENT
import com.connexin.pmx.server.services.impl.TelnyxConstants.PAYLOAD_PATH
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.model.CreateMessageRequest
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.Instant

class TelnyxSmsMessageDispatcher(
    private val client: TelnyxService,
    private val mapper: ObjectMapper,
    private val urlGenerator: UrlGenerator,
    private val deduper: WebhookDeduper,
    private val telnyxEngagementProperties: TelnyxProperties.Engagement
): SmsMessageDispatcher {

    override fun initiate(message: PmxMessage, customer: Customer): InitiateResult {

        val language = message.language ?: Language.ENGLISH
        // create a message. if the message is associated with an engagement, use
        // the PMX+ engagement shared messaging profile; otherwise, use customer's legacy
        // messaging profile.
        val request = CreateMessageRequest()
            .messagingProfileId(
                when {
                    message.engagementId != null && language == Language.ENGLISH -> telnyxEngagementProperties.englishMessagingProfileId
                    message.engagementId != null && language == Language.SPANISH -> telnyxEngagementProperties.spanishMessagingProfileId
                    else -> customer.telnyxConfig.messagingProfileId
                }
            )
            .to(message.to)
            .text(message.message)
            .type(CreateMessageRequest.TypeEnum.SMS)
            .useProfileWebhooks(false)
            .webhookUrl(urlGenerator.pmxMessagingWebhook().toString())

        try {
            val remoteId = client.createMessage(request)
            log.info("Successfully dispatched message {}, remote ID {}", message.id, remoteId)
            return InitiateResult(success = true, remoteId = remoteId)
        } catch (ex: ApiException) {
            if (retryableStatusCodes.contains(ex.code)) {
                log.warn("Received a retryable status code {} when trying to send SMS message, retrying in 1 minute", ex.code)
                return InitiateResult(
                    success = false,
                    retry = true,
                    retryDelay = Duration.ofMinutes(1),
                    errors = ex.responseBody
                )
            }
            log.error("The Messaging API returned error {} when attempting to send an SMS to {} for message {}", ex.code, message.to, message.id, ex)
            return InitiateResult(success = false, errors = ex.responseBody)
        } catch (ex: Exception) {
            log.error("An unexpected error occurred trying to send an SMS message to Telnyx", ex)
            return InitiateResult(success = false, errors = ex.message)
        }
    }

    override fun respond(context: RespondContext): RespondResult {
        val root = context.decodedPayload!! as JsonNode
        val payloadNode = root.at("/data/payload")

        return when(val eventType = root.at("/data/event_type").asText("")) {
            MESSAGE_SENT -> {
                val message = context.message!!
                if (message.status == MessageStatus.FAILED || message.status == MessageStatus.DELIVERED) {
                    log.debug("Message sent webhook received, but status indicates it was already finalized")
                } else {
                    message.status = MessageStatus.SENT
                    log.info("Message {} has been sent by the vendor to the recipient.", message.id)
                }

                RespondResult(success = true)
            }
            MESSAGE_FINALIZED -> {
                val message = context.message!!
                // we should only be sending PMX messages to one number at a time, so there should be exactly one "to" entry
                when(payloadNode.get("to")?.first()?.get("status")?.asText()) {
                    DELIVERED -> {
                        message.status = MessageStatus.DELIVERED
                        message.completedAt = Instant.now()
                        log.info("Message {} has been delivered to the recipient", message.id)
                        RespondResult(success = true)
                    }
                    else -> {
                        message.status = MessageStatus.FAILED
                        message.errors = payloadNode.get("errors").toPrettyString()
                        message.completedAt = Instant.now()
                        log.warn(message.errors)

                        RespondResult(success = true)
                    }
                }
            }
            MESSAGE_RECEIVED -> handleMessageReceived(context)
            else -> throw IllegalArgumentException("Unknown or unsupported event type $eventType received from Telnyx.")
        }
    }

    override fun beforeRespond(context: RespondContext): BeforeRespondResult {

        val root = mapper.readTree(context.payload)
        val eventType = root.at(EVENT_TYPE_PATH).asText("")
        val eventId = root.at(EVENT_ID).asText()
        if (!eventId.isNullOrEmpty() && deduper.isDuplicate(eventId)) {
            log.debug("Ignoring duplicate webhook {}", eventId)
            return BeforeRespondResult(success = true, accepted = false, context)
        }

        if (!validEventTypes.contains(eventType)) {
            log.debug("Ignoring unknown webhook event type {}", eventType)
            return BeforeRespondResult(success = true, accepted = false, context)
        }

        val payloadNode = root.at(PAYLOAD_PATH)
        val remoteId = if (payloadNode.hasNonNull(ID)) payloadNode.get(ID).asText() else null

        return if (eventType == MESSAGE_RECEIVED) {
            val from = payloadNode.get("from")?.get("phone_number")?.asText()
            val text = payloadNode.get("text")?.asText()?.trim()
            log.debug("Received text \"{}\" from {}", text, from)

            val responseCategory = when(text?.lowercase()) {
                in SmsResponses.CONFIRM_WORDS -> RespondContext.ResponseCategory.CONFIRM
                in SmsResponses.CANCEL_WORDS -> RespondContext.ResponseCategory.DECLINE
                in SmsResponses.STOP_WORDS -> RespondContext.ResponseCategory.OPT_OUT
                in SmsResponses.RESTART_WORDS -> RespondContext.ResponseCategory.OPT_IN
                else -> RespondContext.ResponseCategory.UNKNOWN
            }

            BeforeRespondResult(
                success = true,
                accepted = !from.isNullOrEmpty() && responseCategory != RespondContext.ResponseCategory.UNKNOWN,
                context = context.copy(
                    decodedPayload = root,
                    responseCategory = responseCategory,
                    receivedMessage = if (from != null && text != null) {
                        RespondContext.ReceivedMessage(
                            from = from,
                            text = text
                        )
                    } else {
                        null
                    }
                )
            )
        } else {
            BeforeRespondResult(
                success = true,
                accepted = !remoteId.isNullOrBlank(),
                context = context.copy(decodedPayload = root, remoteId = remoteId)
            )
        }
    }

    /**
     * Updates the message confirmation status and adds the text (of the receiver response)
     * to the message's array responses
     * @param [context] the context of the receiver's response
     */
    private fun handleMessageReceived(context: RespondContext): RespondResult {
        val message = context.message!!
        val (from, text) = context.receivedMessage!!
        message.responses.add(PmxMessage.Response(text, Instant.now()))
        when (context.responseCategory) {
            RespondContext.ResponseCategory.CONFIRM -> {
                log.debug("Contact {} has confirmed appointment for message {}", from, message.id)
                message.confirmationStatus = ConfirmationStatus.CONFIRMED
            }
            RespondContext.ResponseCategory.DECLINE -> {
                log.debug("Contact {} has declined appointment for message {}", from, message.id)
                message.confirmationStatus = ConfirmationStatus.DECLINED
            }
            else -> {
                log.info("Contact {} responded {} to message {}", from, text, message.id)
            }
        }
        return RespondResult(success = true)
    }

    companion object {
        private val log = LoggerFactory.getLogger(TelnyxSmsMessageDispatcher::class.java)
        private val validEventTypes = setOf(MESSAGE_SENT, MESSAGE_FINALIZED, MESSAGE_RECEIVED)
        private val retryableStatusCodes = setOf(429, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511)
    }
}