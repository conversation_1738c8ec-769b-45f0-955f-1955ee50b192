package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerProvisioningOrder
import com.connexin.pmx.server.models.dtos.*

interface CustomerProvisioningService {
    fun provision(order: CustomerProvisioningOrder): ProvisionResponse
    fun create(request: CreateOrderRequest): CreateOrderResult
    fun create(request: CreateCsvOrderRequest): CreateOrderResult
    fun getById(id: String): CustomerProvisioningOrder?
    fun deleteById(id: String)
    fun deprovision(customerId: String): DeprovisionResult
    fun deprovisionV2(customerId: String , billingGroupId : String, outBoundVoiceProfileId: String) : DeprovisionResult
}