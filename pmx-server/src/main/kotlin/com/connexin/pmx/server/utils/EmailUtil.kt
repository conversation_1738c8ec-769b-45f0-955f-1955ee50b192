package com.connexin.pmx.server.utils

import com.sanctionco.jmail.JMail

class EmailUtil {
    companion object {
        private val validator = JMail.validator()
            .disallowIpDomain()
            .requireTopLevelDomain()
            .disallowReservedDomains()
            .disallowQuotedIdentifiers()

        fun isValid(email: String): Boolean {
            return validator.isValid(email)
        }
    }
}