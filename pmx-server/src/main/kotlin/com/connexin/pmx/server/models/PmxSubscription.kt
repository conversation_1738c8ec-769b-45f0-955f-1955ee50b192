package com.connexin.pmx.server.models

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document("subscription_preferences")
data class SubscriptionPreference(
    /**
     * The contact's address, such as an email or phone number.
     */
    @Id val id: String,
    /**
     * Whether the contact is subscribed to receiving messages.
     */
    var subscribed: <PERSON><PERSON><PERSON>,
    /**
     * When the record was created.
     */
    var createdAt: Instant? = null,
    /**
     * When the record was last updated.
     */
    var updatedAt: Instant? = null
)