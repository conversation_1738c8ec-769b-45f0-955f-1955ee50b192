package com.connexin.pmx.server.utils

import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil

class PhoneNumberUtil {
    companion object {
        private val phoneNumberUtil = PhoneNumberUtil.getInstance()

        fun canonicalize(number: String?): String? {
            if (number.isNullOrEmpty()) return number

            return try {
                val parsed = phoneNumberUtil.parse(number.trim(), "US")
                phoneNumberUtil.format(parsed, PhoneNumberUtil.PhoneNumberFormat.E164)
            } catch (ex: NumberParseException) {
                number
            }
        }

        fun format(number: String): String {
            return try {
                val parsed = phoneNumberUtil.parse(number.trim(), "US")
                phoneNumberUtil.format(parsed, PhoneNumberUtil.PhoneNumberFormat.NATIONAL)
            } catch (ex: NumberParseException) {
                number
            }
        }

        fun isValid(number: String): <PERSON><PERSON><PERSON> {
            return try {
                val parsed = phoneNumberUtil.parse(number.trim(), "US")
                phoneNumberUtil.isValidNumber(parsed)
            } catch (ex: NumberParseException) {
                false
            }
        }
    }
}