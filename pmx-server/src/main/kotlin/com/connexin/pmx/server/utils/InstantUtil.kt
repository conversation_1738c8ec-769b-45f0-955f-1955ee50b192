package com.connexin.pmx.server.utils

import java.time.*
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

class InstantUtil {
    companion object {
        /**
         * Returns an Instant at the same time relative to 1/1/2022.
         * @param time The original date/time instant.
         * @return The same instant
         */
        fun toEpochTime(time: OffsetTime, date: LocalDate = LocalDate.of(2022, 1, 1)): Instant {
            return time.atDate(date).toInstant()
        }

        /**
         * Returns true if string value converted to int is greater than 2300
         * @param original The original time in 24 hour format HH:mm
         * @return true or false
         */
        private fun shouldAddOneDay(original: String): Boolean {
            val originalInt = original.replace(":","").toInt()
            return (originalInt >= 2400)
        }

        /**
         * Returns tomorrow if string value converted to int is greater than 2300, otherwise returns today
         * @param time The original time in 24 hour format HH:mm
         * @return today or tomorrow (as an Instant)
         */
        private fun getValidDayBasedOnTime(time: String, instant: Instant?): Instant {

            // If we don't know the day, default to "now"
            val newInstant = instant ?: Instant.now()

            if (shouldAddOneDay(time)) {
                return newInstant.plus(1, ChronoUnit.DAYS)
            }

            return newInstant

        }

        /**
         * Returns string in valid 24 hour time HH:mm
         * Function assumes that original is 24:00 or later.
         * @param original The original time in invalid 24 hour time format HH:mm (i.e. 24:00 or later)
         * @return string in valid 24 hour time format HH:mm (00:00 to 24:00)
         */
        private fun convertToNormal24HourTime(original: String): String {

            val (hours, minutes) = original.split(":")
            return "${(hours.toInt() - 24).toString().padStart(2, '0')}:$minutes"

        }

        /**
         * Parses a string that is expected to be 24 hour Eastern time, formatted as HH:mm, and returns an OffsetTime.
         * @param original A string in HH:mm format expressing a time assumed to be in Eastern time.
         * @param instant The day the time is supposed to be on so the correct offset can be calculated. If the date is not known, provide null and "now" will be used.
         * @return The parsed OffsetTime if original is not null or empty; otherwise, null.
         * Examples:
         *   If original = 00:00 or 24:00, function returns 00:00-04:00
         *   If original = 01:00 or 25:00, function returns 01:00-04:00
         */
        fun parse24HourEasternAsOffsetTime(original: String?, instant: Instant? = null): OffsetTime? {

            if (original.isNullOrBlank()) return null

            val newInstant = getValidDayBasedOnTime(original, instant)
            var newOriginal = original

            if (shouldAddOneDay(newOriginal)) {
                newOriginal = convertToNormal24HourTime(newOriginal)
            }

            // gets the offset at a specific date. very important that we pass in what DAY they are
            // requesting the time (if we know it) at so that we can handle DST correctly.
            val offset = ZoneId.of("US/Eastern").rules.getOffset(newInstant)
            return LocalTime.parse(newOriginal, DateTimeFormatter.ofPattern("HH:mm"))
                .atOffset(offset)
        }

        /**
         * Converts a date expressed as an instant and returns a date string assuming Eastern time zone
         * @param dateTimeInstant A date expressed as an instant
         * @return The date string in the format "MM/dd/yyyy hh:mm:ss a" if the instant is not null or empty; otherwise, null.
         */
        fun toLocalDateTime(dateTimeInstant: Instant?): String? {
            if (dateTimeInstant == null) return null
            val localDateTime = dateTimeInstant.atZone(ZoneId.of("US/Eastern")).toLocalDateTime()
            val formattedDateTime: String = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a").format(localDateTime)
            return formattedDateTime
        }

        /**
         * Parses a date string and returns an instant assuming Eastern time zone
         * @param dateTimeString A string in the format "MM/dd/yyyy hh:mm:ss a"
         * @return The instant if date string is not null or empty; otherwise, null.
         */
        fun toInstant(dateTimeString: String?): Instant? {
            if (dateTimeString.isNullOrBlank()) return null
            val dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a")
            val localDateTime = LocalDateTime.parse(dateTimeString, dateFormatter)
            return localDateTime.atZone(ZoneId.of("US/Eastern")).toInstant()
        }
    }
}