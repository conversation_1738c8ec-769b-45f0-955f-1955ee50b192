package com.connexin.pmx.server.models.telnyx

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class SearchPhoneNumberResponse(
    @JsonProperty("data")
    val data: List<PhoneNumber>,
    @JsonProperty("meta")
    val meta: Meta
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CostInformation(
        @JsonProperty("currency")
        val currency: String,
        @JsonProperty("monthly_cost")
        val monthlyCost: String,
        @JsonProperty("upfront_cost")
        val upfrontCost: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class PhoneNumber(
        @JsonProperty("best_effort")
        val bestEffort: Boolean,
        @JsonProperty("cost_information")
        val costInformation: CostInformation,
        @JsonProperty("features")
        val features: List<Feature>,
        @JsonProperty("phone_number")
        val phoneNumber: String,
        @JsonProperty("quickship")
        val quickship: Boolean,
        @JsonProperty("record_type")
        val recordType: String,
        @JsonProperty("region_information")
        val regionInformation: List<RegionInformation>,
        @JsonProperty("reservable")
        val reservable: Boolean,
        @JsonProperty("vanity_format")
        val vanityFormat: String?,
        @JsonProperty("phone_number_type")
        val phoneNumberType: String?
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class RegionInformation(
        @JsonProperty("region_name")
        val regionName: String,
        @JsonProperty("region_type")
        val regionType: String
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Meta(
        @JsonProperty("best_effort_results")
        val bestEffortResults: Int,
        @JsonProperty("total_results")
        val totalResults: Int
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Feature(
        @JsonProperty("name")
        val name: String
    )
}