package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Orders")
data class MTMessages(   // text messages

    @JacksonXmlProperty(localName = "Order")
    @JacksonXmlElementWrapper(useWrapping = false)
    var order: MTMessage

)

@JsonRootName("Order")
data class MTMessage(
    
    @JacksonXmlProperty(isAttribute = true, localName = "Type")
    var type: String?,

    @JacksonXmlProperty(localName = "ShortCode")
    var shortCode: String?,

    @JacksonXmlProperty(localName = "CellPhoneNumber")
    var cellPhoneNumber: String?,

    @JacksonXmlProperty(localName = "Date")
    var date: String?,

    @JacksonXmlProperty(localName = "Time")
    var time: String?,

    @JacksonXmlProperty(localName = "StopTime")
    var stopTime: String?,

    @JacksonXmlProperty(localName = "RestartTime")
    var restartTime: String?,

    @JacksonXmlProperty(localName = "OverType")
    var overType: String?,

    @JacksonXmlProperty(localName = "MessageFile")
    var messageFile: String?,

    @JacksonXmlProperty(localName = "MessageID")
    var messageID: String?,

    @JacksonXmlProperty(localName = "Message")
    var message: String?

)