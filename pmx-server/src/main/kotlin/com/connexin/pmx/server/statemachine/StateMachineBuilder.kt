package com.connexin.pmx.server.statemachine

class StateMachineBuilder<StateEnum, EventEnum, S: State<StateEnum, S>, C: Context> {
    private val states = mutableMapOf<StateEnum, StateTransitionBuilder<StateEnum, EventEnum, S, C>>()

    fun withState(state: StateEnum): StateTransitionBuilder<StateEnum, EventEnum, S, C> {
        val builder = StateTransitionBuilder(this, state)
        states[state] = builder
        return builder
    }

    fun build(): Map<StateEnum, StateTransition<StateEnum, EventEnum, S, C>> {
        return states.mapValues { it.value.build() }
    }

}