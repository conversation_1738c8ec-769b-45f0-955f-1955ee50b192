package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.models.dtos.DeprovisionResult
import com.connexin.pmx.server.services.CustomerProvisioningService
import com.connexin.pmx.server.services.CustomerService
import io.swagger.v3.oas.annotations.Hidden
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@Hidden
@RequestMapping("/api/v2/customers")
@PreAuthorize("hasRole('ROLE_ADMIN')")
class CustomerV2Controller(
    private val customerService: CustomerService,
    private val customerProvisioningService: CustomerProvisioningService
) {
    @DeleteMapping("/{id}/{billingGroupId}/{outboundVoiceProfileId}")
    fun deleteCustomer(
        @PathVariable id: String,
        @PathVariable billingGroupId: String,
        @PathVariable outboundVoiceProfileId: String
    ): ResponseEntity<DeprovisionResult> {
        MDC.put(MdcKeys.MDC_CUSTOMER_ID, id)
        log.info("Received request to DELETE customer")
        try {
            val customer = customerService.getById(id)
            return if (customer != null) {
                log.info("Deprovisioning Telnyx services for ${customer.name}")
                val deprovisionResult = customerProvisioningService.deprovisionV2(
                    id,
                    billingGroupId,
                    outboundVoiceProfileId
                )
                if (deprovisionResult.success) {
                    log.info("Deleting internal record for ${customer.name}")
                    customerService.deleteById(id)
                    ResponseEntity.noContent().build()
                } else {
                    ResponseEntity.internalServerError().body(deprovisionResult)
                }
            } else {
                ResponseEntity.notFound().build()
            }
        } finally {
            MDC.remove(MdcKeys.MDC_CUSTOMER_ID)
        }
    }
    companion object{
        private var log = LoggerFactory.getLogger(CustomerV2Controller::class.java)
    }
}