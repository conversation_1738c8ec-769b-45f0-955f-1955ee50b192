package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.constraints.isEmail
import com.connexin.pmx.server.constraints.isPhoneNumber
import com.connexin.pmx.server.models.LocationResource
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.Validator
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotNull

@Schema(
    description = "A practice location."
)
data class Location(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The location's ID in the customer system."
    )
    val id: String,
    @field:Schema(
        description = "The location's practice ID in the customer system."
    )
    val practiceId: String,
    @field:Schema(
        description = "The location's name in the customer system."
    )
    val name: String,
    @field:Schema(
        description = "The location's zip code."
    )
    val zipCode: String,
    @field:Schema(
        description = "The location's business address."
    )
    val address: String? = null,
    @field:Schema(
        description = "The location's business phone number."
    )
    val phone: String? = null,
    @field:Schema(
        description = "The location's email address."
    )
    val email: String? = null
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(Location::customerId).isNotNull().isNotBlank()
            this.validate()
        }
    }

    companion object {
        fun from(resource: LocationResource): Location {
            return Location(
                id = resource.id,
                practiceId = resource.practiceId,
                name = resource.name,
                zipCode = resource.zipCode,
                address = resource.address,
                phone = resource.phone,
                email = resource.email
            )
        }
    }
}

fun Validator<Location>.validate() {
    validate(Location::id).isNotNull().isNotBlank()
    validate(Location::practiceId).isNotNull().isNotBlank()
    validate(Location::name).isNotNull().isNotBlank()
    validate(Location::zipCode).isNotNull().isNotBlank()
    validate(Location::phone).isNotNull().isPhoneNumber()

    // can be null
    validate(Location::email).isEmail()
}