package com.connexin.pmx.server.web

import kong.unirest.HttpResponse
import kong.unirest.UnirestException
import kong.unirest.UnirestInstance
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component

/**
 * RestHttpClient is a utility class for making HTTP requests (GET, POST, PATCH, DELETE) using Kong Unirest.
 * It abstracts common HTTP operations and allows for easy integration with custom Unirest instances.
 * The Authorization header (Bearer token) is handled automatically by the external 'OAuth2UnirestAuthenticationInterceptor',
 * so it should not be included in the headers passed to this class.
 *
 * To use, simply inject the appropriate UnirestInstance (such as "bridgeUnirestInstance") and
 * call the desired HTTP method with the necessary URL and headers.
 *
 * The architecture pattern in use here follows principles of abstraction, dependency injection, and separation of concerns,
 * enabling the RestHttpClient class to remain flexible and extensible while hiding the complexities of HTTP operations.
 *
 */
@Component
class RestHttpClient(
    @Qualifier("unirestInstant") private val unirestInstance: UnirestInstance,
) {
    @Throws(UnirestException::class)
    fun getAsString(url: String, headers: Map<String, String>): HttpResponse<String> {
        log.info("GET request [url={}, headers={}]", url, headers)

        val response = unirestInstance[url]
            .headers(headers)
            .asString()

        log.info("GET request [url={}, response code={}, status={}]", url, response.status, response.statusText)

        return response
    }

    @Throws(UnirestException::class)
    fun postAsString(url: String, headers: Map<String, String>, body: Any?): HttpResponse<String> {
        log.info("POST request [url={}, headers={}]", url, headers)

        val response: HttpResponse<String> = unirestInstance.post(url)
            .headers(headers)
            .body(body ?: "")
            .asString()

        log.info("POST request [url={}, response code={}, status={}]", url, response.status, response.body)

        return response
    }

    @Throws(UnirestException::class)
    fun patchAsString(url: String, headers: Map<String, String>, body: Any?): HttpResponse<String> {
        log.info("PATCH request [url={}, headers={}]", url, headers)

        val response = unirestInstance.patch(url)
            .headers(headers)
            .body(body)
            .asString()

        log.info("PATCH request [url={}, response code={}, status={}]", url, response.status, response.body)

        return response
    }

    @Throws(UnirestException::class)
    fun deleteAsString(url: String, headers: Map<String, String>): HttpResponse<String> {
        log.info("DELETE request [url={}, headers={}]", url, headers)

        val response = unirestInstance.delete(url)
            .headers(headers)
            .asString()

        log.info("DELETE request [url={}, response code={}, status={}]", url, response.status, response.body)

        return response
    }

    companion object {
        private val log = LoggerFactory.getLogger(RestHttpClient::class.java)
    }
}