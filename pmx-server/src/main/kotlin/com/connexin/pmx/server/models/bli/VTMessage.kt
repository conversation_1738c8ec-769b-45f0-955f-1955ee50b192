package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.*
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Orders")
data class VTMessages(   // voice messages

    @JacksonXmlProperty(localName = "Order")
    @JacksonXmlElementWrapper(useWrapping = false)
    var order: VTMessage

)

@JsonRootName("Order")
data class VTMessage(

    @JacksonXmlProperty(isAttribute = true, localName = "Type")
    var type: String?,

    @JacksonXmlProperty(localName = "Phone")
    var phoneNumber: String?,

    @JacksonXmlProperty(localName = "CallerID")
    var callerID: String?,

    @JacksonXmlProperty(localName = "StopTime")  // hh:mm format
    var stopTime: String?,

    @JacksonXmlProperty(localName = "RestartTime")  // hh:mm format
    var restartTime: String?,

    @JacksonXmlProperty(localName = "Documents")
    var vtDocuments: List<VTDocument> = ArrayList(),

    @JacksonXmlProperty(localName = "HotOne")
    var hotOne: String?,

    @JacksonXmlProperty(localName = "HotTwo")
    var hotTwo: String?,

    @JacksonXmlProperty(localName = "HotThree")
    var hotThree: String?,

    @JacksonXmlProperty(localName = "HotFour")
    var hotFour: String?,

    @JacksonXmlProperty(localName = "HotFive")
    var hotFive: String?,

    @JacksonXmlProperty(localName = "HotSix")
    var hotSix: String?,

    @JacksonXmlProperty(localName = "HotSeven")
    var hotSeven: String?,

    @JacksonXmlProperty(localName = "HotEight")
    var hotEight: String?,

    @JacksonXmlProperty(localName = "HotNine")
    var hotNine: String?,

    @JacksonXmlProperty(localName = "HotZero")
    var hotZero: String?,

    @JacksonXmlProperty(localName = "HotStar")
    var hotStar: String?,

    @JacksonXmlProperty(localName = "HotPound")
    var hotPound: String?

)

@JsonRootName("Document")
data class VTDocument(

    @JacksonXmlProperty(localName = "DocumentID")
    var documentID: String?,

    @JacksonXmlProperty(localName = "DocumentType")
    var documentType: String?,

    @JacksonXmlProperty(localName = "DocumentName")
    var documentName: String?,

    @JacksonXmlProperty(localName = "DocumentBinary")
    var documentBinary: String?

)