package com.connexin.pmx.server.statemachine

class TransitionBuilder<StateEnum, EventEnum, S: State<StateEnum, S>, C: Context>(
    private val parent: StateTransitionBuilder<StateEnum, EventEnum, S, C>,
    private val event: EventEnum
) {
    private var _target: StateEnum? = null
    private var _action: Action<S, Event<EventEnum>, C>? = null

    fun target(state: StateEnum): TransitionBuilder<StateEnum, EventEnum, S, C> {
        _target = state
        return this
    }

    fun action(lambda: Action<S, Event<EventEnum>, C>): StateTransitionBuilder<StateEnum, EventEnum, S, C> {
        _action = lambda
        return parent
    }

    fun noaction(): StateTransitionBuilder<StateEnum, EventEnum, S, C> {
        return parent
    }

    fun build(): Transition<StateEnum, EventEnum, S, C> {
        return Transition(
            id = event,
            target = _target!!,
            action = _action
        )
    }
}