package com.connexin.pmx.server.models

enum class EngagementEvent {
    CHECKPOINT,
    CA<PERSON><PERSON>LED,
    DECLINED,
    CONFIRMED,
    REMINDERS_SENT,
    CANCELLATION_SENT,
    START_CONFIRMATION,
    START_CHECKIN,
    START_<PERSON>EMINDER,
    ERRO<PERSON>,
    START_BOOKING,
    BOOKING_SENT,
    WORKFLOW_COMPLETE,
    CHECKED_IN,
    CHEC<PERSON><PERSON>_DONE,
    START_APPOINTMENT_SURVEY,
    APPOINTMENT_SURVEY_SENT
}