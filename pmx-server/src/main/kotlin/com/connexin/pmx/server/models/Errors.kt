package com.connexin.pmx.server.models

enum class Errors(val code: Int, val message: String) {
    VALIDATION_FAILED(1, "One or more fields were invalid. See details for more information."),
    DUPLICATE(2, "The resource already exists."),
    DUPLICATE_USERNAME(3, "A user with that username already exists."),
    NOT_FOUND(4, "The requested resource could not be found."),
    UNAUTHORIZED(5, "Not authorized."),
    UNEXPECTED_ERROR(6, "An unexpected error occurred."),
    OVERLAPPING_ENGAGEMENT_RULES(7, "The requested engagement rule would overlap an existing engagement rule."),
    MALFORMED_REQUEST(10001, "The request failed because it was not well-formed."),
    ENGAGEMENT_NO_APPLICABLE_RULES(20001, "No applicable engagement rules could be found."),
    ENGAGEMENT_NO_APPOINTMENTS(20002, "Expected one or more appointments when creating or updating an engagement, but none were provided."),
    ENGAGEMENT_CONFIRMATION_RULE_MISSING(20003, "The rule for the confirmation workflow was deleted or disabled."),
    ENGAGEMENT_REMINDER_RULE_MISSING(20004, "The rule for the reminder workflow was deleted or disabled."),
    ENGAGEMENT_CHECK_IN_RULE_MISSING(20005, "The rule for the check-in workflow was deleted or disabled."),
    ENGAGEMENT_CANCELLATION_RULE_MISSING(20006, "The rule for the cancellation workflow was deleted or disabled."),
    CONTACT_METHOD_OR_LANGUAGE_TEMPLATE_MISSING(20007, "The template for the contact's preferred contact method and language is missing. See details for more information."),
    ENGAGEMENT_BOOKING_RULE_MISSING(20008, "The rule for the booking workflow was deleted or disabled."),
    ENGAGEMENT_APPOINTMENT_SURVEY_RULE_MISSING(20009, "The rule for the appointment-survey workflow was deleted or disabled."),
    ENGAGEMENT_APPOINTMENT_INTERNAL_SURVEY_RULE_MISSING(20010, "The rule for the INTERNAL appointment-survey workflow was deleted or disabled."),
    ENGAGEMENT_APPOINTMENT_EXTERNAL_SURVEY_RULE_MISSING(20011, "The rule for the EXTERNAL appointment-survey workflow was deleted or disabled."),
    CONFIGURATION_ERROR(30001, "There is a configuration error in the PMX+ system. Contact support for more details."),
    INVALID_EMAIL_OR_PHONE(30002, "Recipient's email or phone is invalid or could not be reached."),
    INVALID_MESSAGE_CONTENT(30003, "The message content was blocked by a third party or carrier. See details for more information."),
    BLOCKED_SPAM_TEMPORARY(30004, "The message was flagged as SPAM and blocked, however this flag was not permanent and other messages may deliver successfully."),
    BLOCKED_SPAM_PERMANENT(30005, "The recipient flagged all messages originating from this number as SPAM and can no longer be reached."),
    BLOCKED_OPTED_OUT(30006, "The recipient has opted out and will no longer receive messages from this number. Recipient may opt back in at any time."),
    DELIVERY_FAILURE(30007, "The message could not be delivered because of problems with the third party or carrier. See details for more information."),
    DISPATCH_FAILURE_TEMPORARY(30008, "The message could not be delivered because of a temporary problem with the third party or carrier. Retrying or subsequent messages may succeed."),
    MAILBOX_FULL(30009, "The recipient's mailbox is full."),
    INVALID_CONTACT_METHOD(30010, "An invalid contact preference was specified for the contact."),
    NO_CONTACTS(30011, "No contacts found. All contacts may have been deleted or changed their reminder preferences to stop receiving notifications.")
}