package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.telnyx.BillingGroupResponse
import com.connexin.pmx.server.models.telnyx.CallControlApplicationRequest
import com.connexin.pmx.server.models.telnyx.SearchPhoneNumberResponse
import com.telnyx.sdk.model.CreateMessageRequest
import java.util.*

/**
 * Contains functionality required to perform misc. functions not covered by SDK or are buggy.
 */
interface TelnyxService {
    /**
     * Creates a new billing group.
     */
    fun createBillingGroup(name: String): BillingGroupResponse.BillingGroupResult

    /**
     * Retrieves a billing group.
     */
    fun getBillingGroup(id: UUID): BillingGroupResponse.BillingGroupResult

    /**
     * Deletes a billing group.
     */
    fun deleteBillingGroup(id: UUID): BillingGroupResponse.BillingGroupResult

    /**
     * Searches for available toll-free numbers suitable for PMX.
     */
    fun searchTollFreeNumbers(): List<SearchPhoneNumberResponse.PhoneNumber>
    fun createCallControlConnection(
        name: String,
        webhookUrl: String,
        outboundVoiceProfileId: String
    ): CallControlApplicationRequest.CallControlApplication

    /**
     * Retrieves a call control connection application
     */
    fun getCallControlConnection(id: String): CallControlApplicationRequest.CallControlApplication

    /**
     * Deletes a call control connection application
     */
    fun deleteCallControlConnection(id: String): CallControlApplicationRequest.CallControlApplication

    /**
     * Creates an SMS or MMS message and sends it via Telnyx Messaging, returns the message ID that was created.
     */
    fun createMessage(request: CreateMessageRequest): String
}