package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.config.MDCCoroutineContext
import com.connexin.pmx.server.models.CheckpointEvent
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.EngagementStatus
import com.connexin.pmx.server.models.MdcKeys.MDC_CUSTOMER_ID
import com.connexin.pmx.server.models.MdcKeys.MDC_ENGAGEMENT_ID
import com.connexin.pmx.server.models.MdcKeys.MDC_SCHEDULE_CORRELATION_ID
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import com.connexin.pmx.server.utils.use
import kotlinx.coroutines.*
import net.javacrumbs.shedlock.core.LockAssert
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.*
import kotlin.math.max
import kotlin.math.min

@Service
class EngagementSchedulerImpl(
    private val engagementService: EngagementService,
    private val customerService: CustomerService,
    @Value("\${op.pmx.engagement-scheduler-page-size}")
    private val pageSize: Int,
    @Value("\${op.pmx.engagement-scheduler-parallel-batch-size}")
    private val parallelBatchSize: Int
) {

    @Scheduled(
        initialDelayString = "\${op.pmx.engagement-scheduler-initial-delay}",
        fixedDelayString = "\${op.pmx.engagement-scheduler-fixed-delay}",
    )
    @SchedulerLock(name = "scheduledCheckpoint")
    fun scheduledCheckpoint() {
        LockAssert.assertLocked()

        val startQuery = Instant.now()

        MDC.putCloseable(MDC_SCHEDULE_CORRELATION_ID, UUID.randomUUID().toString()).use {
            try {
                log.info("Starting scheduledCheckpoint job. Looking for open engagements that have reached their next checkpoint.")

                val queued = try {
                    engagementService.findAwaitingCheckpoint(
                        Instant.now(),
                        PageRequest
                            .of(0, pageSize)
                            .withSort(Sort.Direction.ASC, "nextCheckpoint")
                    )
                } catch (ex: Exception) {
                    log.error("An unexpected exception occurred while fetching engagements awaiting next checkpoint.", ex)
                    return
                }

                val durationQuery = java.time.Duration.between(startQuery, Instant.now())
                log.info("Finished querying checkpoint engagements. Duration: {} ms", durationQuery.toMillis())
                log.info("Found {} engagements awaiting a checkpoint", queued.size)

                val stableParallelBatchSize = min(MAXIMUM_AMOUNT_OF_THREADS, max(MINIMUM_AMOUNT_OF_THREADS, parallelBatchSize))
                runBlocking {
                    val jobs = queued.chunked( stableParallelBatchSize).map { batch ->
                        async (Dispatchers.IO + MDCCoroutineContext(MDC.getCopyOfContextMap())) {
                            for (engagement in batch) {
                                arrayOf(
                                    MDC.putCloseable(MDC_CUSTOMER_ID, engagement.customerId),
                                    MDC.putCloseable(MDC_ENGAGEMENT_ID, engagement.id)
                                ).use {
                                    try {
                                        checkpoint(engagement)
                                    } catch (ex: Exception) {
                                        log.error("An unexpected exception occurred while running a checkpoint.", ex)
                                    }
                                }
                            }
                        }
                    }

                    jobs.awaitAll()
                }

            } catch (ex: Exception) {
                log.error("An unexpected exception occurred in scheduledCheckpoint.", ex)
            } finally {
                val duration = java.time.Duration.between(startQuery, Instant.now())
                log.info("Finished scheduledCheckpoint job. TOTAL Duration: {} ms", duration.toMillis())
            }
        }
    }

    suspend fun checkpoint(engagement: Engagement) {
        val customer = customerService.getById(engagement.customerId)

        if (customer == null || customer.status != CustomerStatus.ENABLED) {
            log.warn("Customer {} does not exist or is not enabled. Marking engagement {} as ERROR", engagement.customerId, engagement.id)
            engagement.status = EngagementStatus.ERROR
            engagementService.save(engagement)
            return
        }
        log.debug("Triggering checkpoint on engagement with id: {} for customer: {}", engagement.id, customer.id)

        engagementService.sendEvent(CheckpointEvent(
            engagement, customer
        ))

        log.debug("Checkpoint completed")
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementSchedulerImpl::class.java)
        private val MINIMUM_AMOUNT_OF_THREADS = 1
        private val MAXIMUM_AMOUNT_OF_THREADS = 50
    }
}