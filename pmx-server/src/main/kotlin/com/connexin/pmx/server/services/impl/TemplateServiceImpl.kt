package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateOrEditTemplateRequest
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.repositories.TemplateRepository
import com.connexin.pmx.server.services.TemplateService
import com.connexin.pmx.server.services.UrlGenerator
import com.connexin.pmx.server.utils.CacheKeys
import com.connexin.pmx.server.utils.PhoneNumberUtil
import com.connexin.pmx.server.utils.toResponse
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheConfig
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Caching
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.valiktor.ConstraintViolationException
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val UNASSIGNED_STAFF_NAME = "Unassigned"

@Service
@CacheConfig(
    cacheNames = [CacheKeys.templatesById]
)
class TemplateServiceImpl(
    private val repository: TemplateRepository,
    private val urlGenerator: UrlGenerator
) : TemplateService {

    override fun getById(id: String): Template? {
        // note: this method is cached. because internal method calls
        // skip proxies, we need to move caching down a level for methods
        // that should use the cached copy
        return repository.getByIdOrNull(id)
    }

    override fun findAll(pageable: Pageable): Page<Template> {
        return repository.findAll(pageable)
    }

    override fun create(request: CreateOrEditTemplateRequest): Response<Template> {
        return try {
            request.validate()

            val template = CreateOrEditTemplateRequest.to(request)

            Response.success(repository.save(template))
        } catch (ex: ConstraintViolationException) {
            ex.toResponse()
        }
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.templatesById],
                key = "#id"
            )
        ]
    )
    override fun update(id: String, request: CreateOrEditTemplateRequest): Response<Template> {
        val template = repository.findByIdOrNull(id) ?: return Response.failure(
            status = HttpStatus.NOT_FOUND,
            errors = listOf(
                ErrorDto(
                    path = "id",
                    errorCode = Errors.NOT_FOUND.code,
                    message = "Template not found"
                )
            )
        )

        return try {
            request.validate()

            Response.success(
                repository.save(
                    template.copy(
                        name = request.name,
                        workflow = request.workflow,
                        variations = request.variations,
                        scenarios = request.scenarios.ifEmpty {
                            mapOf(TemplateScenario.DEFAULT to request.variations)
                        }
                    )
                )
            )
        } catch (ex: ConstraintViolationException) {
            return ex.toResponse()
        }
    }

    override fun preview(id: String): Map<MessageType, Map<Language, MessageSegments?>>? {
        val template = repository.findByIdOrNull(id) ?: return null

        val engagement = Engagement(
            id = "engagement1",
            eventDate = Instant.from(ZonedDateTime.of(2023, 4, 5, 12, 30, 0, 0, ZoneId.of("US/Eastern"))),
            nextCheckpoint = Instant.now(),
            customerId = "customer1",
            resources = mutableSetOf(
                PracticeResource(
                    id = "practice1",
                    name = "Practice 1"
                ),
                LocationResource(
                    id = "location1",
                    name = "Location 1",
                    email = "<EMAIL>",
                    phone = "+15555555555",
                    zipCode = "00000",
                    zoneId = "US/Eastern",
                    practiceId = "practice1",
                    address = "123 Test St., Test, PA 00000"
                ),
                StaffResource(
                    id = "staff1",
                    name = "Dr Staff 1"
                ),
                PatientResource(
                    id = "patient1",
                    givenName = "First",
                    familyName = "Patient"
                ),
                PatientResource(
                    id = "patient2",
                    givenName = "Second",
                    familyName = "Patient"
                ),
                PatientResource(
                    id = "patient3",
                    givenName = "Third",
                    familyName = "Patient"
                ),
                ContactResource(
                    id = "contact1",
                    givenName = "Spanish SMS",
                    familyName = "Contact",
                    contactMethod = ContactMethod.SMS,
                    language = Language.SPANISH,
                    phone = "+15555551234",
                    email = null
                ),
                ContactResource(
                    id = "contact2",
                    givenName = "English SMS",
                    familyName = "Contact",
                    contactMethod = ContactMethod.SMS,
                    language = Language.ENGLISH,
                    phone = "+15555551234",
                    email = null
                ),
                ContactResource(
                    id = "contact3",
                    givenName = "Spanish Voice",
                    familyName = "Contact",
                    contactMethod = ContactMethod.VOICE,
                    language = Language.SPANISH,
                    phone = "+15555551234",
                    email = null
                ),
                ContactResource(
                    id = "contact4",
                    givenName = "English Voice",
                    familyName = "Contact",
                    contactMethod = ContactMethod.VOICE,
                    language = Language.ENGLISH,
                    phone = "+15555551234",
                    email = null
                ),
                ContactResource(
                    id = "contact5",
                    givenName = "Spanish Email",
                    familyName = "Contact",
                    contactMethod = ContactMethod.EMAIL,
                    language = Language.SPANISH,
                    phone = null,
                    email = "<EMAIL>"
                ),
                ContactResource(
                    id = "contact6",
                    givenName = "English Email",
                    familyName = "Contact",
                    contactMethod = ContactMethod.EMAIL,
                    language = Language.ENGLISH,
                    phone = null,
                    email = "<EMAIL>"
                ),
                AppointmentResource(
                    id = "appointment1",
                    reason = "Reason 1",
                    appointmentType = "type1",
                    practice = "practice1",
                    location = "location1",
                    patient = "patient1",
                    staff = "staff1",
                    startTime = Instant.now()
                ),
                AppointmentResource(
                    id = "appointment2",
                    reason = "Reason 2",
                    appointmentType = "type1",
                    practice = "practice1",
                    location = "location1",
                    patient = "patient2",
                    staff = "staff1",
                    startTime = Instant.now()
                ),
                AppointmentResource(
                    id = "appointment3",
                    reason = "Reason 3",
                    appointmentType = "type1",
                    practice = "practice1",
                    location = "location1",
                    patient = "patient3",
                    staff = "staff1",
                    startTime = Instant.now()
                ),
            )
        )

        val rule = EngagementRule(
            id = "rule1",
            enabled = true,
            workflow = template.workflow,
            templateIds = mapOf(TemplateScenario.DEFAULT to template.id!!)
        )

        val results = mutableMapOf<MessageType, MutableMap<Language, MessageSegments?>>()

        val substitutions = buildSubstitutions(engagement, setOf(Language.ENGLISH, Language.SPANISH))

        engagement
            .resources
            .filterIsInstance<ContactResource>()
            .forEach {
                val type = it.contactMethod.toMessageType()
                if (!results.containsKey(type)) {
                    results[type] = mutableMapOf()
                }

                results[type]!![it.language] = generateMessageSegments(rule, it, substitutions[it.language]!!)
            }

        return results
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.templatesById],
                key = "#id"
            )
        ]
    )
    override fun delete(id: String) {
        repository.deleteById(id)
    }

    private fun buildConfirmationLink(engagement: Engagement): String {
        return urlGenerator.confirm(mapOf("id" to engagement.id!!, "contact" to "\$CONTACTID")).toString()
    }

    override fun buildSubstitutions(engagement: Engagement, languages: Set<Language>, customer: Customer?): Map<Language, MutableMap<String, String>> {
        val appointments = engagement.resources.filterIsInstance<AppointmentResource>()
        val patients = engagement.resources.filterIsInstance<PatientResource>()
        val providers = engagement.resources.filterIsInstance<StaffResource>()
        val practice = engagement.resources.filterIsInstance<PracticeResource>().first()
        val location = engagement.resources.filterIsInstance<LocationResource>()
            .first()
        val translatedTime = engagement.eventDate
            .atZone(ZoneId.of(location.zoneId))
            .minusMinutes(customer?.appointmentTimeDisplayOffset?.toMinutes() ?: 0L)

        val substitutions = languages.associateWith { mutableMapOf<String, String>() }

        for (language in languages) {
            val locale = language.toLocale()
            val localSubs = substitutions[language]!!


            SubstitutionTokens.values().forEach {
                localSubs[it.token] = when (it) {
                    SubstitutionTokens.CONFIRMATION_LINK -> buildConfirmationLink(engagement)
                    SubstitutionTokens.APPOINTMENT_REASON -> appointments.joinToString(SEPARATOR) { a -> a.reason }
                    SubstitutionTokens.PATIENT_FIRST_NAME -> patients.joinToString(SEPARATOR) { p -> p.givenName }
                    SubstitutionTokens.PATIENT_LAST_NAME -> patients.joinToString(SEPARATOR) { p -> p.familyName }
                    SubstitutionTokens.PROVIDER -> if (providers.isNotEmpty()) { providers.joinToString(SEPARATOR) { p -> p.name } } else {
                        UNASSIGNED_STAFF_NAME
                    }
                    SubstitutionTokens.PRACTICE_NAME -> practice.name
                    SubstitutionTokens.LOCATION_TELEPHONE -> PhoneNumberUtil.format(location.phone ?: "")
                    SubstitutionTokens.LOCATION_ADDRESS -> location.address ?: ""
                    SubstitutionTokens.APPOINTMENT_LOCATION -> location.name
                    SubstitutionTokens.APPOINTMENT_DATE -> translatedTime.format(
                        DateTimeFormatter.ofPattern("MMM d", locale)
                    )

                    SubstitutionTokens.APPOINTMENT_DATE_VM -> translatedTime.format(
                        DateTimeFormatter.ofPattern("MMMM d", locale)
                    )

                    SubstitutionTokens.APPOINTMENT_TIME -> translatedTime.format(
                        DateTimeFormatter.ofPattern("h:mm a", locale)
                    )

                    SubstitutionTokens.APPOINTMENT_DAY_DATE -> translatedTime.format(
                        DateTimeFormatter.ofPattern("EEEE, MMM d", locale)
                    )

                    SubstitutionTokens.APPOINTMENT_DAY_DATE_VM -> translatedTime.format(
                        DateTimeFormatter.ofPattern("EEEE, MMMM d", locale)
                    )

                    SubstitutionTokens.RECALL_REASON -> ""
                    SubstitutionTokens.CHECK_IN_LINK -> ""
                    SubstitutionTokens.PIN_CODE -> ""
                    SubstitutionTokens.SURVEY_LINK -> ""
                }
            }
        }

        return substitutions
    }

    override fun generateMessageSegments(
        rule: EngagementRule,
        contact: ContactResource,
        substitutions: Map<String, String>,
        scenario: TemplateScenario
    ): MessageSegments? {
        val type = contact.contactMethod.toMessageType()
        // SMS *must* use default templates (user cannot override)
        // however, we'll try to use template overrides if the customer specified one,
        // otherwise we'll use the default
        val defaultTemplate = getById(rule.templateId!!)
        val template = when (type) {
            MessageType.SMS -> defaultTemplate?.scenarios?.get(scenario)?.get(type)?.get(contact.language)
            else -> rule.templateOverrides?.get(scenario)?.get(type)?.get(contact.language)
                ?: defaultTemplate?.scenarios?.get(scenario)?.get(type)?.get(contact.language)
        } ?: return null

        log.debug("Generating {} {} message for contact {} using template ({}): {}", contact.language, type, contact.id, defaultTemplate?.id, template)

        val contactSubs = substitutions.toMutableMap()

        // fix confirmation URL with contact's ID
        if (contactSubs.containsKey(SubstitutionTokens.CONFIRMATION_LINK.token)) {
            contactSubs[SubstitutionTokens.CONFIRMATION_LINK.token] = contactSubs[SubstitutionTokens.CONFIRMATION_LINK.token]!!.replace("\$CONTACTID", contact.id)
        }

        val main = substitute(template.main, contactSubs)!!
        val instructions = substitute(template.instructions, contactSubs)

        return MessageSegments(
            main = when (type) {
                MessageType.VOICE -> "$main $instructions"
                else -> main
            },
            altMain = when (type) {
                MessageType.VOICE -> main
                else -> null
            },
            subject = substitute(template.subject, contactSubs),
            instructions = instructions
        )
    }

    companion object {
        private const val SEPARATOR = ", "
        private val log = LoggerFactory.getLogger(TemplateServiceImpl::class.java)

        @Suppress("UNNECESSARY_NOT_NULL_ASSERTION")
        private fun substitute(template: String?, substitutions: Map<String, String>): String? {
            if (template == null) {
                return null
            }
            var result = template!!
            substitutions.forEach { (t, u) -> result = result.replace(t, u) }
            return result
        }
    }
}