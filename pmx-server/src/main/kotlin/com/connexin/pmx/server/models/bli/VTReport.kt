package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Report")
data class VTReports(   // voice message reports
    @JacksonXmlProperty(localName = "VT")
    @JacksonXmlElementWrapper(useWrapping = false)
    var vtReport: VTReport
)

@JsonRootName("VT")
data class VTReport(

    @JacksonXmlProperty(localName = "UNQID")
    var uniqueId: String?,

    @JacksonXmlProperty(localName = "JobID")
    var jobID: String?,

    @JacksonXmlProperty(localName = "PhoneNumber")
    var phoneNumber: String?,

    @JacksonXmlProperty(localName = "Duration")
    var duration: String?,

    @JacksonXmlProperty(localName = "Rate")
    var rate: String?,

    @JacksonXmlProperty(localName = "Cost")
    var cost: String?,

    @JacksonXmlProperty(localName = "Status")
    var status: String?,

    @JacksonXmlProperty(localName = "Error")
    var error: String?,

    @JacksonXmlProperty(localName = "DeliveryMethod") // Ex: Live, Voice Mail
    var deliveryMethod: String?,

    @JacksonXmlProperty(localName = "KeyPress")
    var keyPress: String?,

    @JacksonXmlProperty(localName = "Timestamp") // Ex: 5/25/2011 2:07:00 PM
    var timestamp: String?

)