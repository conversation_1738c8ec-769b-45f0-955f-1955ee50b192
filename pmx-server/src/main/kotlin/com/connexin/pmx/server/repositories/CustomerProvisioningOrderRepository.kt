package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.CustomerProvisioningOrder
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Slice
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.data.mongodb.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant

interface CustomerProvisioningOrderRepository: MongoRepository<CustomerProvisioningOrder, String>,
    CustomizedCustomerProvisioningOrderRepository<CustomerProvisioningOrder, String> {
    @Query("{ 'status': 'IN_PROGRESS', 'attempts': { '\$lt': ?0 } }")
    fun findPending(maxAttempts: Int, page: PageRequest): Slice<CustomerProvisioningOrder>
}

interface CustomizedCustomerProvisioningOrderRepository<T, ID> {
    fun <S: T?> save(entity: S): S
}

@Repository
class CustomizedCustomerProvisioningOrderRepositoryImpl(private val mongoTemplate: MongoTemplate):
    CustomizedCustomerProvisioningOrderRepository<CustomerProvisioningOrder, String> {
    override fun <S : CustomerProvisioningOrder?> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        entity!!.createdAt = entity.createdAt ?: Instant.now()
        entity.updatedAt = Instant.now()

        return mongoTemplate.save(entity)
    }

}