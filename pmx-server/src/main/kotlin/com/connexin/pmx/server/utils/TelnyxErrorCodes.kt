package com.connexin.pmx.server.utils

object TelnyxErrorCodes {
    const val INACTIVE_PHONE_NUMBER = 10001 // phone number is inactive
    const val INVALID_PHONE_NUMBER = 10002 // not a phone number
    const val INVALID_PHONE_NUMBER_FORMAT = 10016 // the number is not in +E.164 format
    const val NOT_ROUTABLE = 40001 // is either a landline or non-routable mobile number
    const val BLOCKED_AS_SPAM_TEMPORARY = 40002 // message was flagged as SPAM and not delivered, temporary condition (other messages may work)
    const val BLOCKED_AS_SPAM_PERMANENT = 40003 // system or recipient permanently blocked messages from originating number
    const val REJECTED_BY_DESTINATION = 40004 // destination system rejected message for unknown reason
    const val MESSAGE_EXPIRED_DURING_TRANSMISSION = 40005 // message expired before it could be fully delivered to destination
    const val DESTINATION_UNAVAILABLE = 40006 // destination system is not responding, may be temporary condition
    const val UNDELIVERABLE = 40008 // general purpose delivery failure
    const val INVALID_MESSAGE_BODY = 40009 // incompatibility between telnyx and carrier rules regarding valid message content
    const val TOO_MANY_REQUESTS = 40011 // marked as SPAM for sending too many messages in a window, temporary condition
    const val INVALID_DESTINATION_NUMBER = 40012 // carrier thinks the destination number is invalid
    const val INVALID_SOURCE_NUMBER = 40013 // the number specified to send from is invalid according to the carrier
    const val MESSAGE_EXPIRED_IN_QUEUE = 40014 // telnyx queued the message but was unable to deliver it in a timely fashion; should retry
    const val BLOCKED_AS_SPAM_INTERNAL = 40015 // telnyx flagged message as spam
    const val BLOCKED_DUE_TO_STOP_MESSAGE = 40300 // recipient responded with STOP message
    const val INVALID_TO_ADDRESS = 40310 // number not in +E.164 or short code format
    const val MESSAGING_PROFILE_DISABLED = 40312 // messaging profile was disabled
    const val UNHEALTHY_FROM_ADDRESS = 40315 // number used deemed unhealthy, retry
    const val NO_CONTENT = 40316 // message contains no content
    const val QUEUE_FULL = 40318 // message couldn't be queued, throttle and try again
    const val FROM_ADDRESS_TEMPORARILY_UNUSABLE = 40320 // from number was ordered recently and not available for sending yet
    const val NO_NUMBERS_IN_POOL = 40321 // ready number is missing from pool, can't continue until number(s) are assigned to pool
    const val BLOCKED_DUE_TO_CONTENT = 40322 // message contains content that is blocked
    const val SMS_EXCEEDS_RECOMMENDED_SIZE = 40328 // message is too large, reduce size of message being sent
    const val TOLL_FREE_NUMBER_NOT_VERIFIED = 40329 // toll free number not verified yet

}