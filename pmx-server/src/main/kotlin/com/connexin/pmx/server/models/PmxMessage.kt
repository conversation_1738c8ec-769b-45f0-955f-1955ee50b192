package com.connexin.pmx.server.models

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.io.Serializable
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset

/**
 * Indicates the type of message, which determines how to contact the recipient.
 */
enum class MessageType {
    /**
     * A text sent to a cell phone.
     */
    SMS,

    /**
     * A voice call to a cell phone or landline.
     */
    VOICE,

    /**
     * An email sent to an individual recipient.
     */
    EMAIL,

    /**
     * An email sent to a group of recipients.
     */
    EMAIL_BROADCAST
}

/**
 * Indicates the status of a message.
 */
enum class MessageStatus {
    /**
     * The message has been accepted, but has not been dispatched yet.
     */
    QUEUED,

    /**
     * A failure occurred at any point in the message lifecycle.
     */
    FAILED,

    /**
     * The message was dispatched to the vendor and is waiting to be sent to the recipient.
     */
    DISPATCHED,

    /**
     * The vendor has sent the message to the recipient and is awaiting delivery confirmation.
     */
    SENT,

    /**
     * The message was successfully delivered to the recipient.
     */
    DELIVERED
}

/**
 * Describes how a voice message was delivered.
 */
enum class VoiceDeliveryMethod {
    /**
     * The message was not delivered yet, or delivery cannot be explained.
     */
    UNKNOWN,
    /**
     * The recipient hung up before the message could be relayed.
     */
    HANGUP,
    /**
     * Message was delivered to someone directly.
     */
    LIVE,
    /**
     * Message was left on the machine.
     */
    VOICE_MAIL
}

/**
 * Describes the reason an email failed to be delivered.
 */
enum class EmailDeliveryFailureReason {
    /**
     * A general hard bounce. The recipient's email address should be removed from the mailing list.
     */
    HARD_BOUNCE,

    /**
     * A general soft bounce. You may be able to send to this recipient in the future.
     */
    SOFT_BOUNCE,

    /**
     * An internal vendor issue prevented the email from being delivered.
     */
    VENDOR_INTERNAL_ERROR,

    /**
     * A temporary issue with the recipient's email server.
     */
    RECIPIENT_SERVER_ERROR,

    /**
     * The content of the message was rejected, possibly because a virus was detected.
     */
    CONTENT_REJECTED,

    /**
     * An attachment was rejected, possibly because a virus was detected.
     */
    ATTACHMENT_REJECTED,

    /**
     * The recipient's mailbox is full and unable to receive additional messages.
     */
    MAILBOX_FULL,

    /**
     * The message was too large.
     */
    MESSAGE_TOO_LARGE,

    /**
     * The recipient's mail server detected a large amount of unsolicited emails from the sender.
     */
    SPAM_DETECTED,

    /**
     * The IP address sending the message is being blocked or throttled by the recipient's email provider.
     */
    SENDING_IP_BLOCKED,

    /**
     * There was a temporary communication failure during the SMTP conversation with the recipient's email provider.
     */
    COMMUNICATION_FAILURE,

    /**
     * The recipient's email address is unsubscribed from the mailing list.
     */
    UNSUBSCRIBED,

    /**
     * A reason for the delivery failure could not be determined.
     */
    UNDETERMINED
}

/**
 * Messages originating from the PMX system.
 */
@Document("pmx_messages")
@CompoundIndexes(
    value = [
        CompoundIndex(
            name = "queued_pmx_messages",
            def = "{'status': 1, 'sendAfter': 1, 'sendWindow.from': 1, 'sendWindow.until': 1}",
            unique = false
        ),
        CompoundIndex(
            name = "customer_pmx_messages",
            def = "{ 'customerId' : 1, 'type' : 1, 'status' : 1 }",
            unique = false
        ),
    ]
)
data class PmxMessage(
    /**
     * ID for the record.
     */
    @Id val id: String? = null,
    /**
     * The OPMED ID of the customer who originated the message.
     */
    val customerId: String,
    /**
     * The type of message.
     */
    val type: MessageType,
    /**
     * The status of the message.
     */
    var status: MessageStatus,
    /**
     * The date/time (UTC) after which the message can be sent.
     */
    var sendAfter: Instant,
    /**
     * The optional window within which it is safe to send a message (so as not to disturb people at night). If null, the message will send the earliest possible time after sendAfter.
     */
    val sendWindow: SendWindow? = null,
    /**
     * The number of attempts we made to send the message.
     */
    var attempts: Int = 0,
    /**
     * The date/time (UTC) the record was created.
     */
    var createdAt: Instant? = null,
    /**
     * The date/time (UTC) the record was last updated.
     */
    var updatedAt: Instant? = null,
    /**
     * The date/time (UTC) the record was completed (status = DELIVERED or FAILED)
     */
    var completedAt: Instant? = null,
    /**
     * When type is SMS, VOICE, or EMAIL, the email or phone number of the recipient.
     */
    @Indexed val to: String? = null,
    /**
     * When type is EMAIL or EMAIL_BROADCAST, the email address the message is from.
     */
    val from: String? = null,
    /**
     * When type is EMAIL or EMAIL_BROADCAST, the subject of the email.
     */
    val subject: String? = null,
    /**
     * The message to play or send to the recipient.
     */
    val message: String,
    /**
     * When the type is VOICE, the message that should be played if the call reaches the recipient's voicemail.
     */
    val altMessage: String? = null,
    /**
     * The remote ID of the message as provided by the vendor, after the message was dispatched to the recipient.
     */
    @Indexed var remoteId: String? = null,
    /**
     * A description of the problem(s) if the status is FAILED.
     */
    var errors: String?  = null,
    /**
     * When the type is VOICE or EMAIL, the phone or email address the user can be connected or respond to.
     */
    val replyTo: String? = null,
    /**
     * Any response data that might be useful to the type of message. For example, when the type is VOICE, this field will contain the digits they pressed during the call.
     */
    var responseData: String? = null,
    /**
     * Confirmation status of the message. Currently, only used by type = VOICE.
     */
    var confirmationStatus: ConfirmationStatus = ConfirmationStatus.NOT_APPLICABLE,
    /**
     * When type is VOICE, the method by which the message was delivered. For all other types, this will always be UNKNOWN.
     */
    var voiceDeliveryMethod: VoiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
    /**
     * Used when type is EMAIL_BROADCAST, tracks individual recipients, the message that was sent, and message status.
     */
    val emailRecipients: List<EmailRecipient>? = null,
    /**
     * When type is EMAIL, the reason the message could not be delivered.
     */
    var emailDeliveryFailureReason: EmailDeliveryFailureReason? = null,
    /**
     * The ID of the engagement this message was triggered by, or null for legacy PMX messages.
     */
    @Indexed(sparse = true) val engagementId: String? = null,
    /**
     * The rule within the engagement that triggered this message, or null of legacy pmx messages
     */
    val engagementRuleId: String? = null,
    /**
     * Instructions to play for voice messages.
     */
    val instructions: String? = null,
    /**
     * The preferred language of the recipient(s).
     */
    val language: Language? = null,
    /**
     * A history of responses from the recipient of the message.
     */
    val responses: MutableList<Response> = mutableListOf()
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1
    }

    data class EmailRecipient(
        val address: String,
        var status: MessageStatus,
        var reason: EmailDeliveryFailureReason? = null,
        var remoteId: String? = null,
        var unsubscribed: Boolean? = null,
        var errors: String? = null
    ) : Serializable {
        companion object {
            private const val serialVersionUID: Long = 1
        }
    }

    /**
     * Defines the window within which it is safe to send a message.
     */
    data class SendWindow(
        /**
         * The starting time of the window, in minutes since midnight on 1/1/2022 UTC.
         */
        val from: Instant,
        /**
         * The ending time of the window, in minutes since midnight on 1/1/2022 UTC.
         */
        val until: Instant
    ) : Serializable {
        companion object {
            private const val serialVersionUID: Long = 1

            val DEFAULT = SendWindow(
                from = Instant.from(
                    LocalDateTime.of(2022, 1, 1, 9, 0, 0).toInstant(ZoneOffset.of("-5"))
                ),
                until = Instant.from(
                    LocalDateTime.of(2022, 1, 1, 21, 0, 0).toInstant(ZoneOffset.of("-5"))
                )
            )
        }
    }

    data class Response(
        val text: String,
        val receivedAt: Instant
    ) : Serializable {
        companion object {
            private const val serialVersionUID: Long = 1
        }
    }
}