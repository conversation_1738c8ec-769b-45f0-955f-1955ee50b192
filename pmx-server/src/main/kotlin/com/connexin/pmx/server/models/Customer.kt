package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.io.Serializable
import java.time.Duration
import java.time.Instant
import java.time.LocalTime

/**
 * Describes the status of an ordered phone number.
 */
@Schema(
    description = "Describes the order status of a phone number.",
    enumAsRef = true
)
enum class PhoneNumberOrderStatus {
    /**
     * Order is still pending and may not be usable yet.
     */
    PENDING,

    /**
     * Order is complete and the number is usable.
     */
    SUCCESS,

    /**
     * The order could not be completed and the number is not usable.
     */
    FAILURE
}

/**
 * Describes the status of a customer.
 */
@Schema(
    description = "Describes the status of a customer account.",
    enumAsRef = true
)
enum class CustomerStatus {
    /**
     * Customer is undergoing the provisioning process.
     */
    PROVISIONING,

    /**
     * Customer has been provisioned and can send and receive messages.
     */
    ENABLED,

    /**
     * The customer has been disabled and cannot send or receive messages.
     */
    DISABLED
}

@Document("customers")
@Schema(
    description = "Describes a customer that can use the system and their configuration."
)
data class Customer(
    // todo: Reconfigure the ID to not be optional, refactor entire codebase
    /**
     * The customer's OPMED ID
     */
    @Id val id: String? = null,
    /**
     * The status of the customer.
     */
    var status: CustomerStatus,
    /**
     * Business name for the customer (not to be confused with the name of a practice or location within the customer's hierarchy)
     */
    var name: String,
    /**
     * Telnyx-specific configuration for the customer.
     */
    var telnyxConfig: TelnyxConfig = TelnyxConfig(),
    /**
     * The Atlas ID of the customer
     */
    var organizationId: Long? = null,
    /**
     * The legacy username used for older PMX customers.
     */
    var legacyUsername: String? = null,
    /**
     * The legacy password used for older PMX customers.
     */
    var legacyPassword: String? = null,
    /**
     * When the customer record was created.
     */
    var createdAt: Instant? = null,
    /**
     * When the customer record was last updated.
     */

    var updatedAt: Instant? = null,
    /**
     * What days messages can be delivered on.
     */
    var deliveryDays: MutableSet<DeliveryDay> = mutableSetOf(*DeliveryDay.values()),
    /**
     * The beginning of the window when messages can be delivered.
     */
    var deliveryStartTime: LocalTime = LocalTime.of(9, 0),

    /**
     * The end of the window when messages can be delivered.
     */
    var deliveryEndTime: LocalTime = LocalTime.of(17, 0),

    /**
     * The minimum timeframe before an appointment start when the contact can cancel an appointment.
     */
    var cancellationDeadline: Duration? = Duration.ofHours(12),

    /**
     * "The period of time (in seconds) prior to scheduled appointment time during which patient is asked to arrive."
     */
    var appointmentTimeDisplayOffset: Duration? = Duration.ofMinutes(0),

    val engagementRules: MutableSet<EngagementRule> = mutableSetOf()
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1
    }

    /**
     * Represents a customer's Telnyx configuration.
     */
    @Schema(
        description = "The customer's Telnyx configuration."
    )
    data class TelnyxConfig(
        /**
         * The customer's Billing Group ID.
         */
        @field:Schema(
            description = "The ID of the Telnyx Billing Group that was created for the customer."
        )
        var billingGroupId: String? = null,
        /**
         * The outbound voice profile assigned to the customer.
         */
        @field:Schema(
            description = "The ID of the Telnyx Outbound Voice Profile that was created for the customer."
        )
        var outboundVoiceProfileId: String? = null,
        /**
         * The messaging profile used by the customer to send SMS/MMS messages.
         */
        @field:Schema(
            description = "The ID of the Telnyx Messaging Profile that was created for the customer."
        )
        var messagingProfileId: String? = null,
        /**
         * The call control connection used by the customer to send voice calls.
         */
        @field:Schema(
            description = "The ID of the Telnyx Call Control Connection that was created for the customer."
        )
        var callControlConnectionId: String? = null,
        /**
         * The numbers provisioned for the customer.
         */
        @field:Schema(
            description = "The phone numbers that have been ordered for the customer which will be used for legacy SMS and voice workflows."
        )
        var phoneNumbers: MutableSet<PhoneNumber> = mutableSetOf()
    ) : Serializable {
        companion object {
            private const val serialVersionUID: Long = 1
        }

        /**
         * Returns the best number for the specified type of message.
         */
        fun getNumber(type: MessageType): PhoneNumber? {
            return phoneNumbers.firstOrNull {
                when(type) {
                    MessageType.VOICE -> it.defaultVoice && it.orderStatus == PhoneNumberOrderStatus.SUCCESS
                    MessageType.SMS -> it.defaultMessaging && it.orderStatus == PhoneNumberOrderStatus.SUCCESS
                    else -> false
                }
            }
        }
        /**
         * Represents a number provisioned for a customer.
         */
        data class PhoneNumber(
            /**
             * The ID for the number.
             */
            val id: String,
            /**
             * The actual number, in E164 format.
             */
            val phoneNumber: String,
            /**
             * The ID of the original order created when provisioning the number.
             */
            val orderId: String,

            /**
             * The status of the phone number order.
             */
            var orderStatus: PhoneNumberOrderStatus,

            /**
             * Whether to use this as the default number for voice messages.
             */
            val defaultVoice: Boolean,
            /**
             * Whether to use this as the default number for SMS/MMS messages.
             */
            val defaultMessaging: Boolean
        ) : Serializable {
            companion object {
                private const val serialVersionUID: Long = 1
            }
        }
    }
}
