package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.EngagementMessageResult
import com.connexin.pmx.server.services.EngagementResponseHandler
import com.connexin.pmx.server.services.ResponseService
import org.springframework.stereotype.Component

@Component
class EngagementResponseHandlerImpl(
    private val service: ResponseService
) : EngagementResponseHandler {
    override fun recordError(engagement: Engagement, vararg errors: ErrorDto): ErrorResponse {
        return service.create(
            ErrorResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                respondents = emptySet(),
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                errors = errors.toList()
            )
        )
    }

    override fun recordCheckIn(
        engagement: Engagement,
        respondents: Set<ContactResource>,
        appointments: Set<AppointmentResource>,
        result: CheckInStatus
    ): CheckInResponse {
        return service.create(
            CheckInResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                isFinal = false,
                respondents = respondents,
                appointments = appointments,
                result = result
            )
        )
    }

    override fun recordComplete(
        engagement: Engagement
    ): CompleteResponse {
        return service.create(
            CompleteResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                respondents = emptySet(),
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet()
            )
        )
    }

    override fun recordConfirmation(
        engagement: Engagement,
        result: ConfirmationStatus,
        respondents: Set<ContactResource>,
        isFinal: Boolean,
        messageId: String?
    ): ConfirmationResponse {
        return service.create(
            ConfirmationResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                isFinal = isFinal,
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                respondents = respondents,
                result = result
            )
        )
    }

    override fun recordCancellation(
        engagement: Engagement
    ): CancellationResponse {
        return service.create(
            CancellationResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                respondents = emptySet(),
                result = CancellationStatus.CANCELLED
            )
        )
    }

    override fun recordContactUnreachable(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        contactErrors: EngagementMessageResult.ContactErrors,
    ): MessageResponse {
        return recordMessageResponse(
            engagement,
            workflow,
            engagement.resources.filterIsInstance(ContactResource::class.java)
                .firstOrNull { contactErrors.id.equals(it.id, ignoreCase = true) },
            MessageStatus.FAILED,
            errors = contactErrors.errors,
            message = null,
            altMessage = null,
            subject = null
        )
    }

    override fun recordMessageResponse(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        contact: ContactResource?,
        status: MessageStatus,
        messageId: String?,
        errors: List<ErrorDto>?,
        message: String?,
        altMessage: String?,
        subject: String?
    ): MessageResponse {
        return service.create(
            MessageResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                respondents = if (contact != null) setOf(contact) else emptySet(),
                errors = errors,
                workflow = workflow,
                status = status,
                messageId = messageId,
                message = message,
                altMessage = altMessage,
                subject = subject
            )
        )
    }
}