package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.EngagementRule
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.PatchSelfManagedCustomer
import com.connexin.pmx.server.models.dtos.SelfManagedCustomer
import com.connexin.pmx.server.services.CustomerService
import com.github.fge.jsonpatch.JsonPatch
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v2/customer")
@Tag(name = "Customer", description = "Endpoints for managing the authenticated user's customer configuration.")
class CustomerController(
    service: CustomerService
): AbstractCustomerController<SelfManagedCustomer, PatchSelfManagedCustomer>(service) {
    @Operation(
        summary = "Gets the current user's customer record.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The customer record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun getCustomer(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String
    ): SelfManagedCustomer {
        return doGetCustomer(customerId)
    }

    @PatchMapping(
        "",
        consumes = ["application/json-patch+json"],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchCustomer(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody
        @Parameter(
            description = "An RFC 6902 JSON Patch payload describing the changes to the customer that you wish to make."
        )
        patch: JsonPatch
    ): SelfManagedCustomer {
       return doPatchCustomer(customerId, patch, false)
    }

    @Operation(
        summary = "Gets all engagement rules for the current customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A list of engagement rules for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/engagement-rules",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun getEngagementRules(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
    ): Set<EngagementRule> {
        return doGetEngagementRules(customerId)
    }

    @Operation(
        summary = "Creates a new engagement rule for the current customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "New engagement rule was created for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "409",
                description = "An engagement rule with the specified ID already exists.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement rule was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PostMapping(
        "/engagement-rules",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun createEngagementRule(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody rule: EngagementRule
    ): EngagementRule {
        return doCreateEngagementRule(customerId, rule)
    }

    @Operation(
        summary = "Creates or replaces an engagement rule with the provided ID for the current customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Engagement rule was saved for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement rule was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PutMapping(
        "/engagement-rules/{ruleId}",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun saveEngagementRule(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable ruleId: String,
        @RequestBody rule: EngagementRule
    ): EngagementRule {
        return doSaveEngagementRule(customerId, ruleId, rule)
    }

    @Operation(
        summary = "Gets an existing engagement rule for the current customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Existing engagement rule."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement rule not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/engagement-rules/{ruleId}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun getEngagementRule(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable ruleId: String
    ): EngagementRule {
        return doGetEngagementRule(customerId, ruleId)
    }

    @Operation(
        summary = "Deletes an existing engagement rule for the current customer.",
        responses = [
            ApiResponse(
                responseCode = "204",
                description = "Existing engagement rule was deleted for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement rule not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @DeleteMapping(
        "/engagement-rules/{ruleId}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    fun deleteEngagementRule(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable ruleId: String
    ) {
        doDeleteEngagementRule(customerId, ruleId)
    }

    override fun mapCustomer(customer: Customer): SelfManagedCustomer {
        return SelfManagedCustomer.from(customer)
    }
}