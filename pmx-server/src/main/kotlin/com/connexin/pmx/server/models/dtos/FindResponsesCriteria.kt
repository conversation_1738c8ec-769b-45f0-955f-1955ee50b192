package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

@Schema(
    description = "Criteria used to filter Responses."
)
data class FindResponsesCriteria(
    @field:Schema(
        description = "Only return responses that have occurred after this date/time."
    )
    val since: Instant? = null,
    @field:Schema(
        description = "Only return responses that are related to a specific message."
    )
    val messageId: String? = null,
    @field:Schema(
        description = "Only return responses that are related to a specific engagement."
    )
    val engagementId: String? = null,
    @field:Schema(
        description = "Whether or not to include archived responses.",
        defaultValue = "false"
    )
    val archived: Boolean = false
)
