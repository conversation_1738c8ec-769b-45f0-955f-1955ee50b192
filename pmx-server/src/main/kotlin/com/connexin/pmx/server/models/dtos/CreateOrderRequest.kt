package com.connexin.pmx.server.models.dtos

import org.valiktor.functions.isNotBlank

data class CreateOrderRequest(
    val opmedId: String,
    val name: String,
    val legacyUsername: String,
    val legacyPassword: String,
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(CreateOrderRequest::opmedId).isNotBlank()
            validate(CreateOrderRequest::name).isNotBlank()
            validate(CreateOrderRequest::legacyUsername).isNotBlank()
            validate(CreateOrderRequest::legacyPassword).isNotBlank()
        }
    }
}
