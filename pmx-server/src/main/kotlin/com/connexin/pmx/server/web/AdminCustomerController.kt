package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.EngagementRule
import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.models.dtos.AdminCustomer
import com.connexin.pmx.server.models.dtos.CreateCustomerRequest
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.PatchAdminCustomer
import com.connexin.pmx.server.services.CustomerService
import com.github.fge.jsonpatch.JsonPatch
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.MDC
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v2/admin/customers")
@Tag(
    name = "Customer Admin", description = "Endpoints for administrators to manage customers."
)
class AdminCustomerController(customerService: CustomerService): AbstractCustomerController<AdminCustomer, PatchAdminCustomer>(service = customerService) {

    @Operation(
        summary = "Gets a customer by ID.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The customer record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{customerId}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:read')")
    fun getCustomerById(
        @PathVariable customerId: String
    ): AdminCustomer {
        return doGetCustomer(customerId)
    }

    @PostMapping(
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:write')")
    fun createCustomer(
        @RequestBody request: CreateCustomerRequest
    ): AdminCustomer {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, request.opmedId)
            .use {
                val res = service.create(request)

                if (res.success) return mapCustomer(res.customer!!)
                else throw BadRequestException(errors = res.errors)
            }
    }

    @PatchMapping(
        "/{customerId}",
        consumes = ["application/json-patch+json"],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:write')")
    fun patchCustomerById(
        @PathVariable
        customerId: String,
        @RequestBody
        @Parameter(
            description = "An RFC 6902 JSON Patch payload describing the changes to the customer that you wish to make."
        )
        patch: JsonPatch
    ): AdminCustomer {
        return doPatchCustomer(customerId, patch, true)
    }

    @Operation(
        summary = "Gets all engagement rules for a customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A list of engagement rules for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{customerId}/engagement-rules",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:read')")
    fun getCustomerEngagementRules(
        @PathVariable customerId: String
    ): Set<EngagementRule> {
        return doGetEngagementRules(customerId)
    }

    @Operation(
        summary = "Creates a new engagement rule for a customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "New engagement rule was created for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "409",
                description = "An engagement rule with the specified ID already exists.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement rule was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PreAuthorize("hasAuthority('admin.customers:write')")
    @PostMapping(
        "/{customerId}/engagement-rules",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun createCustomerEngagementRule(
        @PathVariable customerId: String,
        @RequestBody rule: EngagementRule
    ): EngagementRule {
       return doCreateEngagementRule(customerId, rule)
    }

    @Operation(
        summary = "Updates an existing engagement rule for a customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Existing engagement rule was updated for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement rule not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided engagement rule was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PutMapping(
        "/{customerId}/engagement-rules/{ruleId}",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:write')")
    fun updateCustomerEngagementRule(
        @PathVariable customerId: String,
        @PathVariable ruleId: String,
        @RequestBody rule: EngagementRule
    ): EngagementRule {
        return doSaveEngagementRule(customerId, ruleId, rule)
    }

    @Operation(
        summary = "Gets an existing engagement rule for a customer.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Existing engagement rule."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement rule not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{customerId}/engagement-rules/{ruleId}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.customers:read')")
    fun getCustomerEngagementRule(
        @PathVariable customerId: String,
        @PathVariable ruleId: String
    ): EngagementRule {
        return doGetEngagementRule(customerId, ruleId)
    }

    @Operation(
        summary = "Deletes an existing engagement rule for a customer.",
        responses = [
            ApiResponse(
                responseCode = "204",
                description = "Existing engagement rule was deleted for the customer."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer or engagement rule not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @DeleteMapping(
        "/{customerId}/engagement-rules/{ruleId}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @PreAuthorize("hasAuthority('admin.customers:write')")
    fun deleteCustomerEngagementRule(
        @PathVariable customerId: String,
        @PathVariable ruleId: String
    ) {
        doDeleteEngagementRule(customerId, ruleId)
    }

    override fun mapCustomer(customer: Customer): AdminCustomer {
        return AdminCustomer.from(customer)
    }
}