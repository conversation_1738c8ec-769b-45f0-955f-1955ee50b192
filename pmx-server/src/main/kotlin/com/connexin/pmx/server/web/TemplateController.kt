package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.TemplateDto
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.TemplateService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.MDC
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v2/templates")
@Tag(name = "Template", description = "Endpoints for managing engagement templates.")
class TemplateController(
    private val templateService: TemplateService,
    private val customerService: CustomerService
) {
    @Operation(
        summary = "Gets a paginated list of templates.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A page of templates."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getTemplates(
        @RequestHeader(Constants.X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        pageable: Pageable
    ): Page<TemplateDto> {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                return templateService.findAll(pageable).map { TemplateDto.from(it) }
            }
    }

    @Operation(
        summary = "Gets a template by ID.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The template record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Template not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun getById(
        @RequestHeader(Constants.X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable
        id: String
    ): TemplateDto {
        val template = templateService.getById(id)
            ?: throw NotFoundException(errors = listOf(ErrorDto(
                path = "id",
                message = "Not found",
                errorCode = Errors.NOT_FOUND.code
            )))

        return TemplateDto.from(template)
    }

    @Operation(
        summary = "Shows a preview of what the template generates",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A preview of the template."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("/preview/{id}", produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getPreview(
        @RequestHeader(Constants.X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @PathVariable
        id: String
    ):  Map<MessageType, Map<Language, MessageSegments?>> {
        return templateService.preview(id)
            ?: throw NotFoundException(
                errors = listOf(
                    ErrorDto(
                        path = "id",
                        message = "Not found",
                        errorCode = Errors.NOT_FOUND.code
                    )
                )
            )
    }

    private fun validateCustomer(customerId: String) {
        customerService.getById(customerId)
            ?: throw NotFoundException(message = "Customer not found.")
    }
}