package com.connexin.pmx.server.models.dtos;

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.services.EngagementMessageResult

data class EngagementDataForMessageDto (
    val engagement: Engagement,
    val rule: EngagementRule,
    val substitutions: Map<Language, MutableMap<String, String>>,
    val location: LocationResource,
    val customer: Customer,
    val scenario: TemplateScenario,
    val responses: MutableList<CreateMessageResponse> = mutableListOf(),
    val contactErrors: MutableList<EngagementMessageResult.ContactErrors> = mutableListOf(),
)
