package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

@Schema(
    description = "Describes an error that occurred while processing a request."
)
data class ErrorResponse(
    @field:Schema(
        description = "A timestamp of when the error occurred."
    )
    val timestamp: Instant,
    @field:Schema(
        description = "The HTTP status code describing the response."
    )
    val status: Int,
    @field:Schema(
        description = "Description of the error."
    )
    val message: String,
    @field:Schema(
        description = "Path where the error occurred."
    )
    val path: String,
    @field:Schema(
        description = "Additional error details"
    )
    val errors: List<ErrorDto>?
)
