package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.Template
import com.connexin.pmx.server.utils.CacheKeys
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository

@Repository
interface TemplateRepository : MongoRepository<Template, String>,
    CustomizedTemplateRepository<Template, String>

interface CustomizedTemplateRepository<T, ID> {
    fun getByIdOrNull(id: ID): T?
}

@Repository
class CustomizedTemplateRepositoryImpl(
    private val template: MongoTemplate
): CustomizedTemplateRepository<Template, String> {

    @Cacheable(
        key = "#id",
        condition = "#id != null",
        unless = "#result == null",
        cacheNames = [CacheKeys.templatesById]
    )
    override fun getByIdOrNull(id: String): Template? {
        return template.findById(id, Template::class.java)
    }

}