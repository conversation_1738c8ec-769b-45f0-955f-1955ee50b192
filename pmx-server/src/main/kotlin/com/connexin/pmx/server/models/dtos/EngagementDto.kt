package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.*
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import com.connexin.pmx.server.models.Engagement
import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@Schema(
    name = "Engagement"
)
@JsonIgnoreProperties(ignoreUnknown = true)
data class EngagementDto(
    val id: String,
    val customerId: String,
    val createdAt: Instant?,
    val updatedAt: Instant?,
    val state: EngagementStatus = EngagementStatus.INITIAL,
    var confirmationStatus: ConfirmationStatus = ConfirmationStatus.UNCONFIRMED,
    var confirmationAttempts: Int = 0,
    @Deprecated("Status no longer used. Check the checkInStatus property on each Appointment resource instead.")
    @field:Schema(
        deprecated = true,
        description = "Deprecated. Use the checkInStatus property on each Appointment resource instead."
    )
    var checkInStatus: CheckInStatus = CheckInStatus.NOT_CHECKED_IN,
    var checkInAttempts: Int = 0,
    var reminderAttempts: Int = 0,
    val eventDate: Instant,
    val nextCheckpoint: Instant,
    val appointments: List<Appointment>,
    val contacts: List<Contact>,
    val activity: List<Activity>
) {
    companion object {
        fun from(engagement: Engagement): EngagementDto {
            val resources = engagement.resources
            return EngagementDto(
                id = engagement.id!!,
                customerId = engagement.customerId,
                createdAt = engagement.createdAt,
                updatedAt = engagement.createdAt,
                state = engagement.status,
                confirmationStatus = engagement.confirmationStatus,
                confirmationAttempts = engagement.confirmationAttempts,
                checkInAttempts = engagement.checkInAttempts,
                reminderAttempts = engagement.reminderAttempts,
                eventDate = engagement.eventDate,
                nextCheckpoint = engagement.nextCheckpoint,
                appointments = resources.filterIsInstance<AppointmentResource>().map { appointmentResource ->
                    Appointment(
                        id = appointmentResource.id,
                        appointmentTypeId = appointmentResource.appointmentType,
                        startTime = appointmentResource.startTime,
                        patient = Patient.from(resources.find { it.type == ResourceType.PATIENT && it.id == appointmentResource.patient } as PatientResource),
                        practice = Practice.from(resources.find { it.type == ResourceType.PRACTICE && it.id == appointmentResource.practice } as PracticeResource),
                        location = Location.from(resources.find { it.type == ResourceType.LOCATION && it.id == appointmentResource.location } as LocationResource),
                        staff = Staff.from(resources.find { it.type == ResourceType.STAFF && it.id == appointmentResource.staff } as StaffResource),
                        reason = appointmentResource.reason,
                        checkInStatus = appointmentResource.checkInStatus
                    )
                },
                contacts = resources.filterIsInstance<ContactResource>().map { contactResource ->
                    Contact(
                        id = contactResource.id,
                        givenName = contactResource.givenName,
                        familyName = contactResource.familyName,
                        contactMethod = contactResource.contactMethod,
                        language = contactResource.language,
                        email = contactResource.email,
                        phone = contactResource.phone
                    )
                },
                activity = engagement.activity.toList()
            )
        }
    }
}