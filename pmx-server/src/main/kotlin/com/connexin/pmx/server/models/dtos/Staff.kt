package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.StaffResource
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "A practice staff member."
)
data class Staff(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The staff member's ID in the customer system."
    )
    val id: String,
    @Schema(
        description = "The staff member's name as it should be displayed."
    )
    val name: String
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(Staff::id).isNotBlank()
            validate(Staff::customerId).isNotBlank()
            validate(Staff::name).isNotBlank()
        }
    }

    companion object {
        fun from(resource: StaffResource?): Staff? {
            if (resource == null || resource.id.isBlank()) return null
            return Staff(
                id = resource.id,
                name = resource.name
            )
        }
    }
}