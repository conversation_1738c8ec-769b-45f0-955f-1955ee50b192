package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.PmxMessage

data class RespondContext(
    val payload: String,
    val type: MessageType,
    val remoteId: String? = null,
    val decodedPayload: Any? = null,
    val message: PmxMessage? = null,
    val receivedMessage: ReceivedMessage? = null,
    val responseCategory: ResponseCategory? = null
) {
    enum class ResponseCategory {
        UNKNOWN,
        CONFIRM,
        DECLINE,
        OPT_IN,
        OPT_OUT,
        HELP
    }

    data class ReceivedMessage(
        val from: String,
        val text: String
    )
}
