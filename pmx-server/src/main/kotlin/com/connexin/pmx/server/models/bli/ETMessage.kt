package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Orders")
data class ETMessages(   // email messages (single)

    @JacksonXmlProperty(localName = "Order")
    @JacksonXmlElementWrapper(useWrapping = false)
    var order: ETMessage

)

@JsonRootName("Order")
data class ETMessage(

    @JacksonXmlProperty(isAttribute = true, localName = "Type")
    var type: String?,

    @JacksonXmlProperty(localName = "EmailTo")
    var emailTo: String?,

    @JacksonXmlProperty(localName = "EmailFrom")
    var emailFrom: String?,

    @JacksonXmlProperty(localName = "DisplayName")
    var displayName: String?,

    @JacksonXmlProperty(localName = "EmailReplyTo")
    var emailReplyTo: String?,

    @JacksonXmlProperty(localName = "EmailSubject")
    var emailSubject: String?,

    @JacksonXmlProperty(localName = "HtmlFile")
    var htmlFile: String?,

    @JacksonXmlProperty(localName = "HtmlID")
    var htmlID: String?,

    @JacksonXmlProperty(localName = "HTMLBinary")
    var htmlBinary: String?,

    @JacksonXmlProperty(localName = "TextFile")
    var textFile: String?,

    @JacksonXmlProperty(localName = "TextID")
    var textID: String?,

    @JacksonXmlProperty(localName = "TextBinary")
    var textBinary: String?,

    @JacksonXmlProperty(localName = "RtfFile")
    var rtfFile: String?,

    @JacksonXmlProperty(localName = "RtfID")
    var rtfID: String?,

    @JacksonXmlProperty(localName = "EnrichedFile")
    var enrichedFile: String?,

    @JacksonXmlProperty(localName = "EnrichedID")
    var enrichedID: String?,

    @JacksonXmlProperty(localName = "XmlFile")
    var xmlFile: String?,

    @JacksonXmlProperty(localName = "XmlID")
    var xmlID: String?,

    @JacksonXmlProperty(localName = "ReplaceLink")
    var replaceLink: String?,

    @JacksonXmlProperty(localName = "IsForward")
    var isForward: String?,

    @JacksonXmlProperty(localName = "IsUnsubscribe")
    var isUnsubscribe: String?,

    @JacksonXmlProperty(localName = "Attachments")
    var etAttachments: List<ETAttachment> = ArrayList()

)

@JsonRootName("Attachment")
data class ETAttachment(

    @JacksonXmlProperty(localName = "AttachmentID")
    var attachmentID: String?,

    @JacksonXmlProperty(localName = "AttachmentName")
    var attachmentName: String?,

    @JacksonXmlProperty(localName = "AttachmentBinary")
    var attachmentBinary: String?

)