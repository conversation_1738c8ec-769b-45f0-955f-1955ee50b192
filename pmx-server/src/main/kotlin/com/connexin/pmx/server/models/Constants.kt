package com.connexin.pmx.server.models

object Constants {
    const val X_OPMED = "x-opmed"
    const val BEARER_AUTH = "bearerAuth"

    const val TYPE_PROPERTY = "type"
    const val STAFF_RESOURCE = "staff"
    const val LOCATION_RESOURCE = "location"
    const val APPOINTMENT_RESOURCE = "appointment"
    const val PATIENT_RESOURCE = "patient"
    const val CONTACT_RESOURCE = "contact"
    const val PRACTICE_RESOURCE = "practice"

    const val CONFIRMATION_RESPONSE = "confirmation"
    const val CHECK_IN_RESPONSE = "check-in"
    const val CANCELLATION_RESPONSE = "cancellation"
    const val ERROR_RESPONSE = "error"
    const val COMPLETE_RESPONSE = "complete"
    const val CONTACT_UNREACHABLE_RESPONSE = "contact-unreachable"
}