package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.annotation.TraceExecutionTime
import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.EngagementStatus
import com.connexin.pmx.server.models.Resource
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.core.FindAndModifyOptions
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant

interface EngagementRepository: MongoRepository<Engagement, String>, CustomizedEngagementRepository<Engagement, String> {
    @org.springframework.data.mongodb.repository.Query(value = "{ 'nextCheckpoint': { '\$lte':  ?0 }, 'status': { '\$in': ?1} }")
    @TraceExecutionTime
    fun findAwaitingCheckpoint(now: Instant, includeStatus: Set<EngagementStatus>, pageable: Pageable): List<Engagement>

    @TraceExecutionTime
    fun findByCustomerId(customerId: String, pageable: Pageable): Page<Engagement>

    @org.springframework.data.mongodb.repository.Query(value = "{ 'customerId': ?0, 'resources': { '\$elemMatch': { '_id':  { '\$in': ?1}, '_class': ?2 } } }", exists = true)
    @TraceExecutionTime
    fun existsByResources(customerId: String, resourceIds: Set<String>, resourceClass: String): Boolean

    @org.springframework.data.mongodb.repository.Query(value = "{ 'customerId': ?0, 'resources': { '\$elemMatch': { '_id': ?1, '_class': ?2 } } }")
    @TraceExecutionTime
    fun findByResource(customerId: String, resourceId: String, resourceClass: String, pageable: Pageable): List<Engagement>
}

interface CustomizedEngagementRepository<T, ID> {
    fun <S : T> save(entity: S): S

    fun updateFields(id: ID, fields: Map<String, Any?>): T?

    fun <R:Resource> updateResources(customerId: String, now: Instant, resource: R): Long
}

@Repository
class CustomizedEngagementRepositoryImpl(
    private val template: MongoTemplate
): CustomizedEngagementRepository<Engagement, String> {
    @TraceExecutionTime
    override fun <S : Engagement> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        entity!!.createdAt = entity.createdAt ?: Instant.now()
        entity.updatedAt = Instant.now()

        return template.save(entity)
    }

    @TraceExecutionTime
    override fun updateFields(id: String, fields: Map<String, Any?>): Engagement? {
        val query = Query.query(Criteria.where("_id").`is`(id))

        val update = Update()
        fields.forEach { update.set(it.key, it.value) }
        return template.findAndModify(
            query,
            update,
            FindAndModifyOptions().returnNew(true),
            Engagement::class.java
        )
    }

    @TraceExecutionTime
    override fun <R : Resource> updateResources(customerId: String, now: Instant, resource: R): Long {
        val query = Query.query(
            Criteria.where("customerId").`is`(customerId)
                .and("resources").elemMatch(
                    Criteria.where("_class").`is`(resource.javaClass.name)
                        .and("_id").`is`(resource.id)
                )
                .and("eventDate").gte(now)
        )

        val matchedIdentifier = "resource"
        val result = template.updateMulti(
            query,
            Update().filterArray(
                Criteria.where("$matchedIdentifier._id").`is`(resource.id)
                    .and("$matchedIdentifier._class").`is`(resource.javaClass.name)
            ).set("resources.$[$matchedIdentifier]", resource),
            Engagement::class.java
        )

        return result.modifiedCount
    }
}