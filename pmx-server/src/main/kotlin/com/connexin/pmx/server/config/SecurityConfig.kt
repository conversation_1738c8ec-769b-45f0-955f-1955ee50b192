package com.connexin.pmx.server.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.userdetails.User
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.provisioning.InMemoryUserDetailsManager
import org.springframework.security.web.SecurityFilterChain


@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
class SecurityConfig {
    @Value("\${op.pmx.admin-password}")
    lateinit var adminPassword: String

    @Bean
    fun userDetailsService(auth: AuthenticationManagerBuilder, encoder: PasswordEncoder): UserDetailsService {
        val manager = InMemoryUserDetailsManager()
        manager.createUser(
            User.withUsername("admin")
                .password(encoder.encode(adminPassword))
                .roles("USER", "ADMIN")
                .authorities("admin.customers:read", "admin.customers:write", "admin.templates:read", "admin.templates:write", "admin.stats:read")
                .build()
        )

        return manager
    }

    @Bean
    fun legacyFilterChain(http: HttpSecurity): SecurityFilterChain {
        return http
            .csrf().disable()
            .authorizeRequests {
                    authorize ->
                authorize.antMatchers(*anonymousPaths).permitAll()
                authorize.antMatchers("/api/v1/**", "/api/v2/admin/**").authenticated()
            }
            .httpBasic()
            .and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and().build()
    }

    // TODO: Replace BASIC auth with JWTs obtained from an OP staff realm
//    @Bean
//    fun oAuthFilterChain(http: HttpSecurity): SecurityFilterChain {
//        return http
//            .csrf().disable()
//            .authorizeRequests { authorize ->
//                authorize.antMatchers(*anonymousPaths).permitAll()
//                authorize.anyRequest().authenticated()
//            }
//            .oauth2ResourceServer { oauth2 ->
//                oauth2.jwt()
//            }
//            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
//            .and().build()
//    }

    @Bean
    fun encoder(): PasswordEncoder {
        return BCryptPasswordEncoder()
    }

    companion object {
        private val anonymousPaths = arrayOf(
            "/api/v2/responses/**",
            "/api/v2/engagements/**",
            "/api/v2/customer/**",
            "/api/v2/templates/**",
            "/actuator/**",
            "/PostAPI/**",
            "/webhooks/**",
            "/unsubscribe",
            "/unsubscribed",
            "/confirm",
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/favicon.ico"
        )
    }
}