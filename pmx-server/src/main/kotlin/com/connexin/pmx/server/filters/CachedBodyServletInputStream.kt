package com.connexin.pmx.server.filters

import java.io.ByteArrayInputStream
import java.io.IOException
import java.io.InputStream
import javax.servlet.ReadListener
import javax.servlet.ServletInputStream


class CachedBodyServletInputStream(cachedBody: ByteArray?) : ServletInputStream() {
    private val cachedBodyInputStream: InputStream
    override fun isFinished(): <PERSON><PERSON><PERSON> {
        try {
            return cachedBodyInputStream.available() == 0
        } catch (e: IOException) {
            // TODO Auto-generated catch block
            e.printStackTrace()
        }
        return false
    }

    override fun isReady(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun setReadListener(readListener: ReadListener) {
        throw UnsupportedOperationException()
    }

    @Throws(IOException::class)
    override fun read(): Int {
        return cachedBodyInputStream.read()
    }

    init {
        cachedBodyInputStream = ByteArrayInputStream(cachedBody)
    }
}