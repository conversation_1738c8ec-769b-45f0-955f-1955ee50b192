package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.PmxEmailTags
import com.connexin.pmx.server.models.aws.EmailEvent
import com.connexin.pmx.server.models.MdcKeys.MDC_MESSAGE_ID
import com.connexin.pmx.server.services.MessageDispatcherFacade
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/webhooks/pmx")
class PmxWebhooksController(
    val dispatcher: MessageDispatcherFacade,
    private val objectMapper: ObjectMapper
) {
    @PostMapping("voice/incoming")
    fun handleIncoming(@RequestBody json: String): ResponseEntity<Any?> {
        log.debug("Received voice incoming webhook: {}", json)

        val result = dispatcher.respond(MessageType.VOICE, json)

        // always respond with OK so vendor doesn't keep trying to deliver the webhook
        return ResponseEntity.ok(result.payload)
    }

    @PostMapping("/messaging/status")
    fun status(@RequestBody json: String): ResponseEntity<Any?> {
        log.debug("Received messaging status webhook: {}", json)

        val result = dispatcher.respond(MessageType.SMS, json)

        return ResponseEntity.ok(result.payload)
    }

    @PostMapping("email/events")
    fun handleEvent(@RequestBody payload: String): ResponseEntity<Void> {
        log.debug("POST to /webhooks/email/events with payload: {}", payload)

        val event = EmailEvent.parse(payload, objectMapper)
        if (event != null) {
            try {
                val tags = event.mail.tags
                if (tags.containsKey(PmxEmailTags.PMX_MESSAGE_ID_TAG)) {
                    MDC.put(MDC_MESSAGE_ID, tags[PmxEmailTags.PMX_MESSAGE_ID_TAG]!!.first())
                }
                val messageType = tags[PmxEmailTags.PMX_MESSAGE_TYPE_TAG]?.first()
                if (messageType != null) {
                    dispatcher.respond(MessageType.valueOf(messageType), payload)
                } else {
                    log.warn("Could not determine message type from event payload")
                }
            } catch (ex: Exception) {
                log.error("", ex)
            } finally {
                MDC.clear()
            }
        }

        return ResponseEntity.noContent().build()
    }

    companion object {
        private val log = LoggerFactory.getLogger(PmxWebhooksController::class.java)
    }
}