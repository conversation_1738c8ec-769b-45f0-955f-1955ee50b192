package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.PmxMessage
import com.connexin.pmx.server.models.dtos.BeforeRespondResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult

/**
 * A base interface for all message dispatchers.
 */
interface MessageDispatcher {
    /**
     * Initiates sending a message to a patient.
     * @param message Message to initiate dispatching
     * @param customer The customer the message belongs to
     */
    fun initiate(message: PmxMessage, customer: Customer): InitiateResult

    /**
     * Responds to the result of an initiate call or, in the case of IVR, another respond call.
     * In the case of SMS, it modifies the message status based on the event type of  context
     * @param context Context containing payload, message, and other contextual information to process response.
     */
    fun respond(context: RespondContext): RespondResult

    /**
     * Allows a dispatcher to inspect and act if necessary before responding, e.g. filter out spam, etc.
     * @param context The response context.
     * @return The result of the inspection.
     */
    fun beforeRespond(context: RespondContext): BeforeRespondResult
}