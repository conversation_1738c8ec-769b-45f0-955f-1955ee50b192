package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.SmsConsent
import com.connexin.pmx.server.repositories.SmsConsentRepository
import com.connexin.pmx.server.services.SmsConsentService
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class SmsConsentServiceImpl(
    private val repository: SmsConsentRepository
) : SmsConsentService{
    override fun save(model: SmsConsent): SmsConsent {
        return repository.save(model)
    }

    override fun delete(model: SmsConsent) {
        repository.delete(model)
    }

    override fun deleteByPhoneNumber(phone: String) {
        repository.deleteById(phone)
    }

    override fun findByPhoneNumber(phone: String): SmsConsent? {
        return repository.findByIdOrNull(phone)
    }

    override fun findByRelatedMessageId(messageId: String): SmsConsent? {
        return repository.findByRelatedMessageId(messageId).orElse(null)
    }
}