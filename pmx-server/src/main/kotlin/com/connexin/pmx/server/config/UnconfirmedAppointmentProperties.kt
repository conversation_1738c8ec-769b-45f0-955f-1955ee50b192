package com.connexin.pmx.server.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding

/**
 * Configuration properties for unconfirmed appointment behavior.
 */
@ConstructorBinding
@ConfigurationProperties("op.pmx.unconfirmed-appointment")
data class UnconfirmedAppointmentProperties(
    /**
     * The number of hours before an appointment when unconfirmed appointments
     * should skip the check-in phase and go directly to reminders.
     * This only applies when check-in is NOT enabled for the customer.
     * Default: 24 hours
     */
    val skipCheckinHoursThreshold: Long = 24
)
