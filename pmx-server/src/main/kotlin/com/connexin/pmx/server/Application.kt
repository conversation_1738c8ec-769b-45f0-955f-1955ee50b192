package com.connexin.pmx.server

import com.connexin.pmx.server.filters.CorrelationFilter
import com.connexin.pmx.server.filters.LogRequestTimeFilter
import com.connexin.urlformattingtool.config.UrlShortenerConfig
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.core.Ordered
import java.time.Clock

@SpringBootApplication
@EnableConfigurationProperties
@Import(UrlShortenerConfig::class)
class Application {
    @Bean
    @ConditionalOnMissingBean(Clock::class)
    fun clock(): Clock = Clock.systemDefaultZone()

    @Bean
    fun correlationFilter(): FilterRegistrationBean<CorrelationFilter> {
        val registration =
            FilterRegistrationBean<CorrelationFilter>()
        registration.filter = CorrelationFilter()
        registration.order = Ordered.HIGHEST_PRECEDENCE + 1
        return registration
    }

    @Bean
    fun logRequestTimeFilter(): FilterRegistrationBean<LogRequestTimeFilter> {
        val registration =
            FilterRegistrationBean<LogRequestTimeFilter>()

        registration.filter = LogRequestTimeFilter()
        registration.order = Ordered.HIGHEST_PRECEDENCE + 2
        return registration
    }
}

fun main(args: Array<String>) {
    runApplication<Application>(*args)
}
