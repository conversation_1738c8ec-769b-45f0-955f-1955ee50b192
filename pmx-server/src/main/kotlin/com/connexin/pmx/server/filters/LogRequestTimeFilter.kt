package com.connexin.pmx.server.filters

import org.slf4j.LoggerFactory
import java.io.IOException
import java.time.Duration
import java.time.Instant
import javax.servlet.*
import javax.servlet.http.HttpServletRequest


class LogRequestTimeFilter : Filter {

    @Throws(IOException::class, ServletException::class)
    override fun doFilter(req: ServletRequest, resp: ServletResponse?, chain: Filter<PERSON>hain) {
        val start: Instant = Instant.now()
        val uri = (req as HttpServletRequest).requestURI
        log.trace("{}: started at {}", uri, start)
        try {
            chain.doFilter(req, resp)
        } finally {
            val finish: Instant = Instant.now()
            val time: Long = Duration.between(start, finish).toMillis()
            log.trace("{}: finished at {}, took {} ms ", uri, finish, time)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(LogRequestTimeFilter::class.java)
    }
}