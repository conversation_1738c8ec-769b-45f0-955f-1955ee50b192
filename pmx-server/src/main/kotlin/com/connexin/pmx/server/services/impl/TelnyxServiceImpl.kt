package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.telnyx.*
import com.connexin.pmx.server.services.TelnyxService
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.model.CreateMessageRequest
import kong.unirest.HttpResponse
import kong.unirest.Unirest
import org.json.JSONException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.net.URL
import java.util.UUID

@Service
class TelnyxServiceImpl(
    @Value("\${telnyx.api-key}") private val apiKey: String
): TelnyxService {
    override fun createBillingGroup(name: String): BillingGroupResponse.BillingGroupResult {
        val res = Unirest.post(BILLING_GROUP_URL.toString())
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "Bearer $apiKey"
            ))
            .body(CreateBillingGroupRequest(name = name))
            .asObject(BillingGroupResponse::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun getBillingGroup(id: UUID): BillingGroupResponse.BillingGroupResult {
        val res = Unirest.get("$BILLING_GROUP_URL/$id")
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "Bearer $apiKey"
            ))
            .asObject(BillingGroupResponse::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun deleteBillingGroup(id: UUID): BillingGroupResponse.BillingGroupResult {
        val res = Unirest.delete("$BILLING_GROUP_URL/$id")
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "Bearer $apiKey"
            ))
            .asObject(BillingGroupResponse::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun searchTollFreeNumbers(): List<SearchPhoneNumberResponse.PhoneNumber> {
        val res = Unirest.get(AVAILABLE_PHONE_NUMBERS_URL.toString())
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "$BEARER $apiKey"
            ))
            .queryString(mapOf(
                COUNTRY_CODE_FILTER to US,
                PHONE_NUMBER_TYPE_FILTER to TOLL_FREE,
                FEATURES_FILTER to FEATURES,
                EXCLUDE_HELD_NUMBERS_FILTER to EXCLUDE,
                LIMIT_FILTER to LIMIT
            ))
            .asObject(SearchPhoneNumberResponse::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun createCallControlConnection(
        name: String,
        webhookUrl: String,
        outboundVoiceProfileId: String
    ): CallControlApplicationRequest.CallControlApplication {
        val res = Unirest.post(CALL_CONTROL_APPLICATIONS_URL.toString())
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "$BEARER $apiKey"
            ))
            .body("{ \"active\": true, \"application_name\": \"$name\", \"webhook_api_version\": \"2\", \"webhook_event_url\": \"$webhookUrl\", \"outbound\": { \"outbound_voice_profile_id\": \"$outboundVoiceProfileId\" } }")
            .asObject(CallControlApplicationRequest::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun getCallControlConnection(id: String): CallControlApplicationRequest.CallControlApplication {
        val res = Unirest.get("$CALL_CONTROL_APPLICATIONS_URL/$id")
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "$BEARER $apiKey"
            ))
            .asObject(CallControlApplicationRequest::class.java)

        verifyResponse(res)

        return res.body.data
    }

    override fun deleteCallControlConnection(id: String): CallControlApplicationRequest.CallControlApplication {
        val res = Unirest.delete("$CALL_CONTROL_APPLICATIONS_URL/$id")
            .headers(mapOf(
                CONTENT_TYPE to APPLICATION_JSON,
                ACCEPT to APPLICATION_JSON,
                AUTHORIZATION to "$BEARER $apiKey"
            ))
            .asObject(CallControlApplicationRequest::class.java)

        verifyResponse(res)

        return res.body.data
    }

    /**
     * Creates an SMS or MMS message and sends it via Telnyx Messaging, returns the message ID that was created.
     * @param request The request object describing the message that will be created
     * @return The ID of the message that was created
     * @exception ApiException A non-success HTTP status code was returned by the server
     * @exception JSONException Could not find the message ID in the response payload
     */
    override fun createMessage(request: CreateMessageRequest): String {
        val res = Unirest.post(MESSAGES_URL.toString())
            .headers(
                mapOf(
                    CONTENT_TYPE to APPLICATION_JSON,
                    ACCEPT to APPLICATION_JSON,
                    AUTHORIZATION to "$BEARER $apiKey"
                )
            )
            .body(request)
            .asJson()

        verifyResponse(res)

        try {
            return res.body.`object`.getJSONObject("data").getString("id")
        } catch (ex: Exception) {
            log.error("Unable to parse createMessage response: {} {} {}", res.status, res.statusText, res.body, ex)
            throw ApiException(
                INVALID_RESPONSE,
                ex,
                res.status,
                res.headers.all().groupBy({ it.name }, {it.value}),
                res.body.toString()
            )
        }
    }

    private fun <T> verifyResponse(response: HttpResponse<T>) {
        if (!response.isSuccess) {
            val headers = response.headers.all().groupBy({ it.name }, {it.value})
            val originalBody = response.mapError(String::class.java)

            // handles the case where we get a successful response status from telnyx,
            // but we can't parse the response
            if (response.parsingError.isPresent) {
                val ex = response.parsingError.get()

                throw ApiException(
                    INVALID_RESPONSE,
                    ex,
                    response.status,
                    headers,
                    originalBody
                )
            }

            // handles the case where we don't get a successful response status from telnyx
            throw ApiException(
                response.status,
                response.statusText,
                response.headers.all().groupBy({ it.name }, {it.value}),
                response.mapError(String::class.java)
            )
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(TelnyxServiceImpl::class.java)
        private const val INVALID_RESPONSE = "Could not parse response from vendor"
        private const val APPLICATION_JSON = "application/json"
        private const val CONTENT_TYPE = "Content-Type"
        private const val ACCEPT = "Accept"
        private const val AUTHORIZATION = "Authorization"
        private const val BEARER = "Bearer"
        private const val COUNTRY_CODE_FILTER = "filter[country_code]"
        private const val PHONE_NUMBER_TYPE_FILTER = "filter[phone_number_type]"
        private const val FEATURES_FILTER = "filter[features]"
        private const val EXCLUDE_HELD_NUMBERS_FILTER = "filter[exclude_held_numbers]"
        private const val LIMIT_FILTER = "filter[limit]"
        private const val US = "US"
        private const val TOLL_FREE = "toll_free"
        private const val FEATURES = "sms,mms,voice,fax"
        private const val EXCLUDE = "true"
        private const val LIMIT = "10"
        val TELNYX_API_URL = URL("https://api.telnyx.com/")
        val BILLING_GROUP_URL = URL(TELNYX_API_URL, "/v2/billing_groups")
        val OUTBOUND_PROFILE_URL = URL(TELNYX_API_URL, "/v2/outbound_voice_profiles")
        val AVAILABLE_PHONE_NUMBERS_URL = URL(TELNYX_API_URL, "/v2/available_phone_numbers")
        val CALL_CONTROL_APPLICATIONS_URL = URL(TELNYX_API_URL, "/v2/call_control_applications")
        val MESSAGES_URL = URL(TELNYX_API_URL, "/v2/messages")
    }
}