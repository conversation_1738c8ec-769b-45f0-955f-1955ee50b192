package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    enumAsRef = true
)
enum class ResourceType(val value: String) {
    LOCATION(Constants.LOCATION_RESOURCE),
    STAFF(Constants.STAFF_RESOURCE),
    CONTACT(Constants.CONTACT_RESOURCE),
    PATIENT(Constants.PATIENT_RESOURCE),
    APPOINTMENT(Constants.APPOINTMENT_RESOURCE),
    PRACTICE(Constants.PRACTICE_RESOURCE)
}