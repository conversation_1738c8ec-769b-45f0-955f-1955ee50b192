package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.services.WebhookDeduper
import com.connexin.pmx.server.utils.CacheKeys
import org.springframework.cache.Cache
import org.springframework.cache.CacheManager
import org.springframework.stereotype.Service

@Service
class WebhookDeduperImpl(private val cacheManager: CacheManager): WebhookDeduper {

    val webhooksCache: Cache? = cacheManager.getCache(CacheKeys.webhooks)

    override fun isDuplicate(id: String): Boolean {
        val duplicateEventId = webhooksCache?.get(id)
        return if (duplicateEventId != null) {
            true
        } else {
            webhooksCache?.putIfAbsent(id, id)
            false
        }
    }

    override fun clearCache() {
        val webhooksCache: Cache? = cacheManager.getCache(CacheKeys.webhooks)
        webhooksCache?.clear()
    }
}