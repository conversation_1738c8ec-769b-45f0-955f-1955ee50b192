package com.connexin.pmx.server.models

enum class SubstitutionTokens(val token: String) {
    CONFIRMATION_LINK("<Confirmation_Link>"),
    APPOINTMENT_REASON("<Appointment_Reason>"),
    APPOINTMENT_DATE("<Appointment_Date>"),
    APPOINTMENT_DAY_DATE("<Appointment_Day_Date>"),
    APPOINTMENT_TIME("<Appointment_Time>"),
    APPOINTMENT_DAY_DATE_VM("<Appointment_Day_DateVM>"),
    APPOINTMENT_DATE_VM("<Appointment_DateVM>"),
    PATIENT_LAST_NAME("<Patient_Last_Name>"),
    PATIENT_FIRST_NAME("<Patient_First_Name>"),
    PROVIDER("<Provider>"),
    APPOINTMENT_LOCATION("<Appointment_Location>"),
    LOCATION_TELEPHONE("<Location_Telephone>"),
    RECALL_REASON("<Recall_Reason>"),
    PRACTICE_NAME("<Practice_Name>"),
    LOCATION_ADDRESS("<Location_Address>"),
    CHECK_IN_LINK("<Check_In_Link>"),
    PIN_CODE("<Pin_Code>"),
    SURVEY_LINK("<Survey_Link>")
}