package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.config.TelnyxProperties
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Language
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.dtos.BeforeRespondResult
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.services.impl.TelnyxConstants.CALL_CONTROL_ID
import com.connexin.pmx.server.services.impl.TelnyxConstants.CALL_INITIATED
import com.connexin.pmx.server.services.impl.TelnyxConstants.DIRECTION_PATH
import com.connexin.pmx.server.services.impl.TelnyxConstants.EVENT_ID
import com.connexin.pmx.server.services.impl.TelnyxConstants.EVENT_TYPE_PATH
import com.connexin.pmx.server.services.impl.TelnyxConstants.INCOMING
import com.connexin.pmx.server.services.impl.TelnyxConstants.PAYLOAD_PATH
import com.connexin.pmx.server.utils.Base64Util
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.api.CallCommandsApi
import com.telnyx.sdk.model.*
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.Instant
import java.util.*
import java.util.stream.StreamSupport

class TelnyxVoiceMessageDispatcher(
    private val api: CallCommandsApi,
    private val mapper: ObjectMapper,
    private val uuid: UUIDSource,
    private val urlGenerator: UrlGenerator,
    private val callTimeout: Int,
    private val callTimeLimit: Int,
    private val deduper: WebhookDeduper,
    private val pmxMessageService: PmxMessageService,
    private val telnyxEngagementProperties: TelnyxProperties.Engagement
) : VoiceMessageDispatcher {
    override fun initiate(message: PmxMessage, customer: Customer): InitiateResult {
        val language = message.language ?: Language.ENGLISH
        val request = CallRequest()
        request.commandId = uuid.randomUUIDString()
        request.to = message.to!!
        request.from = when {
            message.engagementId != null && language == Language.ENGLISH -> telnyxEngagementProperties.englishVoiceNumbers
            message.engagementId != null && language == Language.SPANISH -> telnyxEngagementProperties.spanishVoiceNumbers
            else -> customer.telnyxConfig.getNumber(message.type)!!.phoneNumber
        }
        request.connectionId = when {
            message.engagementId != null -> telnyxEngagementProperties.callControlConnectionId
            else -> customer.telnyxConfig.callControlConnectionId!!
        }
        request.clientState = Base64Util.encode("init")
        request.timeoutSecs = callTimeout // wait n minutes for someone to answer before hanging up
        request.timeLimitSecs =
            callTimeLimit // maximum limit of n hour for any call, including calls where the user asks to be transferred to the practice.
        request.answeringMachineDetection = CallRequest.AnsweringMachineDetectionEnum.DETECT_WORDS // TODO: support other detection types once we can set to PREMIUM
        request.webhookUrl = urlGenerator.pmxVoiceWebhook().toString()
        request.webhookUrlMethod = CallRequest.WebhookUrlMethodEnum.POST

        try {
            val response = api.dialCall(request)

            if (response.data?.callControlId != null) {
                log.info(
                    "Initiated call {} to {} for message {}",
                    response.data!!.callControlId,
                    message.to,
                    message.id
                )
                return InitiateResult(success = true, remoteId = response.data!!.callControlId)

            }

            // This shouldn't happen. We should either get 200 with a callControlId or a failure response code.
            log.error("Call was not initiated and no reason was provided for message {}", message.id)
            return InitiateResult(success = false)
        } catch (ex: ApiException) {
            if (retryableStatusCodes.contains(ex.code) || bodyContainsRetryableError(ex.responseBody)) {
                log.warn("Received a {} limit when attempting to dial message {}, will retry in 5 minutes", if(ex.code == 429) "rate" else "concurrency", ex.message)
                return InitiateResult(
                    success = false,
                    retry = true,
                    retryDelay = Duration.ofMinutes(5),
                    errors = ex.responseBody
                )
            }

            log.error(
                "The Call API returned an error {} when attempting to dial the recipient {} for message {}",
                ex.code,
                message.to,
                message.id,
                ex
            )
            return InitiateResult(success = false, errors = ex.responseBody)
        } catch (ex: Exception) {
            log.error("An unexpected error occurred trying to send a voice message to Telnyx", ex)
            return InitiateResult(success = false, errors = ex.message)
        }
    }

    private fun bodyContainsRetryableError(body: String?): Boolean {
        if (body.isNullOrBlank()) return false
        return try {
            val json = mapper.readTree(body)
            val errors = json.findValue("errors") ?: return false

            val result = StreamSupport.stream(Spliterators.spliteratorUnknownSize(errors.elements(), 0), false)
                .anyMatch {
                    val code = it.findValue("code")?.asText()
                    !code.isNullOrEmpty() && (
                            code == "90041" ||
                                    code == "90042" ||
                                    code == "90043"
                            )
                }
            return result
        } catch (ex: Exception) {
            false
        }
    }


    override fun respond(context: RespondContext): RespondResult {
        val message = context.message!!
        val json = context.decodedPayload!! as JsonNode
        val callControlId = json.findPath("payload").findPath("call_control_id").asText()
        val clientState = Base64Util.decode(json.findPath("payload").findPath("client_state").asText())
        val eventType = json.findPath("event_type").asText()
        if (callControlId != message.remoteId) {
            throw IllegalArgumentException("Message remoteId does not match the call_control_id found in the payload!")
        }

        log.debug("Received {} event, state {}, payload {}", eventType, clientState, json)

        when (eventType) {
            "call.machine.greeting.ended" -> handleMachineGreetingEnded(message, callControlId)
            "call.machine.detection.ended" -> handleAmdDetectionEnded(json, message)
            "call.answered" -> gatherUsingSpeak(callControlId, message.message, message.instructions, !message.replyTo.isNullOrEmpty(), message.language ?: Language.ENGLISH, "gather-confirm")
            "call.gather.ended" -> handleGatherEnded(message, callControlId, json)
            "call.speak.ended" -> handleSpeakEnded(clientState, message, callControlId)
            "call.hangup" -> handleHangup(message, clientState)
        }

        return RespondResult(success = true)
    }

    private fun handleMachineGreetingEnded(
        message: PmxMessage,
        callControlId: String
    ) {
        if (message.altMessage.isNullOrEmpty()) {
            log.debug("Got voicemail for message {}, but no altMessage was specified. Hanging up.", message.id)
            hangup(callControlId, "hangup-vm")
        } else {
            log.debug("Got voicemail for message {}, playing voicemail message", message.id)
            speak(callControlId, message.altMessage, message.language ?: Language.ENGLISH, "say-vm", stop = "all")
        }

        // even if we didn't play a message, mark the delivery method as voicemail so we know who we talked to
        message.voiceDeliveryMethod = VoiceDeliveryMethod.VOICE_MAIL
    }

    private fun handleAmdDetectionEnded(
        json: JsonNode,
        message: PmxMessage
    ) {
        if (json.findPath("payload").findPath("result").asText() != "machine") {
            log.debug("Someone has picked up for message {}, playing normal message", message.id)
            // indicate someone answered. if it turns out it was voicemail, we'll catch it and change the status as seen above
            message.voiceDeliveryMethod = VoiceDeliveryMethod.LIVE
        }
    }

    private fun handleSpeakEnded(
        clientState: String?,
        message: PmxMessage,
        callControlId: String
    ) {
        when (clientState) {
            "say-vm" -> {
                log.debug("Hanging up after leaving voicemail for message {}", message.id)
                hangup(callControlId, "hangup-vm")
            }
            "say-goodbye-confirm" -> {
                log.debug("Hanging up after confirming for message {}", message.id)
                hangup(callControlId, "hangup-confirm")
            }
            "say-goodbye-decline" -> {
                log.debug("Hanging up after declining for message {}", message.id)
                hangup(callControlId, "hangup-decline")
            }
            "say-xfer" -> {
                log.debug("Transferring call to {} for message {}", message.replyTo, message.id)
                transfer(callControlId, message.replyTo!!, "xfer")
            }
            "say-no-response" -> {
                hangup(callControlId, "hangup-no-response")
            }
        }
    }

    private fun handleHangup(message: PmxMessage, clientState: String?) {
        // always considered delivered if we tried to call and they hung up.
        message.status = MessageStatus.DELIVERED
        message.completedAt = Instant.now()
        when (clientState) {
            "hangup-vm" -> {
                log.info("Left voicemail and ended call for message {}", message.id)
                markNoResponse(message)
            }
            "hangup-confirm" -> {
                log.info("Confirmed appointment and ended call for message {}", message.id)
            }
            "hangup-decline" -> {
                log.info("Declined appointment and ended call for message {}", message.id)
            }
            "xfer" -> {
                log.info("Call was ended after transferring to practice for message {}", message.id)
            }
            else -> {
                log.info(
                    "Callee hung up without confirming, transferring, or leaving a voice mail for message {}",
                    message.id
                )
                markNoResponse(message)
                if (message.voiceDeliveryMethod == VoiceDeliveryMethod.UNKNOWN) {
                    message.voiceDeliveryMethod = VoiceDeliveryMethod.HANGUP
                }
            }
        }
    }

    private fun markNoResponse(message: PmxMessage) {
        if (message.confirmationStatus != ConfirmationStatus.CONFIRMED && message.confirmationStatus != ConfirmationStatus.DECLINED) {
            message.confirmationStatus = ConfirmationStatus.NO_RESPONSE
        }
    }

    private fun handleGatherEnded(message: PmxMessage, callControlId: String, json: JsonNode) {
        val status = json.findPath("payload").findPath("status").asText()
        if (status == "call_hangup" || status == "cancelled" || status == "cancelled_amd") {
            // we hit their voicemail or call was disconnected while other commands were running,
            // so just ignore and move on
            return
        }
        val language = message.language ?: Language.ENGLISH
        val digits = json.findPath("payload").findPath("digits").asText()
        if (!digits.isNullOrBlank()) {
            message.voiceDeliveryMethod = VoiceDeliveryMethod.LIVE
        }
        // PMX will want to know what digits the user pressed during the session, so just store it
        message.responseData = if (message.responseData.isNullOrEmpty()) digits else message.responseData.plus(digits)
        when (digits) {
            "1" -> {
                log.debug("Callee has confirmed appointment for message {}", message.id)
                message.confirmationStatus = ConfirmationStatus.CONFIRMED

                speak(
                    callControlId,
                    LocalizedStrings[language]!![LOCALIZED_STRING_CONFIRM_GOODBYE]!!,
                    language,
                    "say-goodbye-confirm",
                    stop = "all"
                )
            }
            "2" -> {
                if (message.engagementId != null) {
                    log.debug("Callee has declined appointment for message {}", message.id)
                    message.confirmationStatus = ConfirmationStatus.DECLINED

                    speak(
                        callControlId,
                        LocalizedStrings[language]!![LOCALIZED_STRING_DECLINE_GOODBYE]!!,
                        language,
                        "say-goodbye-decline",
                        stop = "all"
                    )
                } else {
                    log.debug(
                        "Callee has asked to be transferred to {} for message {}, playing hold message",
                        message.replyTo,
                        message.id
                    )
                    speak(
                        callControlId,
                        "Transferring your call. Please hold.",
                        language, "say-xfer",
                        stop = "all"
                    )
                }
            }
            "3" -> {
                log.debug("Callee asked the choices to be repeated for message {}", message.id)
                gatherUsingSpeak(callControlId, message.message, message.instructions, !message.replyTo.isNullOrEmpty(), language, "gather-confirm")
            }
            else -> {
                log.debug("Called refused to pick an option, hanging up for message {}", message.id)
                speak(
                    callControlId,
                    LocalizedStrings[language]!![LOCALIZED_STRING_NO_RESPONSE]!!,
                    language,
                    "say-no-response",
                    stop = "all"
                )
            }
        }
    }

    private fun hangup(callControlId: String, state: String) {
        val request = HangupRequest()
        request.commandId = uuid.randomUUIDString()
        request.clientState = Base64Util.encode(state)
        api.hangupCall(callControlId, request)
    }

    private fun speak(callControlId: String, text: String, language: Language, state: String, stop: String = "current") {
        val request = SpeakRequest()
        request.commandId = uuid.randomUUIDString()
        request.clientState = Base64Util.encode(state)
        request.payload = text
        request.voice = SpeakRequest.VoiceEnum.FEMALE
        request.language = if (language == Language.ENGLISH) SpeakRequest.LanguageEnum.EN_US else SpeakRequest.LanguageEnum.ES_US
        request.stop = stop

        api.speakCall(callControlId, request)
    }

    private fun gatherUsingSpeak(
        callControlId: String,
        text: String,
        instructions: String?,
        hasReplyTo: Boolean,
        language: Language?,
        state: String
    ) {
        val actualInstructions = instructions
            ?: if (hasReplyTo) "Press 1 to confirm, 2 to speak to the practice, 3 to repeat, or hang up if you do not wish to confirm."
            else "Press 1 to confirm, 3 to repeat, or hang up if you do not wish to confirm."
        val request = GatherUsingSpeakRequest()
        request.commandId = uuid.randomUUIDString()
        request.clientState = Base64Util.encode(state)
        request.payload = text // if (instructionsOnly) instructions else text
        request.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
        request.language = if (language == Language.SPANISH) GatherUsingSpeakRequest.LanguageEnum.ES_US else GatherUsingSpeakRequest.LanguageEnum.EN_US
        request.validDigits = if (hasReplyTo) "123" else "13"
        request.invalidPayload = actualInstructions
        request.maximumDigits = 1
        request.timeoutMillis = 10000
        request.maximumTries = 3
        api.gatherUsingSpeak(callControlId, request)
    }

    private fun transfer(callControlId: String, replyTo: String, state: String) {
        val request = TransferCallRequest()
        request.commandId = uuid.randomUUIDString()
        request.clientState = Base64Util.encode(state)
        request.to = replyTo
        request.targetLegClientState = Base64Util.encode("xfer-init")
        api.transferCall(callControlId, request)
    }

    private fun reject(callControlId: String) {
        val request = RejectRequest()
        request.commandId = uuid.randomUUIDString()
        request.cause = RejectRequest.CauseEnum.CALL_REJECTED
        request.clientState = Base64Util.encode(STATE_REJECT_UNSOLICITED)
        api.rejectCall(callControlId, request)
    }

    override fun beforeRespond(context: RespondContext): BeforeRespondResult {
        val jsonPayload = mapper.readTree(context.payload)
        val payloadNode = jsonPayload.at(PAYLOAD_PATH)
        val remoteId = if (payloadNode.hasNonNull(CALL_CONTROL_ID)) payloadNode.get(CALL_CONTROL_ID).asText() else null
        val eventType = jsonPayload.at(EVENT_TYPE_PATH)?.asText()
        val direction = jsonPayload.at(DIRECTION_PATH)?.asText()
        val eventId = jsonPayload.at(EVENT_ID).asText()

        if (!eventId.isNullOrEmpty() && deduper.isDuplicate(eventId)) {
            log.debug("Ignoring duplicate webhook {}", eventId)
            return BeforeRespondResult(success=true,accepted=false, context)
        }

        //Fetch data from PMXMessage using remoteId - if it's already FAILED or DELIVERED (call.speak.ended, call.hangup) skips
        if(remoteId != null) {
            val message = pmxMessageService.getByRemoteId(remoteId)
            if (message?.status == MessageStatus.FAILED || message?.status == MessageStatus.DELIVERED) {
                log.debug("Ignoring finalized webhook {}", eventType)
                return BeforeRespondResult(success = true, accepted = false, context)
            }
        }

        // hang up if the webhook event is call.initiated, is incoming, and has a call control ID
        val accepted = if (CALL_INITIATED.equals(eventType, ignoreCase = true)
            && INCOMING.equals(direction, ignoreCase = true)
            && !remoteId.isNullOrEmpty()) {
            reject(remoteId)
            false
        } else {
            !remoteId.isNullOrBlank()
        }

        return BeforeRespondResult(
            success = true,
            accepted = accepted,
            context = context.copy(decodedPayload = jsonPayload, remoteId = remoteId)
        )
    }

    companion object {
        const val STATE_REJECT_UNSOLICITED = "reject-unsolicited"
        private const val LOCALIZED_STRING_GREETING = "GREETING"
        private const val LOCALIZED_STRING_CONFIRM_GOODBYE = "CONFIRM_GOODBYE"
        private const val LOCALIZED_STRING_DECLINE_GOODBYE = "DECLINE_GOODBYE"
        private const val LOCALIZED_STRING_NO_RESPONSE = "NO_RESPONSE"

        private val log = LoggerFactory.getLogger(TelnyxVoiceMessageDispatcher::class.java)
        private val retryableStatusCodes = setOf(429, 500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511)

        private val LocalizedStrings = mapOf(
            Language.ENGLISH to mapOf(
                LOCALIZED_STRING_GREETING to "Hello. You have a message from your pediatrician.",
                LOCALIZED_STRING_CONFIRM_GOODBYE to "Thank you. Goodbye!",
                LOCALIZED_STRING_DECLINE_GOODBYE to "Your appointment has been cancelled. Goodbye!",
                LOCALIZED_STRING_NO_RESPONSE to "Sorry, I didn't get your response. If you have any questions, please contact the practice. Goodbye!"
            ),
            Language.SPANISH to mapOf(
                LOCALIZED_STRING_GREETING to "Buenos dias, Usted tiene un mensaje de su pediatra.",
                LOCALIZED_STRING_CONFIRM_GOODBYE to "Gracias. Adios!",
                LOCALIZED_STRING_DECLINE_GOODBYE to "Su cita ha sido cancelada",
                LOCALIZED_STRING_NO_RESPONSE to "lo siento, no llego su respuesta. Si tiene alguna pregunta, communiquese con la oficina. Adios!"
            )
        )
    }
}