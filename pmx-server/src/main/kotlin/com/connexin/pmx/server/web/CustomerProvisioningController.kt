package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.CustomerProvisioningOrder
import com.connexin.pmx.server.models.Errors
import com.connexin.pmx.server.models.dtos.CreateCsvOrderRequest
import com.connexin.pmx.server.models.dtos.CreateOrderRequest
import com.connexin.pmx.server.models.dtos.CreateOrderResult
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.CustomerProvisioningService
import io.swagger.annotations.ApiOperation
import io.swagger.v3.oas.annotations.Hidden
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.BufferedReader

@RestController
@RequestMapping("/api/v1/customer-provisioning")
@Hidden
@ApiOperation(value = "API for provisioning customers.", hidden = true)
class CustomerProvisioningController(
    private val provisioningService: CustomerProvisioningService
) {
    @PostMapping(
        consumes = ["application/json"],
        produces = ["application/json"]
    )
    fun createSingleOrder(@RequestBody request: CreateOrderRequest): ResponseEntity<CreateOrderResult> {
        val result = provisioningService.create(request)

        return if (result.success) ResponseEntity.ok(result) else ResponseEntity.badRequest().body(result)
    }

    @PostMapping(
        consumes = ["multipart/form-data"],
        produces = ["application/json"]
    )
    fun createCsvOrders(@RequestParam("csv") csvFile: MultipartFile): ResponseEntity<CreateOrderResult> {
        if (csvFile.isEmpty) {
            return ResponseEntity.badRequest().body(
                CreateOrderResult(
                    success = false,
                    errors = listOf(
                        ErrorDto(
                            path = "csv",
                            details = "Must not be blank",
                            message = Errors.VALIDATION_FAILED.message,
                            errorCode = Errors.VALIDATION_FAILED.code
                        )
                    )
                )
            )
        }

        val content = csvFile.inputStream.bufferedReader().use(BufferedReader::readText)
        val res = provisioningService.create(CreateCsvOrderRequest(csv = content))

        return if (res.success) ResponseEntity.ok(res) else ResponseEntity.badRequest().body(res)
    }

    @GetMapping("/{id}")
    fun getById(@PathVariable id: String): ResponseEntity<CustomerProvisioningOrder> {
        val order = provisioningService.getById(id)

        return if (order == null) ResponseEntity.notFound().build() else ResponseEntity.ok(order)
    }

    @DeleteMapping("/{id}")
    fun deleteById(@PathVariable id: String): ResponseEntity<Unit> {
        provisioningService.deleteById(id)

        return ResponseEntity.noContent().build()
    }
}