package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.dtos.DeliveryStatisticResult
import com.connexin.pmx.server.models.dtos.GetDeliveryStatisticsCriteria
import com.connexin.pmx.server.services.StatisticsService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v2/admin/stats")
@Tag(
    name = "Statistics Admin", description = "Endpoints for administrators to view statistics related to usage."
)
class AdminStatsController(
    private val statsService: StatisticsService
) {
    @Operation(
        summary = "Gets a paginated list of delivery statistics, broken down by application and message type.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A page of statistics."
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("/delivery", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority('admin.stats:read')")
    fun getDeliveryStatistics(
        @ParameterObject
        criteria: GetDeliveryStatisticsCriteria
    ): List<DeliveryStatisticResult> {
        return statsService.getDeliveryStatistics(criteria)
    }
}