package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.ConfirmationResponse
import com.connexin.pmx.server.models.ConfirmationStatus
import com.connexin.pmx.server.models.Response
import com.connexin.pmx.server.models.dtos.FindResponsesCriteria
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.data.support.PageableExecutionUtils
import org.springframework.stereotype.Repository

interface ResponseRepository: MongoRepository<Response, String>, CustomizedResponseRepository<Response, String>

interface CustomizedResponseRepository<T, ID> {
    fun findByCriteria(customerId: String, criteria: FindResponsesCriteria, pageable: Pageable): Page<Response>

    fun <R : Response> findDeclinedResponse(entity: R): R
}

@Repository
class CustomizedResponseRepositoryImpl(
    private val template: MongoTemplate
): CustomizedResponseRepository<Response, String> {
    override fun findByCriteria(
        customerId: String,
        criteria: FindResponsesCriteria,
        pageable: Pageable
    ): Page<Response> {
        val query = Query()
            .addCriteria(Criteria.where("customerId").`is`(customerId))
            .addCriteria(Criteria.where("archived").`is`(criteria.archived))

        if (criteria.engagementId != null) {
            query.addCriteria(Criteria.where("engagementId").`is`(criteria.engagementId))
        }

        if (criteria.messageId != null) {
            query.addCriteria(Criteria.where("messageId").exists(true))
                .addCriteria(Criteria.where("messageId").`is`(criteria.messageId))
        }

        if (criteria.since != null) {
            query.addCriteria(Criteria.where("occurredAt").gte(criteria.since))
        }

        val content = template.find(query.with(pageable), Response::class.java)

        return PageableExecutionUtils.getPage(
            content, pageable
        ) {
            template.count(
                Query.of(query).limit(-1).skip(-1),
                Response::class.java
            )
        }
    }

    override fun <R : Response> findDeclinedResponse(entity: R): R {
        if (entity is ConfirmationResponse && ConfirmationStatus.DECLINED.equals(entity.result)) {
            val query = Query()
                    .addCriteria(Criteria.where("engagementId").`is`(entity.engagementId))
                    .addCriteria(Criteria.where("result").`is`(ConfirmationStatus.DECLINED.toString()))

            return template.findOne(query, entity.javaClass) ?: entity
        }

        return entity;
    }
}