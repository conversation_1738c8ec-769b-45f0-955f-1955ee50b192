package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.utils.use
import io.swagger.v3.oas.annotations.media.Schema
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import java.time.Duration
import java.time.Instant

@Controller
@Schema(
    hidden = true
)
class ConfirmationController(
    private val engagementService: EngagementService,
    private val customerService: CustomerService,
    private val messageService: PmxMessageService
) {
    @GetMapping("/confirm")
    fun get(
        @RequestParam(required = false)
        id: String?,
        @RequestParam(required = false)
        contact: String?,
        model: Model
    ): String {
        if (id == null) {
            log.debug("Missing id query parameter.")
            model.addAttribute("success", false)
            return "confirm-result"
        }

        val (engagement, customer) = getEngagementAndCustomer(id, model) ?: return "confirm-result"

        val title = engagement.resources.filterIsInstance<PracticeResource>()
            .map { it.name }
            .firstOrNull()

        model.addAttribute("title", title)

        if (!canDecline(engagement, customer)) {
            return "confirm-deadline"
        }

        if (engagement.confirmationStatus == ConfirmationStatus.CONFIRMED
            || engagement.confirmationStatus == ConfirmationStatus.DECLINED
            || engagement.confirmationStatus == ConfirmationStatus.NO_RESPONSE
        ) {
            model.addAttribute("success", true)
            model.addAttribute(
                "successReason", when (engagement.confirmationStatus) {
                    ConfirmationStatus.CONFIRMED -> "confirmed"
                    else -> "declined"
                }
            )
            return "confirm-result"
        }

        return "confirm-prompt"
    }

    private fun getEngagementAndCustomer(engagementId: String, model: Model): Pair<Engagement, Customer>? {
        val engagement = engagementService.getById(engagementId)

        if (engagement == null) {
            log.warn("Engagement {} not found", engagementId)
            model.addAttribute("success", false)
            return null
        }

        val customer = customerService.getById(engagement.customerId)

        if (customer == null) {
            log.error("Customer {} not found.", engagement.customerId)
            model.addAttribute("success", false)
            return null
        }

        return Pair(engagement, customer)
    }


    @PostMapping("/confirm")
    fun update(
        @RequestParam
        id: String,
        @RequestParam
        result: String,
        @RequestParam(required = false)
        contact: String?,
        model: Model
    ): String {
        val (engagement, customer) = getEngagementAndCustomer(id, model) ?: return "confirm-result"

        val title = engagement.resources.filterIsInstance<PracticeResource>()
            .map { it.name }
            .firstOrNull()

        model.addAttribute("title", title)

        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, engagement.customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, engagement.id)
        ).use {
            if (!canDecline(engagement, customer)) {
                log.error("Cannot confirm or decline; past cancellation deadline")
                model.addAttribute("success", false)
                return "confirm-result"
            }
            try {
                val respondent = engagement.resources.filterIsInstance<ContactResource>()
                    .firstOrNull { it.id.equals(contact, ignoreCase = true) }
                val desiredStatus = when (result) {
                    "confirmed" -> ConfirmationStatus.CONFIRMED
                    else -> ConfirmationStatus.DECLINED
                }

                // try to find the most recent message we sent this recipient for the engagement
                // if we find one, we'll assume that's the message they responded to.
                val message = if (respondent != null)
                    messageService.findForEngagementIdAndContact(
                        engagementId = engagement.id!!,
                        respondent.coerceToRecipient()
                    )
                else null

                val newState = engagementService.sendEvent(
                    ConfirmationResponseEvent(
                        event = when (result) {
                            "confirmed" -> EngagementEvent.CONFIRMED
                            else -> EngagementEvent.DECLINED
                        },
                        engagement = engagement,
                        customer = customer,
                        result = desiredStatus,
                        respondents = if (respondent == null) emptySet() else setOf(respondent),
                        messageId = message?.id
                    )
                )
                if (newState.confirmationStatus != desiredStatus) {
                    log.error("An error occurred recording the contact's response. Likely culprit is that one or more required rules have been disabled by the customer.")
                    model.addAttribute("success", false)
                } else {
                    model.addAttribute("success", true)
                    model.addAttribute(
                        "successReason", when (newState.confirmationStatus) {
                            ConfirmationStatus.CONFIRMED -> "confirmed"
                            else -> "declined"
                        }
                    )
                }
            } catch (ex: Exception) {
                log.error("Unable to send confirmation response event.", ex)
                model.addAttribute("success", false)
            }
        }

        return "confirm-result"
    }

    companion object {
        private val log = LoggerFactory.getLogger(ConfirmationController::class.java)

        private fun canDecline(engagement: Engagement, customer: Customer, now: Instant = Instant.now()): Boolean {
            val cutoff = customer.cancellationDeadline ?: Duration.ZERO

            return engagement.eventDate.minus(cutoff) > now
        }
    }
}