package com.connexin.pmx.server.config

import com.connexin.pmx.server.models.Constants
import io.swagger.v3.oas.models.Components
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Contact
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.info.License
import io.swagger.v3.oas.models.security.SecurityRequirement
import io.swagger.v3.oas.models.security.SecurityScheme
import io.swagger.v3.oas.models.servers.Server
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.env.Environment

/** Provides information to the documentation generated by openapi.json */
@Configuration
class OpenApiConfig {
    @Autowired
    lateinit var env: Environment

    @Bean
    fun openApi(): OpenAPI {
        val openapi = OpenAPI()
            .info(
                Info()
                    .title("PMX+")
                    .description(
                        "PMX+ (Patient Message eXchange Plus) is a product that allows practices to engage patients using SMS, Voice, and \n" +
                                "Email to perform automated tasks such as appointment confirmation and reminders, check-in, or point-to-point \n" +
                                "communication like email campaigns, recalls, etc."
                    )
                    .contact(
                        Contact()
                            .name("Office Practicum")
                            .email("<EMAIL>")
                            .url("https://www.officepracticum.com/supporthub")
                    )
                    .license(
                        License()
                            .name("Proprietary")
                            .identifier("UNLICENSED")
                    )
                    .version("2.0.0")
            )
            // adds bearerAuth to every endpoint
            .addSecurityItem(
                SecurityRequirement()
                    .addList(Constants.BEARER_AUTH)
            )
            .components(
                Components()
                    .addSecuritySchemes(
                        Constants.BEARER_AUTH,
                        SecurityScheme()
                            .name(Constants.BEARER_AUTH)
                            .type(SecurityScheme.Type.HTTP)
                            .scheme("bearer")
                            .bearerFormat("JWT")
                            .`in`(SecurityScheme.In.HEADER)
                    )
            )

        if (!env.activeProfiles.contains("local-dev")) {
            openapi.addServersItem(
                Server()
                    .description("Development")
                    .url("https://applications-dev.op.healthcare/pmx")
            )
                .addServersItem(
                    Server()
                        .description("Production")
                        .url("https://applications.op.healthcare/pmx")
                )
        }

        return openapi
    }
}