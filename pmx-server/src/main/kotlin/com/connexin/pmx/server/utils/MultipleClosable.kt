package com.connexin.pmx.server.utils

import java.io.Closeable

inline fun <T : Closeable, R> Array<T>.use(block: () -> R): R {
    var exception: Throwable? = null
    try {
        return block()
    } catch (e: Throwable) {
        exception = e
        throw e
    } finally {
        when (exception) {
            null -> forEach { it.close() }
            else -> forEach {
                try {
                    it.close()
                } catch (closeException: Throwable) {
                    exception.addSuppressed(closeException)
                }
            }
        }
    }
}