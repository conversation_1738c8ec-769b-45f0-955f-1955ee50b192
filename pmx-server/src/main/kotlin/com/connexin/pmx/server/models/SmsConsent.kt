package com.connexin.pmx.server.models

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

enum class ConsentStatus {
    REQUESTED,
    OPTED_IN,
    OPTED_OUT
}

@Document("sms_consents")
data class SmsConsent (
    /**
     * The phone number for the consent.
     */
    @Id val id: String,
    var createdAt: Instant? = null,
    var updatedAt: Instant? = null,
    /**
     * The status of the consent.
     */
    var status: ConsentStatus = ConsentStatus.REQUESTED,
    /**
     * The original PMX message id that included opt-in language.
     */
    @Indexed(unique = true) val relatedMessageId: String? = null,
    /**
     * A history of responses received from this number that were processed as consents.
     */
    val responses: MutableList<Response> = mutableListOf()
) {
    data class Response(
        val text: String,
        val receivedAt: Instant
    )
}