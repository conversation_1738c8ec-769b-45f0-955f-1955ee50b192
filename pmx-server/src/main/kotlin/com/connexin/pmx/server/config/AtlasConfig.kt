package com.connexin.pmx.server.config

import com.connexin.atlas.sl.http.HttpService
import com.connexin.atlas.sl.http.UnirestHttpService
import com.connexin.atlas.sl.practice.AtlasPracticeFacade
import com.connexin.atlas.sl.practice.PracticeFacade
import com.connexin.atlas.sl.survey.AtlasSurveyFacade
import com.connexin.atlas.sl.survey.SurveyFacade
import com.connexin.authentication.service.ManagedToken
import com.connexin.authentication.service.impl.GenericManagedToken
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.LocalDateTime

/** Configures the facades for Atlas-Practice and Atlas-Survey */
@Configuration
class AtlasConfig {
    @Value("\${op.atlas.token-url}")
    private lateinit var tokenUrl: String

    @Value("\${op.atlas.client-id}")
    private lateinit var clientId: String

    @Value("\${op.atlas.client-secret}")
    private lateinit var clientSecret: String

    @Value("\${op.atlas.audience}")
    private lateinit var audience: String

    @Value("\${op.atlas.practice.url}")
    private lateinit var practiceUrl: String

    @Value("\${op.atlas.survey.url}")
    private lateinit var surveyUrl: String

    @Bean(name =["atlasHttpService"])
    fun atlasHttpService(): HttpService? {
        return UnirestHttpService()
    }

    @Bean(name = ["atlasManagedToken"])
    fun atlasManagedToken(): ManagedToken {
        return GenericManagedToken.Builder(tokenUrl, clientId, clientSecret)
            .setAudience(audience).build()
    }

    @Bean
    fun practiceFacade(
        @Qualifier("atlasManagedToken")
        atlasManagedToken: ManagedToken,
        @Qualifier("atlasHttpService")
        httpService: HttpService
    ): PracticeFacade {
        return AtlasPracticeFacade(httpService, practiceUrl, atlasManagedToken,false)
    }

    @Bean
    fun surveyFacade(
        @Qualifier("atlasManagedToken")
        atlasManagedToken: ManagedToken,
        @Qualifier("atlasHttpService")
        httpService: HttpService
    ): SurveyFacade {
        return AtlasSurveyFacade(httpService, surveyUrl, atlasManagedToken)
    }

    @Bean(name = ["atlasObjectMapper"])
    fun atlasObjectMapper(): ObjectMapper {
        val objectMapper = ObjectMapper()
        val javaTimeModule = JavaTimeModule()
        javaTimeModule.addDeserializer(LocalDateTime::class.java, LocalDateTimeDeserializer.INSTANCE)
        objectMapper.registerModule(javaTimeModule)
        objectMapper.registerModule(KotlinModule.Builder().build())
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        return objectMapper
    }
}