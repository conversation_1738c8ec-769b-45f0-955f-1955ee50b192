package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("PostAPIResponse")
data class PostAPIResponse (

    @JacksonXmlProperty(localName = "SaveTransactionalOrderResult")
    @JacksonXmlElementWrapper(useWrapping = false)
    var saveTransactionalOrderResult: SaveTransactionalOrderResult

)

@JsonRootName("SaveTransactionalOrderResult")
data class SaveTransactionalOrderResult (

    @JacksonXmlProperty(localName = "OrderID")
    var orderID: String? = null,

    @JacksonXmlProperty(localName = "transactionID")
    var transactionID: String? = null,

    @JacksonXmlProperty(localName = "Error")
    var error: String? = null,

    @JacksonXmlProperty(localName = "Exception")
    var exception: String? = null

)

/*
===========================================================
Samples:

<PostAPIResponse>
	<SaveTransactionalOrderResult>
		<OrderID>73087671</OrderID>
		<transactionID>8233e3f4-1d7d-ec11-ac6c-000c29335097</transactionID>
	</SaveTransactionalOrderResult>
</PostAPIResponse>

<PostAPIResponse>
	<SaveTransactionalOrderResult>
		<Exception>Get the EB Detail Report Error. Details Info:
System.Net.WebException: The remote server returned an error: (500) Internal Server Error.
   at System.Net.HttpWebRequest.GetResponse()
   at PostAPI.DataClass.Report.GetEBDetailReportByOrderID(DB db, User user, Int32 OrderID, String reportKey)
        </Exception>
	</SaveTransactionalOrderResult>
</PostAPIResponse>

For broadcast emails, the transactionID is null and the OrderID is filled in.
For all other messages, the transactionID is filled in and the OrderID is null.

*/