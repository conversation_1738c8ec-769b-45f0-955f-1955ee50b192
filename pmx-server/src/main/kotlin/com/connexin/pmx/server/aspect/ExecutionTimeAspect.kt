package com.connexin.pmx.server.aspect

import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant


@Aspect
@Component
@ConditionalOnExpression("\${op.trace-execution-time}")
class ExecutionTimeAdvice {
    @Around("@annotation(com.connexin.pmx.server.annotation.TraceExecutionTime)")
    @Throws(Throwable::class)
    fun executionTime(point: ProceedingJoinPoint): Any? {
        val name = "${point.signature.declaringTypeName}.${point.signature.name}"
        val start = Instant.now()
        log.trace("{}: started at {}", name, start)
        val result = point.proceed()
        val finish = Instant.now()
        val time = Duration.between(start, finish).toMillis()
        log.trace("{}: finished at {}, took {} ms", name, finish, time)
        return result
    }
    companion object {
        private val log = LoggerFactory.getLogger(ExecutionTimeAdvice::class.java)
    }
}