package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.EngagementEvent
import io.swagger.v3.oas.annotations.media.Schema

data class EngagementEventDto(
    @field:Schema(
        description = "Op appointment id which is related to the requested engagement"
    )
    val appointmentId: String,
    @field:Schema(
        description = "Event type that is happening in the engagement"
    )
    val engagementEventType: EngagementEvent,
)