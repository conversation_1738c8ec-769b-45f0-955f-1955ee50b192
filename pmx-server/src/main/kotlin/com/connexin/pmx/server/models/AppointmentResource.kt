package com.connexin.pmx.server.models

import java.time.Instant

data class AppointmentResource(
    override val id: String,
    val startTime: Instant,
    val reason: String,
    val location: String,
    val staff: String?,
    val patient: String,
    val appointmentType: String,
    val practice: String?,
    val cancelled: Boolean = false,
    var checkInStatus: CheckInStatus = CheckInStatus.NOT_CHECKED_IN
): Resource {
    override val type: ResourceType
        get() = ResourceType.APPOINTMENT
}
