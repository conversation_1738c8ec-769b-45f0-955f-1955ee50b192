package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.ConfirmationStatus
import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.EngagementStatus
import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    description = "Engagement fields that can be patched."
)
data class PatchEngagement(
    val status: EngagementStatus? = null,
    val confirmationStatus: ConfirmationStatus? = null,
) {
    companion object {
        fun from (engagement: Engagement): PatchEngagement {
            return PatchEngagement(
                status = engagement.status,
                confirmationStatus = engagement.confirmationStatus,
            )
        }
    }
}