package com.connexin.pmx.server.models.dtos

import org.springframework.http.HttpStatus

class Response<Result>(
    val success: <PERSON>olean,
    val status: HttpStatus,
    val result: Result? = null,
    val errors: List<ErrorDto>? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Response<*>

        if (result != other.result) return false
        if (success != other.success) return false
        if (errors != other.errors) return false

        return true
    }

    override fun hashCode(): Int {
        var result1 = result?.hashCode() ?: 0
        result1 = 31 * result1 + success.hashCode()
        result1 = 31 * result1 + (errors?.hashCode() ?: 0)
        return result1
    }

    fun get(): Result {
        return result!!
    }

    companion object {
        fun <Result> failure(status: HttpStatus, errors: List<ErrorDto>): Response<Result> {
            return failure(status, *errors.toTypedArray())
        }

        fun <Result> failure(status: HttpStatus, vararg error: ErrorDto): Response<Result> {
            return Response(success = false, status = status, errors = listOf(*error))
        }

        fun <Result> success(result: Result): Response<Result> {
            return Response(success = true, status = HttpStatus.OK, result = result)
        }
    }
}