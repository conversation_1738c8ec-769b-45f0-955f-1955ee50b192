package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.annotation.TraceExecutionTime
import com.connexin.pmx.server.exceptions.ZoneNotFoundException
import com.connexin.pmx.server.services.DateService
import com.connexin.pmx.server.services.ZipCodeService
import com.connexin.pmx.server.utils.CacheKeys
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheConfig
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service

@Service
@CacheConfig(
    cacheNames = [CacheKeys.zipCodes]
)
class ZipCodeServiceImpl(
    private val dateService: DateService
): ZipCodeService {
    /**
     * Returns the Zone ID for the specified US ZIP code.
     * @param zip The US ZIP code.
     * @return The Zone ID associated with the US ZIP code.
     */
    @Cacheable(
        key = "#zip",
        condition = "#zip != null",
        unless = "#result == null"
    )
    @TraceExecutionTime
    override fun getZoneId(zip: String): String {
        return try {
            dateService.getZoneIdForLocation(zip)?.toString()
        } catch (ex: Exception) {
            log.error("Failed to fetch timezone for zip code {}.", zip, ex)
            null
        } ?: throw ZoneNotFoundException("Unable to determine timezone for zip code $zip")
    }

    companion object {
        private val log = LoggerFactory.getLogger(ZipCodeServiceImpl::class.java)
    }
}