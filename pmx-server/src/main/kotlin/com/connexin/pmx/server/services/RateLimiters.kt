package com.connexin.pmx.server.services

import io.github.resilience4j.ratelimiter.RateLimiter
import io.github.resilience4j.ratelimiter.RateLimiterConfig
import io.github.resilience4j.ratelimiter.RateLimiterRegistry
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component


/**
 * A component that handles retrieval and registration of rate limiter instances.
 */
@Component
class RateLimiters(
    private val rateLimiterRegistry: RateLimiterRegistry
) {
    fun getByName(serviceName: String, config: RateLimiterConfig?): RateLimiter {
        return rateLimiterRegistry.find(serviceName).orElseGet {
            val rateLimiter = rateLimiterRegistry.rateLimiter(serviceName, config ?: RateLimiterConfig.ofDefaults())
            rateLimiter.eventPublisher
                .onFailure { log.warn("[rateLimiter={}, event={}]", it.rateLimiterName, it.eventType) }
            rateLimiter
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(RateLimiters::class.java)
    }
}

object RateLimiterConstants {
    const val EMAILS_PER_DAY = "emails-per-day"
    const val EMAILS_PER_SECOND = "emails-per-second"
}