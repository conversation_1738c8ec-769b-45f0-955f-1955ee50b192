package com.connexin.pmx.server.utils

import java.util.*

class Base64Util {
    companion object {
        private val encoder = Base64.getEncoder()
        private val decoder = Base64.getDecoder()

        fun decode(encoded: String?): String? {
            if (encoded.isNullOrEmpty()) return null

            return String(decoder.decode(encoded))
        }

        fun decodeToBytes(encoded: String?): ByteArray? {
            if (encoded.isNullOrEmpty()) return null

            return decoder.decode(encoded)
        }

        fun encode(decoded: String?): String? {
            if (decoded.isNullOrEmpty()) return null

            return encoder.encodeToString(decoded.toByteArray())
        }

        fun encode(decoded: ByteArray): String {
            return encoder.encodeToString(decoded)
        }
    }
}