package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.MessageApplication
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.dtos.DeliveryStatisticResult
import com.connexin.pmx.server.models.dtos.GetDeliveryStatisticsCriteria
import com.connexin.pmx.server.repositories.PmxMessageRepository
import com.connexin.pmx.server.services.StatisticsService
import com.connexin.pmx.server.utils.formatPercent
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import kong.unirest.UnirestInstance
import net.javacrumbs.shedlock.core.LockAssert
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.time.temporal.ChronoUnit

@Service
class StatisticsServiceImpl(
    private val messageRepository: PmxMessageRepository,
    private val mapper: ObjectMapper,
    @Qualifier("unirestInstant") private val client: UnirestInstance
): StatisticsService {

    @Value("\${op.pmx.jobs.delivery-stats.webhook-url}")
    val webhookUrl: String = ""
    @Value("\${op.pmx.jobs.delivery-stats.failure-threshold}")
    val failureThreshold: Double = .1
    @Value("\${op.pmx.jobs.delivery-stats.grace-period}")
    val gracePeriod: Long = 5
    @Value("\${op.pmx.jobs.delivery-stats.window}")
    val window: Long = 60

    override fun getDeliveryStatistics(criteria: GetDeliveryStatisticsCriteria): List<DeliveryStatisticResult> {
        val (from, until, statuses, types) = criteria

        val aggregate = messageRepository.getMessageStatistics(from, until,
            statuses ?: MessageStatus.values(), types ?: MessageType.values())

        return aggregate.groupBy {
            Pair(
                if (it.engagement) MessageApplication.ENGAGEMENT else MessageApplication.LEGACY,
                it.type
            )
        }
            .mapValues { kv -> kv.value.groupBy { it.status }.mapValues { it.value.first().count } }
            .map {
                val total = it.value.values.sum()
                val totalD = total.toDouble()
                val delivered = it.value[MessageStatus.DELIVERED] ?: 0
                val failed = it.value[MessageStatus.FAILED] ?: 0
                val other = total - (delivered + failed)
                DeliveryStatisticResult(
                    from = from,
                    until = until,
                    application = it.key.first,
                    type = it.key.second,
                    total = total,
                    delivered = delivered,
                    failed = failed,
                    other = other,
                    deliveryRate = delivered/totalD,
                    failureRate = failed/totalD,
                    otherRate = other/totalD,
                    details = it.value
                )
            }
    }

    @Scheduled(cron = "\${op.pmx.jobs.delivery-stats.cron}")
    @SchedulerLock(name = "scheduledDeliveryStats")
    fun generateDeliveryStatisticsReport() {
        LockAssert.assertLocked()

        val start = Instant.now()
        log.info("Starting scheduledDeliveryStats job. Generating delivery statistics report")

        try {
            // provide a grace period between now and the reporting window so we can try to minimize churn
            // from messages that are queued or waiting for delivery confirmation
            val until = Instant.now().minus(gracePeriod, ChronoUnit.MINUTES).truncatedTo(ChronoUnit.MINUTES)
            val from = until.minus(window, ChronoUnit.MINUTES)

            // get our stats
            val deliveryStats = getDeliveryStatistics(GetDeliveryStatisticsCriteria(
                from = from,
                until = until
            ))

            if (deliveryStats.isEmpty()) {
                log.debug("No statistics in reporting period.")
                return
            }

            val payload = try {
                mapper.writeValueAsString(generateWebhookPayload(deliveryStats))
            } catch (ex: Exception) {
                log.error("Unexpected error when creating webhook payload", ex)
                return
            }

            try {
                val response = client.post(webhookUrl)
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .body(payload)
                    .asEmpty()

                if (!response.isSuccess) {
                    log.error("Unable to post statistics to webhook: [{} {}] {}", response.status, response.statusText, response.body)
                } else {
                    log.info("Successfully posted delivery statistics via webhook")
                }
            } catch (ex: Exception) {
                log.error("Unable to post to webhook.", ex)
            }
        } finally {
            val end = Instant.now()
            val duration = java.time.Duration.between(start, end)
            log.info("Finished scheduledDeliveryStats job. Duration: {} ms", duration.toMillis())
        }
    }

    private fun generateWebhookPayload(stats: List<DeliveryStatisticResult>): ObjectNode {
        val payload = mapper.createObjectNode()
        val blocks = mapper.createArrayNode()

        // section heading
        val header = mapper.createObjectNode()
        val headerText = mapper.createObjectNode()
        val from = stats.minOf { it.from }.atZone(ZoneId.of("US/Eastern"))
        val until = stats.minOf { it.until }.atZone(ZoneId.of("US/Eastern"))
        headerText.put(TYPE, MARKDOWN)
        headerText.put(TEXT, ":chart_with_upwards_trend: *Delivery Report*\n${formatter.format(from)} to ${formatter.format(until)} (Eastern)")

        header.put(TYPE, SECTION)
        header.put(TEXT, headerText)

        blocks.add(header)
        blocks.add(generateDivider())

        // add section for each stat entry
        stats.sortedBy { it.type }.forEach { result ->
            val section = mapper.createObjectNode()
            section.put(TYPE, SECTION)
            val text = mapper.createObjectNode()
            text.put(TYPE, MARKDOWN)
            val content = buildString {
                append("*${result.type} - ${result.application}*")
                if (result.failureRate > failureThreshold) {
                    append(":exclamation:")
                }
                append("\n")
                append("${result.delivered} out of ${result.total} (${result.deliveryRate.formatPercent(SCALE)})")

                if (result.failed > 0) {
                    append(", ${result.failed} failed (${result.failureRate.formatPercent(SCALE)})")
                }

                if (result.other > 0) {
                    append(", ${result.other} other* (${result.otherRate.formatPercent(SCALE)})")
                }
            }
            text.put(TEXT, content)
            section.put(TEXT, text)

            blocks.add(section)
            blocks.add(generateDivider())
        }

        val footer = mapper.createObjectNode()
        val footerText = mapper.createObjectNode()
        footerText.put(TYPE, MARKDOWN)
        footerText.put(TEXT, "_* other includes queued and sent but delivery not confirmed yet_")
        footer.put(TYPE, SECTION)
        footer.put(TEXT, footerText)

        blocks.add(footer)

        payload.put("blocks", blocks)

        return payload
    }

    private fun generateDivider(): ObjectNode? {
        val divider = mapper.createObjectNode()
        divider.put(TYPE, DIVIDER)

        return divider
    }

    companion object {
        private val log = LoggerFactory.getLogger(StatisticsServiceImpl::class.java)
        private val formatter = DateTimeFormatter.ofLocalizedDateTime(FormatStyle.SHORT, FormatStyle.SHORT)
        private const val MARKDOWN = "mrkdwn"
        private const val TYPE = "type"
        private const val TEXT = "text"
        private const val SECTION = "section"
        private const val DIVIDER = "divider"
        private const val SCALE = 5
    }
}