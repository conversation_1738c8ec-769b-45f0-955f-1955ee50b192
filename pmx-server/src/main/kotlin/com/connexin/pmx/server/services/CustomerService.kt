package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.EngagementRule
import com.connexin.pmx.server.models.dtos.CreateCustomerRequest
import com.connexin.pmx.server.models.dtos.CreateCustomerResponse
import com.connexin.pmx.server.models.dtos.PatchableCustomer
import com.connexin.pmx.server.models.dtos.Response
import com.github.fge.jsonpatch.JsonPatch
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

/**
 * A contract for services that manage PMX customers.
 */
interface CustomerService {
    /**
     * Retrieves a number by OPMED ID.
     * @param opmedId The customer's ID aka OPMED ID.
     * @param disableCache Whether cached copy of the customer should be utilized or not.
     * @return The customer whose ID matches opmedId, or null if not found.
     */
    fun getById(opmedId: String, disableCache: Boolean = false): Customer?

    /**
     * Delete a customer by OPMED ID.
     * @param opmedId The customer's ID aka OPMED ID.
     */
    fun deleteById(opmedId: String)

    /**
     * Saves a customer.
     * @param model The customer entity to save
     */
    fun save(model: Customer): Customer

    /**
     * Authenticates a username/password and returns the associated customer.
     * @param username The username
     * @param password The plaintext password
     * @return The Customer that authenticates successfully for the provided credentials, or null if the customer could not be found or authentication fails.
     */
    fun authenticate(username: String, password: String): Customer?

    /**
     * Creates a customer and places them in the provisioning status.
     * @param request The customer parameters
     * @return The result of the action
     */
    fun create(request: CreateCustomerRequest): CreateCustomerResponse

    /**
     * Finds a page of customers
     * @param pageable Paging information when searching customers.
     * @return The specified page of customers.
     */
    fun findAll(pageable: Pageable): Page<Customer>

    /**
     * Patches specific fields on the Customer.
     * @param customer The customer to patch
     * @param patchable The copy of the customer values to patch
     * @param patch The JSON Patch containing the desired changes
     * @return The patched customer.
     */
    fun <Patchable: PatchableCustomer> patch(customer: Customer, patchable: Patchable, patch: JsonPatch): Customer

    /**
     * Creates a new engagement rule for a given customer.
     * @param customer The customer that will own the new rule.
     * @param rule The new rule
     * @return A response that describes the result of the action.
     */
    fun createEngagementRule(customer: Customer, rule: EngagementRule): Response<EngagementRule>

    /**
     * Creates or replaces an engagement rule for a given customer.
     * @param customer The customer that owns the rule.
     * @param rule The rule.
     * @return A response that describes the result of the action.
     */
    fun saveEngagementRule(customer: Customer, ruleId: String, rule: EngagementRule): Response<EngagementRule>

    /**
     * Deletes an existing engagement rule for a given customer.
     * @param customer The customer that owns the rule.
     * @param ruleId The ID of the rule to be deleted.
     * @return A response that describes the result of the action.
     */
    fun deleteEngagementRule(customer: Customer, ruleId: String): Response<EngagementRule>
}