package com.connexin.pmx.server.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.convert.converter.Converter
import org.springframework.data.mongodb.core.convert.MongoCustomConversions
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.*


@Configuration
class MongoConfig {

    @Bean
    fun mongoCustomConversions(): MongoCustomConversions {
        return MongoCustomConversions(
            listOf(
                DateToLocalDateTimeConverter(),
                LocalDateTimeToDateConverter()
            )
        )
    }
}

class DateToLocalDateTimeConverter : Converter<Date, LocalDateTime> {
    override fun convert(p0: Date): LocalDateTime? {
        return LocalDateTime.ofInstant(p0.toInstant(), ZoneOffset.UTC)
    }
}

class LocalDateTimeToDateConverter : Converter<LocalDateTime, Date> {
    override fun convert(p0: LocalDateTime): Date? {
        return Date.from(p0.toInstant(ZoneOffset.UTC))
    }
}