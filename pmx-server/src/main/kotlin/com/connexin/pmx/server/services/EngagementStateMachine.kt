package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.EngagementContext
import com.connexin.pmx.server.models.EventMessage

/**
 * A contract for components that provide engagement state machine functionality.
 */
interface EngagementStateMachine {
    /**
     * Sends an event described by the specified message to the state machine to continue work.
     */
    fun <M: EventMessage> sendEvent(message: M, context: EngagementContext? = null): Engagement
}