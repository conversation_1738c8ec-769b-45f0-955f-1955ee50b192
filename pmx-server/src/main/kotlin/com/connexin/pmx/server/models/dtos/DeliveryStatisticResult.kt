package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.MessageApplication
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import java.time.Instant

data class DeliveryStatisticResult(
    val from: Instant,
    val until: Instant,
    val application: MessageApplication,
    val type: MessageType,
    val total: Long,
    val delivered: Long,
    val failed: Long,
    val other: Long,
    val deliveryRate: Double,
    val failureRate: Double,
    val otherRate: Double,
    val details: Map<MessageStatus, Long>
)
