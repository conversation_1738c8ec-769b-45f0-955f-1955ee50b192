package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.PmxMessage
import com.connexin.pmx.server.models.dtos.RespondResult

/**
 * A service that dispatches message to the recipient(s) based on the message type.
 * It sends messages to the recipient(s) and handles the response from the recipient
 */
interface MessageDispatcherFacade {
    /**
     * Sends the message to the recipient using a dispatcher appropriate for the message type.
     */
    fun initiate(message: PmxMessage): InitiateResult

    /**
     * Handles the response to a message from the recipient
     * @param type A hint for the type of response
     * @param payload The raw payload from the vendor received as a result of a prior initiate or respond call.
     */
    fun respond(type: MessageType, payload: String): RespondResult
}