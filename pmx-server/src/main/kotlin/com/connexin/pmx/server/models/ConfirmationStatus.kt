package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema

/**
 * Describes the confirmation status of a message or engagement.
 */
@Schema(
    enumAsRef = true
)
enum class ConfirmationStatus {
    /**
     * Confirmation is not applicable to the message.
     */
    NOT_APPLICABLE,

    /**
     * Confirmation has not been asked yet, and is in an indeterminate state.
     */
    UNCONFIRMED,

    /**
     * The appointment was confirmed by the recipient.
     */
    CONFIRMED,

    /**
     * The appointment was declined by the recipient.
     */
    DECLINED,

    /**
     * No recipient answered one way or the other regarding confirmation.
     */
    NO_RESPONSE
}