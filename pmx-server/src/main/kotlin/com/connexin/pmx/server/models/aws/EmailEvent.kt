package com.connexin.pmx.server.models.aws

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import java.time.Instant

enum class EmailEventType {
    Bounce,
    Delivery,
    DeliveryDelay,
    Reject,
    Send
}

/**
 * Represents an email sending event published by SES to SNS.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class EmailEvent(
    /**
     * Describes the type of event.
     */
    val eventType: EmailEventType,
    /**
     * Contains information about the email that produced the event.
     */
    val mail: Mail,
    /**
     * This field is only present if eventType is Delivery.
     */
    val delivery: Delivery? = null,
    /**
     * This field is only present if eventType is DeliveryDelay.
     */
    val deliveryDelay: DeliveryDelay? = null,
    /**
     * This field is only present if eventType is Bounce.
     */
    val bounce: Bounce? = null,
    /**
     * This field is only present if eventType is Reject.
     */
    val reject: Reject? = null
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Mail(
        val timestamp: Instant,
        val messageId: String,
        val destination: List<String>,
        val source: String,
        val tags: Map<String, Set<String>>
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class Delivery(
        val timestamp: Instant,
        val recipients: List<String>
    )

    data class DeliveryDelay(
        val delayType: DelayType,
        val delayedRecipients: List<DelayedRecipient>
    ) {
        enum class DelayType {
            /**
             * An internal Amazon SES issue caused the message to be delayed.
             */
            InternalFailure,

            /**
             * A generic failure occurred during the SMTP conversation.
             */
            General,

            /**
             * The recipient's mailbox is full and is unable to receive additional messages.
             */
            MailboxFull,

            /**
             * The recipient's mail server has detected a large amount of unsolicited email from your account.
             */
            SpamDetected,

            /**
             * A temporary issue with the recipient's email server is preventing the delivery of the message.
             */
            RecipientServerError,

            /**
             * The IP address that's sending the message is being blocked or throttled by the recipient's email provider.
             */
            IPFailure,

            /**
             * There was a temporary communication failure during the SMTP conversation with the recipient's email provider.
             */
            TransientCommunicationFailure,

            /**
             * Amazon SES was unable to look up the DNS hostname for your IP addresses.
             */
            BYOIPHostNameLookupUnavailable,

            /**
             * Amazon SES wasn't able to determine the reason for the delivery delay.
             */
            Undetermined
        }

        data class DelayedRecipient(
            val emailAddress: String,
            val status: String,
            val diagnosticCode: String
        )
    }

    data class Bounce(
        val bounceType: BounceType,
        val bounceSubType: BounceSubType,
        val bouncedRecipients: List<BouncedRecipient>,
        val feedbackId: String
    ) {
        enum class BounceType {
            /**
             * Amazon SES was unable to determine a specific bounce reason.
             */
            Undetermined,

            /**
             * You should remove the recipient's email address from your mailing list.
             */
            Permanent,

            /**
             * You may be able to successfully send to this recipient in the future.
             */
            Transient
        }

        enum class BounceSubType {
            /**
             * Amazon SES was unable to determine a specific bounce reason.
             */
            Undetermined,

            /**
             * Amazon SES received a general bounce.
             */
            General,

            /**
             * Amazon SES received a permanent hard bounce because the target email address does not exist.
             */
            NoEmail,

            /**
             * Amazon SES has suppressed sending to this address because it has a recent history of bouncing as an invalid address.
             */
            Suppressed,

            /**
             * Amazon SES has suppressed sending to this address because it is on the account-level suppression list.
             */
            OnAccountSuppressionList,

            /**
             * Amazon SES received a mailbox full bounce.
             */
            MailboxFull,

            /**
             * Amazon SES received a message too large bounce.
             */
            MessageTooLarge,

            /**
             * Amazon SES received a content rejected bounce.
             */
            ContentRejected,

            /**
             * Amazon SES received an attachment rejected bounce.
             */
            AttachmentRejected
        }

        data class BouncedRecipient(
            val emailAddress: String,
            val diagnosticCode: String
        )
    }

    data class Reject(
        val reason: String
    )

    companion object {
        private val log = LoggerFactory.getLogger(EmailEvent::class.java)

        fun parse(payload: String, objectMapper: ObjectMapper): EmailEvent? {
            return try {
                objectMapper.readValue(payload, EmailEvent::class.java)
            } catch (ex: Exception) {
                log.error("Failed to parse payload for EmailEvent", ex)
                null
            }
        }
    }
}