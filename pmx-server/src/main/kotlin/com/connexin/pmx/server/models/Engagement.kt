package com.connexin.pmx.server.models

import com.connexin.pmx.server.statemachine.State
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Document("engagements")
@CompoundIndexes(
    value = [
        CompoundIndex(
            name = "customer_resources",
            def = "{'customerId': 1, 'resources._class': 1, 'resources._id': 1}",
            unique = false
        ),
        CompoundIndex(
            name = "engagements_awaiting_checkpoint",
            def = "{'nextCheckpoint': 1, 'status': 1}",
            unique = false
        )
    ]
)
data class Engagement(
    @Id
    val id: String? = null,
    val customerId: String,
    var createdAt: Instant? = null,
    var updatedAt: Instant? = null,
    override var status: EngagementStatus = EngagementStatus.INITIAL,
    var eventDate: Instant,
    var nextCheckpoint: Instant,
    val resources: MutableSet<Resource> = mutableSetOf(),
    val activity: MutableList<Activity> = mutableListOf(),
    var confirmationStatus: ConfirmationStatus = ConfirmationStatus.UNCONFIRMED,
    var confirmationAttempts: Int = 0,
    var checkInAttempts: Int = 0,
    var reminderAttempts: Int = 0,
    var cancellationAttempts: Int = 0,
    var appointmentSurveyAttempts: Int = 0,
    var contactDeclined: Boolean = false,
    var bookingAttempts: Int = 0,
    var sameDayCheckInSent: Boolean = false
): State<EngagementStatus, Engagement> {

    override fun copyWithState(state: EngagementStatus): Engagement {
        return this.copy(status = state)
    }

    fun reset() {
        status = EngagementStatus.INITIAL
        confirmationStatus = ConfirmationStatus.UNCONFIRMED
        confirmationAttempts = 0
        checkInAttempts = 0
        reminderAttempts = 0
        cancellationAttempts = 0
        appointmentSurveyAttempts = 0
        contactDeclined = false
        resources.clear()
    }

    fun getContact(type: MessageType, to: String?): ContactResource? {
        if (to == null) {
            return null
        }

        return resources.filterIsInstance<ContactResource>()
            .firstOrNull {
                when (type) {
                    MessageType.SMS -> it.phone.equals(to, ignoreCase = true)
                            && it.contactMethod == ContactMethod.SMS
                    MessageType.VOICE -> it.phone.equals(to, ignoreCase = true)
                            && it.contactMethod == ContactMethod.VOICE
                    MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> it.email.equals(to, ignoreCase = true)
                            && it.contactMethod == ContactMethod.EMAIL
                }
            }
    }

  fun getContact(id: String): ContactResource? {
    return resources.filterIsInstance<ContactResource>()
        .firstOrNull { it.id == id}
  }

    fun getStaff(): StaffResource? {
        return resources.filterIsInstance<StaffResource>()
            .firstOrNull()
    }

  fun getAppointment(id: String): AppointmentResource? {
    return resources.filterIsInstance<AppointmentResource>()
        .firstOrNull { it.id == id }
  }

    fun getAppointment(): AppointmentResource? {
        return resources.filterIsInstance<AppointmentResource>()
            .firstOrNull()
    }

    fun getAppointments(): List<AppointmentResource> {
        return resources.filterIsInstance<AppointmentResource>()
    }
}


