package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.SubscriptionPreference

interface SubscriptionService {
    /**
     * Retrieves the subscription preference for an address.
     * @param address The contact's address, such as an email or phone number.
     * @return The subscription preference whose ID matches address, or null if not found.
     */
    fun getById(address: String): SubscriptionPreference?

    /**
     * Saves a subscription preference.
     * @param preference The preference entity to save.
     */
    fun save(preference: SubscriptionPreference): SubscriptionPreference
}