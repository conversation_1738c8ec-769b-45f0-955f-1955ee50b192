package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v2/resource")
@Tag(name = "Resource", description = "Endpoint for updating resources referenced within engagements.")
class ResourceController(
    private val customerService: CustomerService,
    private val engagementService: EngagementService
) {

    @Operation(
        summary = "Patches all matching contact resources referenced within engagements.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Patch was successful."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided resource was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/contact",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchContact(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: Contact
    ): Contact {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                val res = engagementService.updateContact(request.copy(customerId = customerId))

                return if (res.success) Contact.from(res.get())
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update contact."
                )
            }
    }

    @Operation(
        summary = "Patches all matching patient resources referenced within engagements.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Patch was successful."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided resource was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/patient",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchPatient(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: Patient
    ): Patient {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                val res = engagementService.updatePatient(request.copy(customerId = customerId))

                return if (res.success) Patient.from(res.get())
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update patient."
                )
            }
    }

    @Operation(
        summary = "Patches all matching location resources referenced within engagements.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Patch was successful."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided resource was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/location",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchLocation(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: Location
    ): Location {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                val res = engagementService.updateLocation(request.copy(customerId = customerId))

                return if (res.success) Location.from(res.get())
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update location."
                )
            }
    }

    @Operation(
        summary = "Patches all matching staff resources referenced within engagements.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Patch was successful."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided resource was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/staff",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchStaff(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: Staff
    ): Staff {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                val res = engagementService.updateStaff(request.copy(customerId = customerId))

                return if (res.success) Staff.from(res.get())!!
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update staff."
                )
            }
    }

    @Operation(
        summary = "Patches all matching practice resources referenced within engagements.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Patch was successful."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "400",
                description = "The provided resource was poorly formed or failed validation.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PatchMapping(
        "/practice",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun patchPractice(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @RequestBody request: Practice
    ): Practice {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                val res = engagementService.updatePractice(request.copy(customerId = customerId))

                return if (res.success) Practice.from(res.get())!!
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not update practice."
                )
            }
    }

    private fun validateCustomer(customerId: String) {
        customerService.getById(customerId)
            ?: throw NotFoundException(message = "Customer not found.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(ResourceController::class.java)
    }
}