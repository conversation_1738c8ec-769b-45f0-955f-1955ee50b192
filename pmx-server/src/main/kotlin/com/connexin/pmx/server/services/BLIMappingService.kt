package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.utils.Base64Util
import com.connexin.pmx.server.utils.InstantUtil
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.contains
import org.apache.http.util.TextUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.Instant

@Service
class BLIMappingService(
    @Qualifier("xmlMapper") private val xmlMapper: ObjectMapper,
    private val jsonMapper: ObjectMapper,
    private val clock: Clock
) {

    companion object {
        private val log = LoggerFactory.getLogger(BLIMappingService::class.java)

        const val UNKNOWN_ERROR = "Unknown error"
    }

    fun <T> parseXml(xml: String, type: Class<T>): T {
        return xmlMapper.readValue(xml, type)
    }

    /**
     * converts a VTMessages object to a CreateMessageRequest object
     */
    fun mapToCreateMessageRequest(vtMessages: VTMessages, opMedID: String) = CreateMessageRequest(
        customerId = opMedID,
        type = MessageType.VOICE,
        to = if (vtMessages.order.phoneNumber.isNullOrBlank()) setOf() else setOf(vtMessages.order.phoneNumber!!),
        message = findMessageAndDecode(vtMessages, true) ?: "",
        altMessage = findMessageAndDecode(vtMessages, false) ?: "",
        replyTo = vtMessages.order.callerID?.ifBlank { null },
        sendFrom = InstantUtil.parse24HourEasternAsOffsetTime(vtMessages.order.restartTime, instant = Instant.now(clock)),
        sendUntil = InstantUtil.parse24HourEasternAsOffsetTime(vtMessages.order.stopTime, instant = Instant.now(clock))
    )

    /**
     * converts a ETMessages object to a CreateMessageRequest object
     */
    fun mapToCreateMessageRequest(etMessages: ETMessages, opMedID: String) = CreateMessageRequest(
        customerId = opMedID,
        type = MessageType.EMAIL,
        to = if (etMessages.order.emailTo.isNullOrBlank()) setOf() else setOf(etMessages.order.emailTo!!),
        message = Base64Util.decode(etMessages.order.textBinary) ?: "",
        altMessage = null,
        subject = etMessages.order.emailSubject,
        replyTo = etMessages.order.emailReplyTo?.ifBlank { null }
    )

    /**
     * converts a EBMessages object to a CreateMessageRequest object
     */
    fun mapToCreateMessageRequest(ebMessages: EBMessages, opMedID: String) = CreateMessageRequest(
        customerId = opMedID,
        type = MessageType.EMAIL_BROADCAST,
        to = convertToSet(ArrayList(
            splitToMap(Base64Util.decode(ebMessages.order.listBinary)!!, "\n", ",").keys
        ).filterNot { s -> s == "Email" }.sorted()),
        message = Base64Util.decode(ebMessages.order.textBinary) ?: "",
        subject = ebMessages.order.subject,
        replyTo = ebMessages.order.replyTo?.ifBlank { null }
    )

    /**
     * converts a MTMessages object to a CreateMessageRequest object
     */
    fun mapToCreateMessageRequest(mtMessages: MTMessages, opMedID: String) = CreateMessageRequest(
        customerId = opMedID,
        type = MessageType.SMS,
        to = if (mtMessages.order.cellPhoneNumber.isNullOrBlank()) setOf() else setOf(mtMessages.order.cellPhoneNumber!!),
        message = mtMessages.order.message ?: "",
        sendFrom = InstantUtil.parse24HourEasternAsOffsetTime(mtMessages.order.restartTime, instant = Instant.now(clock)),
        sendUntil = InstantUtil.parse24HourEasternAsOffsetTime(mtMessages.order.stopTime, instant = Instant.now(clock))
    )

    /**
     * maps a PmxMessage object to an EBReports object
     */
    fun mapToEmailBroadcastReport(message: PmxMessage): EBReports {
        val ebReports = mutableListOf<EBReport>()

        // create an EBReports object for each PMX message destination
        message.emailRecipients!!.forEach {
            val ebReport = EBReport(
                orderId = message.id,
                project = "Broadcast",
                emailAddress = it.address,
                openCount = null,
                lastOpened = null,
                jobStatus = getMessageStatus(it.status),
                result = null,
                error = formatEmailMessageError(it.status, it.reason?.name?: "", it.errors?: ""),
                timestamp = InstantUtil.toLocalDateTime(message.completedAt),
                email = it.address,
                name = null   // name is not available from the PMXMessage object
            )
            ebReports.add(ebReport)
        }

        return EBReports(ebReports)
    }

    /**
     * maps a PmxMessage object to an ETReports object
     */
    fun mapToEmailReport(message: PmxMessage): ETReports {
        val etReport = ETReport(
            formId = message.id,
            uniqueId = message.id,
            orderId = message.remoteId,  // translates to JobId in PMX
            project = null,
            emailAddress = message.to,
            openCount = null,
            lastOpened = null,
            jobStatus = getMessageStatus(message.status),
            result = null,
            error = formatEmailMessageError(message.status, message.emailDeliveryFailureReason?.name?: "",
                message.errors?: ""),
            timestamp = InstantUtil.toLocalDateTime(message.completedAt)   // expecting format 08/12/2013 02:10:42 PM
        )

        return ETReports(etReport)
    }

    /**
     * maps a PmxMessage object to an MTReports object
     */
    fun mapToTextMessageReport(message: PmxMessage): MTReports {
        val mtReport = MTReport(
            orderID = message.remoteId,  // translates to JobId in PMX
            uniqueId = message.id,
            project = null,
            cellPhoneNumber = message.to,
            jobStatus = getMessageStatus(message.status),
            result = null,
            error = formatVoiceAndTextMessageError(message.status, message.errors ?: "")
        )

        return MTReports(mtReport)
    }

    /**
     * maps a PmxMessage object to an VTReports object
     */
    fun mapToVoiceMessageReport(message: PmxMessage): VTReports {
        val vtReport = VTReport(
            uniqueId = message.id,
            jobID = message.remoteId,  // translates to JobId in PMX
            phoneNumber = message.to,
            duration = null,
            rate = null,
            cost = null,
            status = getMessageStatus(message.status),
            error = formatVoiceAndTextMessageError(message.status, message.errors ?: ""),
            deliveryMethod = getVoiceDeliveryMethod(message.voiceDeliveryMethod),
            keyPress = message.responseData,  // PMX expects 1, 2 or other number
            timestamp = InstantUtil.toLocalDateTime(message.completedAt)  // expecting format 5/25/2011 2:07:00 PM
        )

        return VTReports(vtReport)
    }

    /**
     * maps Live and Voice Mail delivery methods to values PMX recognizes
     */
    fun getVoiceDeliveryMethod(deliveryMethod: VoiceDeliveryMethod): String {
        return when (deliveryMethod) {
            VoiceDeliveryMethod.LIVE -> "Live"
            VoiceDeliveryMethod.VOICE_MAIL -> "Voice Mail"
            else -> deliveryMethod.toString()
        }
    }

    /**
     * translates the message status Delivered to Sent for PMX
     */
    fun getMessageStatus(messageStatus: MessageStatus): String {
        return if ((messageStatus == MessageStatus.DELIVERED) || (messageStatus == MessageStatus.SENT))
            "Sent"
        else
            messageStatus.toString()
    }

    /**
     * parses a string to key value pairs
     */
    private fun splitToMap(source: String, entriesSeparator: String?, keyValueSeparator: String?): Map<String, String> {
        val map: MutableMap<String, String> = HashMap()
        val entries = source.split(entriesSeparator!!).toTypedArray()
        for (entry in entries) {
            if (!TextUtils.isEmpty(entry) && entry.contains(keyValueSeparator!!)) {
                val keyValue = entry.split(keyValueSeparator).toTypedArray()
                map[keyValue[0]] = keyValue[1]
            }
        }
        return map
    }

    private fun <T> convertToSet(list: List<T>): Set<T> {
        return HashSet(list)
    }

    /**
     * retrieve the base64 encoded message from the voice transaction message object and convert it to a string
     */
    private fun findMessageAndDecode(vtMessages: VTMessages, isLive: Boolean): String? {

        val message = vtMessages.order.vtDocuments.find { it.documentType == if (isLive) "Live" else "Message" }

        // decode the message
        if (message != null) {
            val messageToDecode = vtMessages.order.vtDocuments[
                            vtMessages.order.vtDocuments.indexOf(message)
            ].documentBinary
            return Base64Util.decode(messageToDecode)
        }
        else
            return ""

    }

    private fun formatVoiceAndTextMessageError(status: MessageStatus, error : String): String? {
        try {
            var containerJson = jsonMapper.readTree(error)

            if (containerJson.contains("errors")) {
                containerJson = containerJson.get("errors")
            }

            if (!containerJson.isArray || containerJson.size() == 0) {
                return if (status == MessageStatus.FAILED) UNKNOWN_ERROR else null
            }

            val errorData = containerJson[0]
            val title = if (errorData.contains("title")) {
                errorData.get("title").textValue()
            } else {
                null
            }
            val code = if (errorData.contains("code")) {
                errorData.get("code").textValue()
            } else {
                null
            }
            val detail = if (errorData.contains("detail")) {
                errorData.get("detail").textValue()
            } else {
                null
            }

            return "title: $title\ncode: $code\ndetail: $detail"

        } catch (ex : Exception ) {
            log.error("Failed to read error details", ex)
            return "Failed to read error details"
        }
    }

    private fun formatEmailMessageError(status: MessageStatus, reason: String, error : String): String? {
        if (error.isEmpty() || reason.isEmpty() ) {
            return if (status == MessageStatus.FAILED) UNKNOWN_ERROR else null
        }

        try {
            val errorJson = jsonMapper.readTree(error)

            val detail = when {
                errorJson.contains("delayType") -> errorJson.get("delayType").textValue()
                errorJson.contains("bounceType") -> String.format("%s/%s", errorJson.get("bounceType").textValue(),
                    errorJson.get("bounceSubType").textValue())
                errorJson.contains("reason") -> errorJson.get("reason").textValue()
                else -> null
            }
            if (detail.isNullOrBlank()) {
                return if (status == MessageStatus.FAILED) UNKNOWN_ERROR else null
            }
            return "title: $reason\ndetail: $detail"
        } catch (ex : Exception ) {
            log.error("Failed to read error details", ex)
            return "Failed to read error details"
        }
    }
}