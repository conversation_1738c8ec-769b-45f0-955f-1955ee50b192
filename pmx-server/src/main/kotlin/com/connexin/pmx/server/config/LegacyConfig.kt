package com.connexin.pmx.server.config

import com.connexin.pmx.server.services.BLIMappingService
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.dataformat.xml.JacksonXmlModule
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Clock

@Configuration
class LegacyConfig {
    @Bean
    @Qualifier("xmlMapper")
    fun xmlMapper(): ObjectMapper {
        return XmlMapper(JacksonXmlModule())
            .registerModule(JavaTimeModule())
            .registerModule(Jdk8Module())
            .registerKotlinModule()
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS) // to parse the dates as LocalDate, else parsing error
            .enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)
            .enable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    }

    @Bean
    @ConditionalOnMissingBean(BLIMappingService::class)
    fun bliMappingService(@Qualifier("xmlMapper") xmlMapper: ObjectMapper, clock: Clock): BLIMappingService {
        val jsonMapper = ObjectMapper()
        return BLIMappingService(xmlMapper, jsonMapper, clock)
    }
}