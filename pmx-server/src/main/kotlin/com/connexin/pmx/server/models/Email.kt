package com.connexin.pmx.server.models

import org.json.JSONObject

data class EmailMessage(
    val subject: String? = null,
    val body: String? = null,
    val isHtml: Boolean = false,
    val fromAddress: String? = null,
    val replyToAddress: String? = null,
    val toAddresses: MutableSet<String> = mutableSetOf(),
    val ccAddresses: MutableSet<String> = mutableSetOf(),
    val bccAddresses: MutableSet<String> = mutableSetOf()
)

data class BulkEmailMessage(
    val template: Template,
    val fromAddress: String? = null,
    val replyToAddress: String? = null,
    val entries: List<Entry> = listOf()
) {
    data class Template(
        val name: String,
        val defaultReplacementValues: JSONObject = JSONObject(),
        val isTransient: Boolean = true,
        val subject: String? = null,
        val textContent: String? = null,
        val htmlContent: String? = null
    )

    data class Entry(
        val toAddress: String,
        val unsubscribeLink: String? = null,
        val replacementValues: JSONObject = JSONObject()
    )
}