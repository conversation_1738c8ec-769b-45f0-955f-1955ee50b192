package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.ApiException
import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.exceptions.UnauthorizedException
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.dtos.GenerateCheckInUrlRequestDto
import com.connexin.pmx.server.services.BridgeService
import com.connexin.pmx.server.web.RestHttpClient
import kong.unirest.UnirestInstance
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders.CONTENT_TYPE
import org.springframework.http.MediaType.APPLICATION_JSON_VALUE
import org.springframework.stereotype.Service

@Service
class BridgeServiceImpl(
    @Qualifier("bridgeUnirestInstance")
    private val unirestInstance: UnirestInstance,
): BridgeService {

    @Value("\${op.bridge.url}")
    private lateinit var bridgeUrl: String

    @Value("\${op.bridge.paths.patient-checkin-generate-url-path}")
    private lateinit var patientGenerateCheckinPath: String

    private val restHttpClient: RestHttpClient = RestHttpClient(unirestInstance)

    override fun generatePatientCheckinUrl(localAppointmentId: String, localPatientId: String, localContactId: String, opmedId: String): String {
        val generateCheckinUrlRequestDto = GenerateCheckInUrlRequestDto(localAppointmentId, localPatientId, localContactId)

        val response = restHttpClient.postAsString(
            "${bridgeUrl.removeSuffix("/")}/${patientGenerateCheckinPath.removePrefix("/")}",
            mapOf(
                Pair(CONTENT_TYPE, APPLICATION_JSON_VALUE),
                Pair(X_OPMED, opmedId)
            ),
            generateCheckinUrlRequestDto
        )

        if (!response.isSuccess) {
            val responseErrorPrefix = "Could not generate check-in link url"
            when (response.status) {
                400 -> throw BadRequestException(message = "${responseErrorPrefix}, bad request response from op bridge microservice: ${response.body}")
                401, 403 -> throw UnauthorizedException(message = "${responseErrorPrefix}, unauthorized call to op bridge microservice")
                404 -> throw NotFoundException(message = "${responseErrorPrefix}, op bridge microservice endpoint not found")
                else -> throw ApiException(message = "${responseErrorPrefix}, error from op bridge microservice, more details: ${response.body}")
            }
        }

        log.info("Successfully generated bridge check-in link: ${response.body}")
        return response.body
    }

    companion object {
        private val log = LoggerFactory.getLogger(BridgeServiceImpl::class.java)
    }

}