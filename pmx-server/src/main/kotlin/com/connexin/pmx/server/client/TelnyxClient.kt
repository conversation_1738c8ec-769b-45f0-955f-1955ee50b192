package com.connexin.pmx.server.client

import com.connexin.pmx.server.models.dtos.BillingGroupResponse
import com.connexin.pmx.server.models.dtos.OutboundVoiceProfileResponse
import com.connexin.pmx.server.services.impl.TelnyxServiceImpl.Companion.BILLING_GROUP_URL
import com.connexin.pmx.server.services.impl.TelnyxServiceImpl.Companion.OUTBOUND_PROFILE_URL
import com.telnyx.sdk.model.ErrorResponse
import kong.unirest.HttpResponse
import kong.unirest.Unirest
import kong.unirest.UnirestInstance
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class TelnyxClient(
    @Qualifier("unirestInstant") private val unirestInstance: UnirestInstance,
    @Value("\${telnyx.api-key}") private val apiKey: String
) {

    fun deleteBillingGroup(billingGroupId: String): BillingGroupResponse? {
        val billingGroupUrl = BILLING_GROUP_URL.toString()
        val queryParam = "/{id}"
        val urlBuilder = StringBuilder()
        urlBuilder.append(billingGroupUrl).append(queryParam)
        val response: HttpResponse<BillingGroupResponse> = unirestInstance
            .delete(urlBuilder.toString())
            .routeParam("id", billingGroupId)
            .header("Authorization", "Bearer $apiKey")
            .asObject(BillingGroupResponse::class.java)

        return if (response.isSuccess) {
            response.body
        } else {
            handleErrorResponseBillingGroup(response)
            null
        }
    }

    fun deleteOutboundVoiceProfile(profileId: String): OutboundVoiceProfileResponse? {
        val outboundProfileUrl = OUTBOUND_PROFILE_URL.toString()
        val queryParam = "/{id}"
        val urlBuilder = StringBuilder()
        urlBuilder.append(outboundProfileUrl).append(queryParam)
        val response: HttpResponse<OutboundVoiceProfileResponse> = unirestInstance
            .delete(urlBuilder.toString())
            .routeParam("id", profileId)
            .asObject(OutboundVoiceProfileResponse::class.java)
        return if (response.isSuccess) {
            response.body
        } else {
            handleErrorResponseOutboundVoiceProfile(response)
            null
        }
    }

    fun handleErrorResponseBillingGroup(response: HttpResponse<*>) {
        val errorResponse: ErrorResponse? = response.body?.let {
            Unirest.config().objectMapper.readValue(it.toString(), ErrorResponse::class.java)
        }

        errorResponse?.errors?.forEach { error ->
            println("Error Code: ${error.code}, Title: ${error.title}, Detail: ${error.detail}")
        }
    }

    private fun handleErrorResponseOutboundVoiceProfile(response: HttpResponse<*>) {
        when (response.status) {
            401 -> println("Unauthorized: ${response.statusText}")
            404 -> println("Resource not found: ${response.statusText}")
            422 -> println("Bad request: ${response.statusText}")
            else -> println("Unknown error: ${response.statusText}")
        }
    }
}