package com.connexin.pmx.server.models

import com.connexin.pmx.server.constraints.isPhoneNumber
import com.connexin.pmx.server.constraints.isEmail
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotEqualTo
import org.valiktor.functions.isNotNull

data class ContactResource(
    override val id: String,
    val familyName: String?,
    val givenName: String?,
    val email: String?,
    val phone: String?,
    val contactMethod: ContactMethod,
    val language: Language
): Resource {
    fun validate() {
        org.valiktor.validate(this) {
            when(contactMethod) {
                ContactMethod.EMAIL -> validate(ContactResource::email).isNotNull().isNotBlank().isEmail()
                ContactMethod.VOICE, ContactMethod.SMS -> validate(ContactResource::phone).isNotNull().isNotBlank().isPhoneNumber()
                else -> validate(ContactResource::contactMethod).isNotEqualTo(ContactMethod.NONE)
            }
        }

    }

    fun coerceToRecipient(): String {
        validate()
        // validation should ensure that none of the exceptions below happen
        return when (contactMethod) {
            ContactMethod.VOICE -> phone!!
            ContactMethod.SMS -> phone!!
            ContactMethod.EMAIL -> email!!
            ContactMethod.NONE -> throw IllegalStateException("Accessed toRecipient without calling validate.")
        }
    }

    override val type: ResourceType
        get() = ResourceType.CONTACT
}
