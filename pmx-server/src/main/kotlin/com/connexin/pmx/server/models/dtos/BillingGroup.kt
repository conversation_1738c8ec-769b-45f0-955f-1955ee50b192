package com.connexin.pmx.server.models.dtos

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class BillingGroup (
    val recordType: String,
    val id: String,
    val organizationId: String,
    val name: String,
    val createdAt : String,
    val updatedAt: String,
    val deletedAt : String?
)