package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.models.SmsConsent
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.stereotype.Repository
import org.springframework.util.Assert
import java.time.Instant
import java.util.Optional

interface SmsConsentRepository : MongoRepository<SmsConsent, String>, CustomizedConsentRepository<SmsConsent, String> {
    fun findByRelatedMessageId(messageId: String): Optional<SmsConsent>
}

interface CustomizedConsentRepository<T, ID> {
    fun <S : T?> save(entity: S): S
}

@Repository
class CustomizedConsentRepositoryImpl(
    private val template: MongoTemplate
): CustomizedConsentRepository<SmsConsent, String> {

    override fun <S : SmsConsent?> save(entity: S): S {
        Assert.notNull(entity, "Entity must not be null!")

        val now = Instant.now()
        entity!!.createdAt = entity.createdAt ?: now
        entity.updatedAt = now

        return template.save(entity)
    }
}