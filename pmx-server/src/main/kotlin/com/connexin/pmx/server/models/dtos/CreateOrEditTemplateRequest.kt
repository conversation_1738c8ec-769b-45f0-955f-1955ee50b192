package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.*
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank
import org.valiktor.functions.isNotEmpty
import org.valiktor.functions.isNotNull

@Schema(
    description = "Parameters used to create or edit a Template.",
    name = "CreateTemplateRequest"
)
data class CreateOrEditTemplateRequest(
    @field:Schema(
        description = "A name that describes the template."
    )
    val name: String,
    @field:Schema(
        description = "The engagement workflow this template is associated with."
    )
    val workflow: EngagementWorkflow,
    @Deprecated("Use scenarios instead")
    @field:Schema(
        description = "Variations of the template for different communication channels and languages.",
        deprecated = true
    )
    val variations: Map<MessageType, Map<Language, Template.Segments>> = emptyMap(),
    @field:Schema(
        description = "Variations of the template for different scenarios, communication channels, and languages."
    )
    val scenarios: Map<TemplateScenario, Map<MessageType, Map<Language, Template.Segments>>> = emptyMap(),
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(CreateOrEditTemplateRequest::name).isNotBlank()
            validate(CreateOrEditTemplateRequest::workflow).isNotNull()

            // continue validating variations until deprecated field is removed
            if (scenarios.isEmpty()) {
                validate(CreateOrEditTemplateRequest::variations).isNotEmpty()
            }
            if (variations.isEmpty()) {
                validate(CreateOrEditTemplateRequest::scenarios).isNotEmpty()
            }
        }
    }

    companion object {
        fun to(request: CreateOrEditTemplateRequest): Template {
            return Template(
                name = request.name,
                workflow = request.workflow,
                variations = request.variations,
                scenarios = request.scenarios.ifEmpty {
                    mapOf(TemplateScenario.DEFAULT to request.variations)
                }
            )
        }
    }
}
