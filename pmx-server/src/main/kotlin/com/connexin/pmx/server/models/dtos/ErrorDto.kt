package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    name = "Error",
    description = "Describes an error that occurred."
)
data class ErrorDto(
    @field:Schema(
        description = "Path where the error occurred."
    )
    val path: String,
    @field:<PERSON>hema(
        description = "Describes the error."
    )
    val message: String,
    @field:<PERSON>hema(
        description = "The error code."
    )
    val errorCode: Int,
    @field:Schema(
        description = "Additional details relating to the error."
    )
    val details: String? = null
)
