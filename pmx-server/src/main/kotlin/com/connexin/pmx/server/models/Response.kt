package com.connexin.pmx.server.models

import com.connexin.pmx.server.models.dtos.ErrorDto
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.index.CompoundIndex
import org.springframework.data.mongodb.core.index.CompoundIndexes
import org.springframework.data.mongodb.core.index.Indexed
import org.springframework.data.mongodb.core.mapping.Document
import java.time.Instant

@Schema(
    description = "A response from one or more contacts to a particular workflow in an engagement.",
    oneOf = [ErrorResponse::class, CancellationResponse::class, ConfirmationResponse::class, CheckInResponse::class, MessageResponse::class]
)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = Constants.TYPE_PROPERTY,
    visible = true
)
@JsonSubTypes(
    JsonSubTypes.Type(value = CancellationResponse::class, name = Constants.CANCELLATION_RESPONSE),
    JsonSubTypes.Type(value = ConfirmationResponse::class, name = Constants.CONFIRMATION_RESPONSE),
    JsonSubTypes.Type(value = CheckInResponse::class, name = Constants.CHECK_IN_RESPONSE),
    JsonSubTypes.Type(value = ErrorResponse::class, name = Constants.ERROR_RESPONSE),
    JsonSubTypes.Type(value = CompleteResponse::class, name = Constants.COMPLETE_RESPONSE),
    JsonSubTypes.Type(value = MessageResponse::class, name = Constants.CONTACT_UNREACHABLE_RESPONSE),
)
@Document("responses")
@CompoundIndexes(
    value = [
        CompoundIndex(
            name = "customer_responses_since",
            def = "{'customerId': 1, 'occurredAt': 1, 'archived': 1 }",
            unique = false
        )
    ]
)
abstract class Response(
    @Id
    var id: String? = null,
    var occurredAt: Instant = Instant.now(),
    var customerId: String,
    @Indexed var engagementId: String,
    var isFinal: Boolean,
    var respondents: Set<ContactResource>,
    var appointments: Set<AppointmentResource>,
    var archived: Boolean
) {
  abstract val type: ResponseType
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Response

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id?.hashCode() ?: 0
    }
}

abstract class ResultResponse<TResult>(
    id: String?,
    occurredAt: Instant,
    customerId: String,
    engagementId: String,
    isFinal: Boolean,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    val result: TResult,
    archived: Boolean = false
) : Response(id, occurredAt, customerId, engagementId, isFinal, respondents, appointments, archived)

@Schema(
    description = "A response from the system that an error occurred."
)
class ErrorResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    val errors: List<ErrorDto>,
    archived: Boolean = false
) : Response(id, occurredAt, customerId, engagementId, true, respondents, appointments, archived) {
    override val type: ResponseType
        get() = ResponseType.ERROR
}

@Schema(
    description = "A response from the system regarding an appointment cancellation."
)
class CancellationResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    result: CancellationStatus,
    archived: Boolean = false
) : ResultResponse<CancellationStatus>(
    id, occurredAt, customerId, engagementId, true, respondents, appointments, result, archived
) {
    override val type: ResponseType
        get() = ResponseType.CANCELLATION
}

@Schema(
    description = "A response from one or more contacts regarding confirmation of one or more appointments."
)
class ConfirmationResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    isFinal: Boolean,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    result: ConfirmationStatus,
    archived: Boolean = false,
    val messageId: String? = null
) : ResultResponse<ConfirmationStatus>(
    id, occurredAt, customerId, engagementId, isFinal, respondents, appointments, result, archived
) {
    override val type: ResponseType
        get() = ResponseType.CONFIRMATION
}

@Schema(
    description = "A response from one or more contacts regarding check-in for one or more appointments."
)
class CheckInResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    isFinal: Boolean,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    result: CheckInStatus,
    archived: Boolean = false
): ResultResponse<CheckInStatus>(
    id, occurredAt, customerId, engagementId, isFinal, respondents, appointments, result, archived
) {
    override val type: ResponseType
        get() = ResponseType.CHECK_IN
}

@Schema(
    description = "A response indicating that all engagement workflows have completed successfully."
)
class CompleteResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    archived: Boolean = false
): Response(id, occurredAt, customerId, engagementId, true, respondents, appointments, archived)  {
    override val type: ResponseType
        get() = ResponseType.COMPLETE
}

@Schema(
    description = "A response regarding the successful or failed delivery of a message."
)
class MessageResponse(
    id: String? = null,
    occurredAt: Instant = Instant.now(),
    customerId: String,
    engagementId: String,
    respondents: Set<ContactResource>,
    appointments: Set<AppointmentResource>,
    @Schema(
        description = "The workflow that was executing when the contact was discovered to be unreachable."
    )
    val workflow: EngagementWorkflow,
    @Schema(
        description = "The status of the message at the time this response was created."
    )
    val status: MessageStatus,
    @Schema(
        description = "The ID of the PMX message. If null, a message could not be created for the reasons described in errors."
    )
    @Indexed(sparse = true) val messageId: String? = null,
    @Schema(
        description = "Additional error information."
    )
    val errors: List<ErrorDto>? = null,
    archived: Boolean = false,
    val message: String? = null,
    val altMessage: String? = null,
    val subject: String? = null
): Response(id, occurredAt, customerId, engagementId, isFinal = false, respondents, appointments, archived) {
    override val type: ResponseType
        get() = ResponseType.MESSAGE

}