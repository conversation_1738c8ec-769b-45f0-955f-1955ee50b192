package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateOrEditTemplateRequest
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.TemplateDto
import com.connexin.pmx.server.services.TemplateService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v2/admin/templates")
@Tag(
    name = "Template Admin", description = "Endpoints for administrators to manage templates"
)
class AdminTemplateController(
    private val templateService: TemplateService
) {
    @Operation(
        summary = "Gets a paginated list of templates.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A page of templates."
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority('admin.templates:read')")
    fun find(
        pageable: Pageable
    ): Page<TemplateDto> {
        return templateService.findAll(pageable).map { TemplateDto.from(it) }
    }

    @Operation(
        summary = "Gets a template by ID.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The template record.",
            ),
            ApiResponse(
                responseCode = "404",
                description = "Template not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping(
        "/{id}",
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    @PreAuthorize("hasAuthority('admin.templates:read')")
    fun getById(
        @PathVariable
        id: String
    ): TemplateDto {
        val template = templateService.getById(id)
            ?: throw NotFoundException(errors = listOf(ErrorDto(
                path = "id",
                message = "Not found",
                errorCode = Errors.NOT_FOUND.code
            )))

        return TemplateDto.from(template)
    }

    @GetMapping("/{id}/preview")
    fun preview(
        @PathVariable
        id: String
    ): Map<MessageType, Map<Language, MessageSegments?>> {
        return templateService.preview(id)
            ?: throw NotFoundException(
                errors = listOf(
                    ErrorDto(
                        path = "id",
                        message = "Not found",
                        errorCode = Errors.NOT_FOUND.code
                    )
                )
            )
    }

    @Operation(
        summary = "Creates a new template.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The template was created.",
            ),
            ApiResponse(
                responseCode = "400",
                description = "One or more fields are invalid.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PostMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority('admin.templates:write')")
    fun create(
        @RequestBody
        request: CreateOrEditTemplateRequest
    ): TemplateDto {
        val result = templateService.create(request)

        if (result.success) {
            return TemplateDto.from(result.get())
        } else throw BadRequestException(
            errors = result.errors,
            message = "Could not create template."
        )
    }

    @Operation(
        summary = "Updates an existing template.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "The template was updated.",
            ),
            ApiResponse(
                responseCode = "400",
                description = "One or more fields are invalid.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @PutMapping("/{id}", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority('admin.templates:write')")
    fun update(
        @PathVariable
        id: String,
        @RequestBody
        request: CreateOrEditTemplateRequest
    ): TemplateDto {
        val result = templateService.update(id, request)

        if (result.success) {
            return TemplateDto.from(result.get())
        } else if (result.status == HttpStatus.NOT_FOUND) throw NotFoundException(
            errors = result.errors,
            message = "Template not found."
        ) else throw BadRequestException(
            errors = result.errors,
            message = "Could not update template."
        )
    }

    @Operation(
        summary = "Deletes an existing template.",
        responses = [
            ApiResponse(
                responseCode = "204",
                description = "The template was deleted.",
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("hasAuthority('admin.templates:write')")
    fun delete(
        @PathVariable
        id: String
    ) {
        templateService.delete(id)
    }
}