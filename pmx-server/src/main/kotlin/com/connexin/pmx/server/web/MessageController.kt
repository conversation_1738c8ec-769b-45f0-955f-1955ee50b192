package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.PmxMessage
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.CreateMessageResponse
import com.connexin.pmx.server.services.PmxMessageService
import io.swagger.annotations.ApiOperation
import io.swagger.v3.oas.annotations.Hidden
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/v1/messages")
@Hidden
@ApiOperation(value = "Legacy API for creating messages")
class MessageController(
    val messageService: PmxMessageService
) {
    @PostMapping
    fun createMessage(@RequestBody model: CreateMessageRequest): ResponseEntity<CreateMessageResponse> {
        // create the message record
        val response = messageService.create(model)

        return if(response.success) ResponseEntity.ok(response) else ResponseEntity.badRequest().body(response)
    }

    @GetMapping("/{id}")
    fun getMessageById(@PathVariable id: String): ResponseEntity<PmxMessage> {
        val message = messageService.getById(id)

        return if(message == null) ResponseEntity.notFound().build() else ResponseEntity.ok(message)
    }
}