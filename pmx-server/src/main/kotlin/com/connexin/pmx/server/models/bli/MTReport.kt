package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Report")
data class MTReports(    // text message reports
    @JacksonXmlProperty(localName = "MT")
    @JacksonXmlElementWrapper(useWrapping = false)
    var mtReport: MTReport
)

@JsonRootName("MT")
data class MTReport(

    @JacksonXmlProperty(localName = "OrderID")
    var orderID: String?,

    @JacksonXmlProperty(localName = "UNQID")
    var uniqueId: String?,

    @JacksonXmlProperty(localName = "Project")
    var project: String?,

    @JacksonXmlProperty(localName = "CellPhoneNumber")
    var cellPhoneNumber: String?,

    @JacksonXmlProperty(localName = "Jobstatus")
    var jobStatus: String?,

    @JacksonXmlProperty(localName = "Result")
    var result: String?,

    @JacksonXmlProperty(localName = "Error")
    var error: String?

)
