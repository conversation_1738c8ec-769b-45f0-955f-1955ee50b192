package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Report")
data class EBReports(    // email broadcast reports
    @JacksonXmlProperty(localName = "EB")
    @JacksonXmlElementWrapper(useWrapping = false)
    var ebReport: List<EBReport> = ArrayList()
)

@JsonRootName("EB")
data class EBReport(

    @JacksonXmlProperty(localName = "OrderId")
    var orderId: String?,

    @JacksonXmlProperty(localName = "Project")
    var project: String?,

    @JacksonXmlProperty(localName = "EmailAddress")
    var emailAddress: String?,

    @JacksonXmlProperty(localName = "OpenCount")
    var openCount: String?,

    @JacksonXmlProperty(localName = "LastOpened")
    var lastOpened: String?,

    @JacksonXmlProperty(localName = "JobStatus")
    var jobStatus: String?,

    @JacksonXmlProperty(localName = "Result")
    var result: String?,

    @JacksonXmlProperty(localName = "Error")
    var error: String?,

    @JacksonXmlProperty(localName = "Timestamp") // Ex: 03/17/2014 10:55:41 AM
    var timestamp: String?,

    @JacksonXmlProperty(localName = "Email")
    var email: String?,

    @JacksonXmlProperty(localName = "Name")
    var name: String?
)