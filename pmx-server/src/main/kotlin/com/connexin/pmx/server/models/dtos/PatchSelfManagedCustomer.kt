package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.DeliveryDay
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Duration
import java.time.LocalTime

@Schema(
    description = "Customer settings that self-managed customers can patch."
)
data class PatchSelfManagedCustomer(
    val appointmentTimeDisplayOffset: Duration?,
    val cancellationDeadline: Duration?,
    val deliveryDays: MutableSet<DeliveryDay>,
    val deliveryStartTime: LocalTime,
    val deliveryEndTime: LocalTime,
) : PatchableCustomer {
    companion object {
        fun from(customer: Customer): PatchSelfManagedCustomer {
            return PatchSelfManagedCustomer(
                appointmentTimeDisplayOffset = customer.appointmentTimeDisplayOffset,
                cancellationDeadline = customer.cancellationDeadline,
                deliveryDays = customer.deliveryDays,
                deliveryStartTime = customer.deliveryStartTime,
                deliveryEndTime = customer.deliveryEndTime,
            )
        }
    }
}
