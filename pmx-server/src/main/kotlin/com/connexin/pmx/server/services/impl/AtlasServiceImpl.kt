package com.connexin.pmx.server.services.impl

import com.connexin.atlas.sl.practice.PracticeFacade
import com.connexin.atlas.sl.survey.SurveyFacade
import com.connexin.atlas.sl.survey.dto.RemoteSurveyLinkDto
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.services.AtlasService
import com.connexin.pmx.server.utils.CacheKeys
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service

@Service
class AtlasServiceImpl(
    private val practiceFacade: PracticeFacade,
    private val surveyFacade: SurveyFacade
) : AtlasService {
    @Throws(NotFoundException::class)
    @Cacheable(
        cacheNames = [CacheKeys.organizationsByCustomers],
        key = "#customer.id",
        condition = "#customer.id != null",
        unless = "#result == null"
    )
    override fun getOrganizationId(customer: Customer): Long {
        return if (customer.organizationId != null) customer.organizationId!! else {
            log.info("Fetching organizationId from practice for customer ${customer.id}")
            val response = practiceFacade.getOrganizationByLocalId(customer.id!!)
            response?.id ?: throw NotFoundException(message = "Organization not found by local id: ${customer.id}")
        }
    }

    override fun getSurveyLinks(organizationId: Long, localPatientId: String): List<RemoteSurveyLinkDto> {
        log.info("Fetching survey links for organization $organizationId and PATNO $localPatientId")
        val surveyLinks = surveyFacade.getQueuedSurveyLinksForOrganizationAndPatient(localPatientId, organizationId)
            ?: throw NotFoundException(message = "SurveyQueues not found for patientId: $localPatientId and organizationId: $organizationId")
        return surveyLinks
    }

    companion object {
        private val log = LoggerFactory.getLogger(AtlasServiceImpl::class.java)
    }
}