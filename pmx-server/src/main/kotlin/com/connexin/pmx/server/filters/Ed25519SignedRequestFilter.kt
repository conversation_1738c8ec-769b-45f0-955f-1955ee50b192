package com.connexin.pmx.server.filters

import com.connexin.pmx.server.utils.Base64Util
import org.bouncycastle.crypto.params.Ed25519PublicKeyParameters
import org.bouncycastle.crypto.signers.Ed25519Signer
import org.slf4j.LoggerFactory
import java.nio.charset.StandardCharsets
import java.util.stream.Collectors
import javax.servlet.Filter
import javax.servlet.FilterChain
import javax.servlet.ServletRequest
import javax.servlet.ServletResponse
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


/**
 * A servlet filter that compares the body and timestamp in the header and cryptographically verifies it matches the signature in the header using ED25519.
 */
class Ed25519SignedRequestFilter(
    private val signatureHeaderName: String,
    private val timestampHeaderName: String,
    publicKey: String
): Filter {
    private val pkParams = Ed25519PublicKeyParameters(Base64Util.decodeToBytes(publicKey), 0)

    /**
     * Performs the filter action.
     * @param request The request.
     * @param response The response.
     * @param chain The filter chain.
     */
    override fun doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain) {
        val req = CachedBodyHttpServletRequest(request as HttpServletRequest)
        val res = response as HttpServletResponse

        val validated = try {
            val signature = req.getHeader(signatureHeaderName)
            val timestamp = req.getHeader(timestampHeaderName)
            val payload = req.reader.lines().collect(Collectors.joining(System.lineSeparator()))

            validatePayload(signature, timestamp, payload)
        } catch (ex: Exception) {
            log.error("An unexpected error occurred validating request signature to {}", req.requestURI, ex)

            false
        }

        if(validated) {
            chain.doFilter(req, res)
        } else {
            res.sendError(HttpServletResponse.SC_UNAUTHORIZED)
            log.warn("Signature for request to {} could not be verified.", req.requestURI)
        }
    }

    private fun validatePayload(signature: String, timestamp: String, payload: String): Boolean {
        val signedPayload = "$timestamp|$payload"
        val signedPayloadBytes = signedPayload.toByteArray(StandardCharsets.UTF_8)

        val verifier = Ed25519Signer()
        verifier.init(false, pkParams)
        verifier.update(signedPayloadBytes, 0, signedPayloadBytes.size)

        return verifier.verifySignature(Base64Util.decodeToBytes(signature))
    }

    companion object {
        private val log = LoggerFactory.getLogger(Ed25519SignedRequestFilter::class.java)
    }
}