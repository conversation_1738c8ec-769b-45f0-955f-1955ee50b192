package com.connexin.pmx.server.services.impl

import com.connexin.atlas.sl.survey.dto.RemoteSurveyLinkDto
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.services.AtlasService
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.SurveyService
import com.connexin.pmx.server.utils.use
import com.connexin.urlformattingtool.model.ShortenOptions
import com.connexin.urlformattingtool.service.UrlShortener
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.stereotype.Service

@Service
class SurveyServiceImpl(
    private val atlasService: AtlasService,
    private val urlShortener: UrlShortener,
    private val customerService: CustomerService
): SurveyService {

    override fun generateSurveyLinks(customer: Customer, patientId: String): List<RemoteSurveyLinkDto> {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customer.id),
            MDC.putCloseable(MdcKeys.MDC_PATIENT_ID, patientId)
        ).use {
            try {
                log.info("Generating survey links")

                if (customer.organizationId == null) {
                    val organizationId = atlasService.getOrganizationId(customer)
                    customer.organizationId = organizationId
                    customerService.save(customer)
                }

                log.debug("Fetching survey links")
                val surveyLinks = atlasService.getSurveyLinks(customer.organizationId!!, patientId)

                log.debug("Shortening survey links")
                return surveyLinks.map {
                    arrayOf(
                        MDC.putCloseable(MdcKeys.MDC_SURVEY_QUEUE_ID, it.surveyQueueId.toString()),
                        MDC.putCloseable(MdcKeys.MDC_SURVEY_GUID, it.guid)
                    ).use {
                        it.url = shortenSurveyUrl(it.url)
                        it
                    }
                }.filter { it.url != null }
            } catch (exception: Exception) {
                log.error("Failed to generate survey links due to: {}", exception)
                return emptyList()
            }
        }
    }

    private fun shortenSurveyUrl(originalUrl: String?): String? {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_ORIGINAL_URL, originalUrl)
        ).use {
            if (originalUrl == null) {
                log.warn("Unable to shorten: originalUrl is null")
                return null
            }

            log.debug("Shortening the survey link.")
            return urlShortener.shortenUrl(
                originalUrl,
                ShortenOptions(null) //There is no expiry date for the shortened url
            )
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(SurveyServiceImpl::class.java)
    }
}