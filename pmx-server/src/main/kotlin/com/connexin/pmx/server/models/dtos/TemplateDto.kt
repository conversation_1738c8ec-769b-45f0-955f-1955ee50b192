package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.*
import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    description = "Contains variations of a message template.",
    name = "Template"
)
data class TemplateDto(
    @Schema(
        description = "The template's ID in the PMX+ system."
    )
    val id: String,
    @Schema(
        description = "A name that describes the template."
    )
    val name: String,
    @Schema(
        description = "The engagement workflow this template is associated with."
    )
    val workflow: EngagementWorkflow,
    @Deprecated("Use scenarios instead")
    @Schema(
        description = "Variations of the template for different communication channels and languages.",
        deprecated = true
    )
    val variations: Map<MessageType, Map<Language, Template.Segments>>,
    @Schema(
        description = "Variations of the template for different scenarios, communication channels, and languages."
    )
    val scenarios: Map<TemplateScenario, Map<MessageType, Map<Language, Template.Segments>>>
) {
    companion object {
        fun from(template: Template): TemplateDto {
            return TemplateDto(
                id = template.id!!,
                name = template.name,
                workflow = template.workflow,
                variations = template.variations,
                scenarios = template.scenarios
            )
        }
    }
}