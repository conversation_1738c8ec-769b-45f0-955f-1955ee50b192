package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.client.TelnyxClient
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Errors
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.repositories.CustomerProvisioningOrderRepository
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.use
import com.fasterxml.jackson.dataformat.csv.CsvMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.api.MessagingProfilesApi
import com.telnyx.sdk.api.NumberConfigurationsApi
import com.telnyx.sdk.api.NumberOrdersApi
import com.telnyx.sdk.api.OutboundVoiceProfilesApi
import com.telnyx.sdk.model.*
import net.javacrumbs.shedlock.core.LockAssert
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Slice
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.valiktor.ConstraintViolationException
import org.valiktor.i18n.mapToMessage
import java.math.BigDecimal
import java.time.Instant
import java.util.*

@Service
class CustomerProvisioningServiceImpl(
    private val repository: CustomerProvisioningOrderRepository,
    private val customerService: CustomerService,
    private val urlGenerator: UrlGenerator,
    private val adminService: TelnyxService,
    private val outboundVoiceProfilesApi: OutboundVoiceProfilesApi,
    private val messagingProfilesApi: MessagingProfilesApi,
    private val numberOrdersApi: NumberOrdersApi,
    private val numberConfigurationsApi: NumberConfigurationsApi,
    private val meterService: MeterService,
    private val telnyxRestApi : TelnyxClient,
    @Value("\${op.pmx.environment}") private val env: String,
    @Value("\${op.pmx.provisioning-page-size}") private val pageSize: Int,
    @Value("\${op.pmx.provisioning-disable-vendor-calls}") private val disableVendorCalls: Boolean,
    @Value("\${op.pmx.provisioning-max-attempts}") private val maxAttempts: Int
) : CustomerProvisioningService {

    private val csvMapper: CsvMapper = CsvMapper()

    init {
        csvMapper.registerModule(KotlinModule.Builder().build())
    }

    fun performNewCustomerProvisionStep(
        context: NewCustomerProvisionContext,
        step: NewCustomerProvisioningStep,
        action: (customer: Customer, order: NewCustomerProvisioningOrder) -> Boolean
    ): Boolean {
        log.debug(
            "[customerId={}, orderId={}] Performing provisioning step {}",
            context.customer.id,
            context.order.id,
            step
        )
        context.order.step = step
        var success = false
        try {
            success = action(context.customer, context.order)
            if (success) {
                log.info(
                    "[customerId={}, orderId={}] Successfully completed {} step",
                    context.customer.id,
                    context.order.id,
                    step
                )
            } else {
                log.warn(
                    "[customerId={}, orderId={}] Could not complete {} step. See provisioning activity or prior log entries for more details.",
                    context.customer.id,
                    context.order.id,
                    step
                )
            }
        } catch (ex: ApiException) {
            log.error(
                "[customerId={}, orderId={}] Telnyx API returned an error during the {} step",
                context.customer.id,
                context.order.id,
                step,
                ex
            )
            context.order.addActivity(
                "Received error from Telnyx during $step phase. Exception: $ex",
                error = true
            )
        } catch (ex: Exception) {
            log.error(
                "[customerId={}, orderId={}] An unexpected error occurred while performing step {}",
                context.customer.id,
                context.order.id,
                step,
                ex
            )
            context.order.addActivity(
                "An unexpected error occurred during $step phase. Exception: $ex",
                error = true
            )
        }

        return success
    }



    fun performBillingGroupStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.BILLING_GROUP
        ) { customer: Customer, order: NewCustomerProvisioningOrder ->
            customer.telnyxConfig.billingGroupId = adminService.createBillingGroup(buildResourceName(customer)).id

            order.addActivity(
                "Created Telnyx billing group ${customer.telnyxConfig.billingGroupId}"
            )

            log.info(
                "[customerId={}, orderId={}] Created billing group {}",
                customer.id,
                order.id,
                customer.telnyxConfig.billingGroupId
            )

            true
        }
    }

    fun performOutboundVoiceProfileStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.OUTBOUND_VOICE_PROFILE
        ) { customer: Customer, order: NewCustomerProvisioningOrder ->
            val callRecording = OutboundCallRecording()
            callRecording.callRecordingType = OutboundCallRecording.CallRecordingTypeEnum.NONE

            val outboundRequest = CreateOutboundVoiceProfileRequest()
            outboundRequest.dailySpendLimitEnabled = false
            outboundRequest.enabled = true
            outboundRequest.name = buildResourceName(customer)
            outboundRequest.tags = listOf(env, "opmedid-${customer.id}")
            outboundRequest.servicePlan = ServicePlan.GLOBAL
            outboundRequest.trafficType = TrafficType.CONVERSATIONAL
            outboundRequest.whitelistedDestinations = listOf("US", "CA", "PR", "VI")
            outboundRequest.callRecording = callRecording
            outboundRequest.billingGroupId = UUID.fromString(customer.telnyxConfig.billingGroupId!!)

            val res = outboundVoiceProfilesApi.createVoiceProfile(outboundRequest)

            log.debug("Create outbound voice profile response {}", res)

            customer.telnyxConfig.outboundVoiceProfileId = res.data!!.id!!

            order.addActivity(
                "Created Telnyx outbound voice profile ${customer.telnyxConfig.outboundVoiceProfileId}"
            )

            log.info(
                "[customerId={}, orderId={}] Created outbound voice profile {}",
                customer.id,
                order.id,
                customer.telnyxConfig.outboundVoiceProfileId
            )

            true
        }
    }

    fun performMessagingProfileStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.MESSAGING_PROFILE
        ) { customer: Customer, order: NewCustomerProvisioningOrder ->
            val numberPoolSettings = NumberPoolSettings()
            numberPoolSettings.geomatch = false
            numberPoolSettings.longCodeWeight = BigDecimal.valueOf(1)
            numberPoolSettings.tollFreeWeight = BigDecimal.valueOf(10)
            numberPoolSettings.skipUnhealthy = false
            val messagingProfileRequest = CreateMessagingProfileRequest()
            messagingProfileRequest.name = buildResourceName(customer, suffix = " - PMX")
            messagingProfileRequest.enabled = true
            messagingProfileRequest.numberPoolSettings = numberPoolSettings
            messagingProfileRequest.webhookApiVersion = CreateMessagingProfileRequest.WebhookApiVersionEnum._2
            messagingProfileRequest.webhookUrl = urlGenerator.pmxMessagingWebhook().toString()
            messagingProfileRequest.whitelistedDestinations = listOf("US", "CA", "PR", "VI")

            val res = messagingProfilesApi.createMessagingProfile(messagingProfileRequest)

            log.debug("Create messaging profile response {}", res)

            customer.telnyxConfig.messagingProfileId = res.data!!.id!!.toString()

            order.addActivity(
                "Created Telnyx messaging profile ${customer.telnyxConfig.messagingProfileId}"
            )
            log.info(
                "[customerId={}, orderId={}] Created message profile {}",
                customer.id,
                order.id,
                customer.telnyxConfig.messagingProfileId
            )

            true
        }
    }

    fun performCallControlConnectionStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.CALL_CONTROL_CONNECTION
        ) { customer: Customer, order: NewCustomerProvisioningOrder ->
            val res = adminService.createCallControlConnection(
                buildResourceName(customer, suffix = " - PMX"),
                urlGenerator.pmxVoiceWebhook().toString(),
                customer.telnyxConfig.outboundVoiceProfileId!!
            )

            log.debug("Create call control application response {}", res)

            customer.telnyxConfig.callControlConnectionId = res.id

            order.addActivity(
                "Created Telnyx call control connection ${customer.telnyxConfig.callControlConnectionId}"
            )

            log.info(
                "[customerId={}, orderId={}] Created call control application {}",
                customer.id,
                order.id,
                customer.telnyxConfig.callControlConnectionId
            )

            true
        }
    }

    fun performOrderNumberStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.ORDER_NUMBER
        ) { customer: Customer, order: NewCustomerProvisioningOrder ->
            if (customer.telnyxConfig.phoneNumbers.isEmpty()) {
                // first we have to search for available numbers
                val prospectiveNumbers = adminService.searchTollFreeNumbers()
                log.info(
                    "[customerId={}, orderId={}] Found {} prospective numbers: {}",
                    customer.id,
                    order.id,
                    prospectiveNumbers.size,
                    prospectiveNumbers.map { it.phoneNumber }
                )

                if (prospectiveNumbers.isNotEmpty()) {
                    // if we find at least one, take the first one and order it
                    log.info(
                        "[customerId={}, orderId={}] Selecting first acceptable number {}",
                        customer.id,
                        order.id,
                        prospectiveNumbers.first()
                    )
                    val number = prospectiveNumbers.first().phoneNumber
                    order.addActivity(
                        "Found acceptable toll-free number $number"
                    )
                    val request = CreateNumberOrderRequest()
                    val telnyxNumber = PhoneNumber()
                    telnyxNumber.phoneNumber = number
                    request.phoneNumbers = listOf(telnyxNumber)
                    request.billingGroupId = customer.telnyxConfig.billingGroupId
                    request.connectionId = customer.telnyxConfig.callControlConnectionId
                    request.messagingProfileId = customer.telnyxConfig.messagingProfileId
                    request.customerReference = "$env ${customer.id} PMX"

                    val res = numberOrdersApi.createNumberOrder(request)

                    log.debug("Create number order response {}", res)

                    val numberResult = res.data!!.phoneNumbers!!.first()
                    val pmxNumber = Customer.TelnyxConfig.PhoneNumber(
                        id = numberResult.id!!.toString(),
                        orderStatus = telnyxToPmxNumberStatus(numberResult.status!!),
                        phoneNumber = numberResult.phoneNumber!!,
                        orderId = res.data!!.id!!.toString(),
                        defaultMessaging = true,
                        defaultVoice = true
                    )
                    customer.telnyxConfig.phoneNumbers.add(pmxNumber)
                    order.addActivity(
                        "Created number order ${pmxNumber.orderId} for ${pmxNumber.phoneNumber}, status is ${pmxNumber.orderStatus}"
                    )
                    log.info(
                        "[customerId={}, orderId={}] Created number order {} for {}",
                        customer.id,
                        order.id,
                        pmxNumber.orderId,
                        pmxNumber.phoneNumber
                    )
                } else {
                    // no suitable numbers were found, so we can't complete provisioning
                    log.error(
                        "[customerId={}, orderId={}] Could not find any suitable numbers for customer!!!",
                        customer.id,
                        order.id,
                    )
                    order.addActivity(
                        "No suitable toll-free numbers supporting sms, voice, and fax could be found.",
                        error = true
                    )
                    return@performNewCustomerProvisionStep false
                }
            }
            true
        }
    }

    fun deleteOutboundVoiceProfileStep(id: String): Boolean {
        return try {
            val response = outboundVoiceProfilesApi.deleteOutboundVoiceProfile(id)
            log.info("Deleted outbound voice profile {}", id)
            log.debug("Delete outbound voice profile response: {}", response)
            true
        } catch (apiException: ApiException) {
            log.error(
                "[outboundVoiceProfileId={}] Telnyx API returned an error when attempting to delete the outbound voice profile: ",
                id,
                apiException
            )
            // 404 (not found) may be considered successful for delete step
            apiException.code == 404
        }
    }

    fun deleteMessagingProfileStep(id: UUID): Boolean {
        return try {
            val response = messagingProfilesApi.deleteMessagingProfile(id)
            log.info("Deleted messaging profile {}", id)
            log.debug("Delete messaging profile response: {}", response)
            true
        } catch (apiException: ApiException) {
            log.error(
                "[messagingProfileId={}] Telnyx API returned an error when attempting to delete the messaging profile: ",
                id,
                apiException
            )
            // 404 (not found) may be considered successful for delete step
            apiException.code == 404
        }
    }

    fun deleteBillingGroupStep(id: UUID): Boolean {
        return try {
            val response = adminService.deleteBillingGroup(id)
            log.info("Deleted billing group {}", id)
            log.debug("Delete billing group response: {}", response)
            true
        } catch (apiException: ApiException) {
            log.error(
                "[billingGroupId={}] Telnyx API returned an error when attempting to delete the billing group: ",
                id,
                apiException
            )
            // 404 (not found) may be considered successful for delete step
            apiException.code == 404
        }
    }

    fun deleteCallControlConnectionStep(id: String): Boolean {
        return try {
            val response = adminService.deleteCallControlConnection(id)
            log.info("Deleted call control connection {}", id)
            log.debug("Delete call control connection app response: {}", response)
            true
        } catch (apiException: ApiException) {
            log.error(
                "[callControlConnectionId={}] Telnyx API returned an error when attempting to delete the call control connection app: ",
                id,
                apiException
            )
            // 404 (not found) may be considered successful for delete step
            apiException.code == 404
        }
    }

    //TODO: Rewrite deletePhoneNumber this match new API Spec
//    fun deletePhoneNumberStep(numberId: String): Boolean {
//        return try {
//            val response = numberConfigurationsApi.deletePhoneNumber(numberId)
//            log.info("Deleted phone number with id {}", numberId)
//            log.debug("Delete phone number response: {}", response)
//            true
//        } catch (apiException: ApiException) {
//            log.error(
//                "[phoneNumberId={}] Telnyx API returned an error when attempting to delete the phoneNumber: ",
//                numberId,
//                apiException
//            )
//            // 404 (not found) may be considered successful for delete step
//            apiException.code == 404
//        }
//    }

    override fun deprovisionV2(customerId: String, billingGroupId : String, outBoundVoiceProfileId: String) : DeprovisionResult{
        val errors = mutableListOf<String>()
        var success = true
        try{
            val customer = customerService.getById(customerId);

            if ( customer == null){
                log.warn("Customer not found.")
                errors.add("Customer not found.")
                success = false
            }else{
                telnyxRestApi.deleteBillingGroup(billingGroupId)
                telnyxRestApi.deleteOutboundVoiceProfile(outBoundVoiceProfileId)
                customerService.deleteById(customerId)
            }
        }catch (e: Exception){
            errors.add(e.message ?: "Unknown error")
            success = false
        }
        return DeprovisionResult(success = success, errors = if(errors.isEmpty()) emptyList() else errors)
    }

    override fun deprovision(customerId: String): DeprovisionResult {
        val customer = customerService.getById(customerId)
        val errors = mutableListOf<String>()
        if (customer == null) {
            log.warn("Customer not found.")
        } else {
            val callControlConnectionId = customer.telnyxConfig.callControlConnectionId
            val messageProfileId = customer.telnyxConfig.messagingProfileId
            val outboundVoiceId = customer.telnyxConfig.outboundVoiceProfileId
            val billingGroupId = customer.telnyxConfig.billingGroupId
            // NOTE: steps are called in reverse order of provisioning

            if (!billingGroupId.isNullOrEmpty()) {
                try {
                    //TODO: Rewrite number delete step to match new API spec
//                    val phoneNumbers =
//                        numberConfigurationsApi.listPhoneNumbers().filterBillingGroupId(billingGroupId).execute()
//                    log.debug("List phone numbers for billing group response: {}", phoneNumbers)
//
//                    if (!phoneNumbers.data.isNullOrEmpty()) {
//                        phoneNumbers.data!!.forEach {
//                            // just double-checking to make sure the phone number belongs to the requested billing group
//                            if (it.billingGroupId != billingGroupId) {
//                                log.warn("Returned billingGroupId ${it.billingGroupId} does not match $billingGroupId")
//                                return@forEach
//                            }
//
//                            val success = deletePhoneNumberStep(it.id!!)
//                            if (!success) {
//                                val error = "Attempt to delete phone number ${it.id} failed"
//                                log.warn(error)
//                                errors.add(error)
//                            }
//                        }
//                    } else {
//                        log.info("No Telnyx phone numbers found. Skipping delete step.")
//                    }
                } catch (apiException: ApiException) {
                    val error = "Attempt to retrieve phone numbers for billing group $billingGroupId failed"
                    log.warn(error)
                    errors.add(error)
                }
            }

            if (callControlConnectionId != null) {
                val success = deleteCallControlConnectionStep(callControlConnectionId)
                if (!success) {
                    val error = "Attempt to delete call control connection $callControlConnectionId failed"
                    log.warn(error)
                    errors.add(error)
                }
            } else {
                log.info("No Telnyx call control connection configured. Skipping delete step.")
            }

            if (messageProfileId != null) {
                val success = deleteMessagingProfileStep(UUID.fromString(messageProfileId))
                if (!success) {
                    val error = "Attempt to delete messaging profile $messageProfileId failed"
                    log.warn(error)
                    errors.add(error)
                }
            } else {
                log.info("No Telnyx messaging profile configured. Skipping delete step.")
            }

            if (outboundVoiceId != null) {
                val success = deleteOutboundVoiceProfileStep(outboundVoiceId)
                if (!success) {
                    val error = "Attempt to delete outbound voice profile $outboundVoiceId failed"
                    log.warn(error)
                    errors.add(error)
                }
            } else {
                log.info("No Telnyx outbound voice profile configured. Skipping delete step.")
            }

            if (billingGroupId != null) {
                val success = deleteBillingGroupStep(UUID.fromString(billingGroupId))
                if (!success) {
                    val error = "Attempt to delete Billing Group $billingGroupId failed"
                    log.warn(error)
                    errors.add(error)
                }
            } else {
                log.info("No Telnyx billing group configured. Skipping delete step.")
            }
        }

        return DeprovisionResult(success = errors.isEmpty(), errors = errors)
    }

    fun performUpdateNumberOrderStatusStep(context: NewCustomerProvisionContext): Boolean {
        return performNewCustomerProvisionStep(
            context,
            NewCustomerProvisioningStep.UPDATE_NUMBER_ORDER_STATUS
        ) { customer, order ->
            for (number in customer.telnyxConfig.phoneNumbers) {
                val res = numberOrdersApi.retrieveNumberOrder(number.orderId)
                val newStatus = telnyxToPmxNumberStatus(res.data!!.phoneNumbers!!.first().status!!)

                if (number.orderStatus != newStatus) {
                    // check if failed and set error
                    order.addActivity(
                        "Status for number order ${number.orderId} has changed from ${number.orderStatus} to $newStatus",
                        error = newStatus == PhoneNumberOrderStatus.FAILURE
                    )
                }
                number.orderStatus = telnyxToPmxNumberStatus(res.data!!.phoneNumbers!!.first().status!!)
            }

            true
        }
    }

    fun performCompleteStep(context: NewCustomerProvisionContext): Boolean {
        if (context.customer.telnyxConfig.phoneNumbers.all { it.orderStatus == PhoneNumberOrderStatus.SUCCESS }) {
            context.order.status = ProvisioningStatus.COMPLETED
            context.customer.status = CustomerStatus.ENABLED
            context.order.addActivity(
                "All ordered numbers have completed successfully, customer is fully provisioned."
            )
            log.info(
                "[customerId={}, orderId={}] Customer has been successfully completed provisioning",
                context.customer.id,
                context.order.id
            )
        } else if (context.customer.telnyxConfig.phoneNumbers.any { it.orderStatus == PhoneNumberOrderStatus.FAILURE }) {
            context.order.status = ProvisioningStatus.FAILED
            log.error(
                "[customerId={}, orderId={}] One or more numbers could not be provisioned. Customer requires manual intervention. Check order activity for more info.",
                context.customer.id,
                context.order.id
            )
        } else {
            log.debug("[customerId={}, orderId={}] Still waiting for {} number order(s) to complete.",
                context.customer.id,
                context.order.id,
                context.customer.telnyxConfig.phoneNumbers.count { it.orderStatus == PhoneNumberOrderStatus.PENDING })
        }

        return true
    }

    private fun provisionNewCustomer(order: NewCustomerProvisioningOrder): ProvisionResponse {

        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, order.customerId),
            MDC.putCloseable(MdcKeys.MDC_PROVISIONING_ORDER_ID, order.id),
            )
            .use {
                val customer = customerService.getById(order.customerId)

                if (customer == null) {
                    log.error("Could not find customer")
                    return ProvisionResponse(success = false)
                }

                if (disableVendorCalls) {
                    log.warn("Cannot complete order, vendor calls are disabled")
                    return ProvisionResponse(success = false)
                }

                val res = try {

                    val context = NewCustomerProvisionContext(
                        order = order,
                        customer = customer,
                        remainingSteps = PROVISIONING_STEPS_IN_ORDER.subList(
                            PROVISIONING_STEPS_IN_ORDER.indexOf(order.step),
                            PROVISIONING_STEPS_IN_ORDER.lastIndex + 1
                        )
                    )

                    if (context.remainingSteps.isNotEmpty()) {
                        log.info("Working on new customer provisioning from step {}", context.remainingSteps.firstOrNull())
                    }

                    var success = true
                    for (step in context.remainingSteps) {
                        val result = when (step) {
                            NewCustomerProvisioningStep.BILLING_GROUP -> performBillingGroupStep(context)
                            NewCustomerProvisioningStep.OUTBOUND_VOICE_PROFILE -> performOutboundVoiceProfileStep(context)
                            NewCustomerProvisioningStep.MESSAGING_PROFILE -> performMessagingProfileStep(context)
                            NewCustomerProvisioningStep.CALL_CONTROL_CONNECTION -> performCallControlConnectionStep(context)
                            NewCustomerProvisioningStep.ORDER_NUMBER -> performOrderNumberStep(context)
                            NewCustomerProvisioningStep.UPDATE_NUMBER_ORDER_STATUS -> performUpdateNumberOrderStatusStep(context)
                            NewCustomerProvisioningStep.COMPLETE -> performCompleteStep(context)
                        }

                        // steps should be completed in order. if a step failed, stop immediately
                        if (!result) {
                            success = false
                            break
                        }
                    }

                    ProvisionResponse(success = success)
                } catch (ex: Exception) {
                    log.error("Unable to process provisioning order", ex)
                    ProvisionResponse(success = false)
                }

                return try {
                    customerService.save(customer)
                    res
                } catch (ex: Exception) {
                    log.error("Unable to save customer", ex)
                    ProvisionResponse(success = false)
                }
            }
    }

    override fun provision(order: CustomerProvisioningOrder): ProvisionResponse {
        val res =
            try {
                when (order) {
                    is NewCustomerProvisioningOrder -> provisionNewCustomer(order)
                    is CsvMultiCustomerProvisioningOrder -> provisionMultiCustomer(order)
                    else -> throw IllegalArgumentException("Unsupported customer provisioning order type.")
                }
            } catch (ex: Exception) {
                log.error("[orderId={}] Unable to process provisioning order", order.id)
                ProvisionResponse(success = false)
            }

        if (!res.success) {
            order.attempts++
        }

        return try {
            repository.save(order)

            res
        } catch (ex: Exception) {
            log.error("[orderId={}] Could not update order ",  order.id, ex)
            ProvisionResponse(success = false)
        }
    }

    private fun provisionMultiCustomer(order: CsvMultiCustomerProvisioningOrder): ProvisionResponse {
        val schema = csvMapper.schema()
            .withHeader()
        val iterator = csvMapper.readerFor(CreateOrderRequest::class.java)
            .with(schema)
            .readValues<CreateOrderRequest>(order.csvContent)

        var index = 0

        while (iterator.hasNext()) {
            val request = iterator.next()

            try {
                val res = create(request)

                if (!res.success) {
                    order.addActivity("Could not create order for customer ${request.opmedId}: ${res.errors}", error = true)
                } else {
                    order.addActivity("Created order ${res.order!!.id} for customer ${request.opmedId}")
                }
            } catch (ex: Exception) {
                log.error("[orderId={}] Unexpected error processing row {} for CSV order", order.id, index)
                order.addActivity("Unexpected exception processing CSV entry at row $index", error = true)
            }
            index++
        }

        order.status = if(order.activity.any { it.error }) ProvisioningStatus.FAILED else ProvisioningStatus.COMPLETED

        return ProvisionResponse(success = true)
    }

    override fun create(request: CreateOrderRequest): CreateOrderResult {
        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            log.error("Unable to create customer because of validation errors", ex)
            return CreateOrderResult(
                success = false,
                errors = ex.constraintViolations.mapToMessage()
                    .map { ErrorDto(path = it.property, message = it.message, errorCode = Errors.VALIDATION_FAILED.code) }
            )
        }
        val customerRequest = CreateCustomerRequest(
            opmedId = request.opmedId,
            name = request.name,
            legacyUsername = request.legacyUsername,
            legacyPassword = request.legacyPassword
        )
        val customerResult = customerService.create(customerRequest)

        if (!customerResult.success) {
            log.error("Could not create customer {}. Check response errors or previous logs for additional details.", request.opmedId)

            return CreateOrderResult(
                success = false,
                errors = customerResult.errors
            )
        }

        val order = NewCustomerProvisioningOrder(
            customerId = request.opmedId,
            status = ProvisioningStatus.IN_PROGRESS,
            step = NewCustomerProvisioningStep.BILLING_GROUP
        )

        return try {
            val saved = repository.save(order)

            CreateOrderResult(success = true, order = saved)
        } catch(ex: Exception) {
            log.error("Could not create order for customer {}", request.opmedId, ex)
            CreateOrderResult(success = false)
        }
    }

    override fun create(request: CreateCsvOrderRequest): CreateOrderResult {
        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            log.error("Unable to create csv order because of validation errors", ex)
            return CreateOrderResult(
                success = false,
                errors = ex.constraintViolations.mapToMessage()
                    .map { ErrorDto(path = it.property, message = it.message, errorCode = Errors.VALIDATION_FAILED.code) }
            )
        }

        val order = CsvMultiCustomerProvisioningOrder(
            status = ProvisioningStatus.IN_PROGRESS,
            csvContent = request.csv
        )

        return try {
            val saved = repository.save(order)

            CreateOrderResult(success = true, order = saved)
        } catch (ex: Exception) {
            log.error("Could not create CSV order", ex)

            CreateOrderResult(success = false)
        }
    }

    override fun getById(id: String): CustomerProvisioningOrder? {
        return repository.findByIdOrNull(id)
    }

    override fun deleteById(id: String) {
        repository.deleteById(id)
    }

    @Scheduled(
        initialDelayString = "\${op.pmx.provisioning-initial-delay}",
        fixedDelayString = "\${op.pmx.provisioning-fixed-delay}",
    )
    @SchedulerLock(name = "scheduledProvision")
    fun scheduledProvision() {
        LockAssert.assertLocked()
        val start = Instant.now()
        log.info("Starting scheduledProvision job. Handling pending provisioning orders.");
        val sample = meterService.startTimer()

        try {
            var items: Slice<CustomerProvisioningOrder>
            var total = 0
            var page = 0

            do {
                log.debug("Getting customer provisioning order page {}", page)

                items = try {
                    repository.findPending(maxAttempts, PageRequest.of(page, pageSize))
                } catch(ex: Exception) {
                    sample.stop(meterService.getFindQueuedTimer())
                    log.error("Could not fetch pending provisioning orders", ex)
                    return
                }
                log.info("Processing {} customer provisioning orders for page {}", items.numberOfElements, page)

                for(order in items.content) {
                    try {
                        provision(order)
                    } catch (ex: Exception) {
                        log.error("An unexpected exception occurred while trying to provision order {}", order.id, ex)
                    }
                }

                total += items.numberOfElements
                log.info("Finished processing page {} of customer provisioning orders", page++)
            } while (!items.isEmpty)

            sample.stop(meterService.getFindQueuedTimer())
            log.info("Finished provisioning {} queued orders", total)
        } finally {
            val end = Instant.now()
            val duration = java.time.Duration.between(start, end)
            log.info("Finished scheduledProvision job. Duration: {} ms", duration.toMillis())
        }
    }

    fun buildResourceName(customer: Customer, suffix: String? = null): String {
        return buildString {
            append(env)
            append(" ")
            append(customer.id)
            append(" ")
            append(customer.name.take(30))
            if (suffix != null) {
                append(suffix)
            }
        }
    }

    companion object {
        private var log = LoggerFactory.getLogger(CustomerProvisioningServiceImpl::class.java)

        val PROVISIONING_STEPS_IN_ORDER = listOf(
            NewCustomerProvisioningStep.BILLING_GROUP,
            NewCustomerProvisioningStep.OUTBOUND_VOICE_PROFILE,
            NewCustomerProvisioningStep.MESSAGING_PROFILE,
            NewCustomerProvisioningStep.CALL_CONTROL_CONNECTION,
            NewCustomerProvisioningStep.ORDER_NUMBER,
            NewCustomerProvisioningStep.UPDATE_NUMBER_ORDER_STATUS,
            NewCustomerProvisioningStep.COMPLETE
        )

        private fun telnyxToPmxNumberStatus(original: PhoneNumber.StatusEnum): PhoneNumberOrderStatus {
            return when (original) {
                PhoneNumber.StatusEnum.PENDING -> PhoneNumberOrderStatus.PENDING
                PhoneNumber.StatusEnum.SUCCESS -> PhoneNumberOrderStatus.SUCCESS
                PhoneNumber.StatusEnum.FAILURE -> PhoneNumberOrderStatus.FAILURE
            }
        }
    }
}