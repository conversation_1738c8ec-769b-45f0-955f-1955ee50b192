package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.repositories.CustomerRepository
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.utils.CacheKeys
import com.connexin.pmx.server.utils.toResponse
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.fge.jsonpatch.JsonPatch
import com.github.fge.jsonpatch.JsonPatchException
import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheConfig
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.cache.annotation.Caching
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpStatus
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.valiktor.ConstraintViolationException
import org.valiktor.i18n.mapToMessage
import java.time.Instant

@Service
@CacheConfig(
    cacheNames = [CacheKeys.customers]
)
class CustomerServiceImpl(
    private val repository: CustomerRepository,
    private val passwordEncoder: PasswordEncoder,
    private val mapper: ObjectMapper
) : CustomerService {
    @Cacheable(
        key = "#opmedId",
        condition = "#opmedId != null && !#disableCache",
        unless = "#result == null"
    )
    override fun getById(opmedId: String, disableCache: Boolean): Customer? {
        log.info("Retrieving customer by id {}", opmedId)
        return repository.findByIdOrNull(opmedId)
    }

    override fun findAll(pageable: Pageable): Page<Customer> {
        return repository.findAll(pageable)
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.customers],
                key = "#customer.id"
            ),
            CacheEvict(
                value = [CacheKeys.customersByCredentials],
                allEntries = true
            ),
        ]
    )
    override fun <Patchable : PatchableCustomer> patch(
        customer: Customer,
        patchable: Patchable,
        patch: JsonPatch
    ): Customer {
        return try {
            val patchedNode = patch.apply(mapper.convertValue(patchable, JsonNode::class.java))
            val patched = mapper.treeToValue(patchedNode, patchable.javaClass)
            when (patched) {
                is PatchAdminCustomer -> {
                    customer.status = patched.status
                    customer.name = patched.name
                    customer.legacyUsername = patched.legacyUsername
                    customer.legacyPassword = patched.legacyPassword
                    customer.appointmentTimeDisplayOffset = patched.appointmentTimeDisplayOffset
                    customer.cancellationDeadline = patched.cancellationDeadline
                    customer.deliveryDays = patched.deliveryDays
                    customer.deliveryStartTime = patched.deliveryStartTime
                    customer.deliveryEndTime = patched.deliveryEndTime
                    customer.telnyxConfig = patched.telnyxConfig
                }

                is PatchSelfManagedCustomer -> {
                    customer.appointmentTimeDisplayOffset = patched.appointmentTimeDisplayOffset
                    customer.cancellationDeadline = patched.cancellationDeadline
                    customer.deliveryDays = patched.deliveryDays
                    customer.deliveryStartTime = patched.deliveryStartTime
                    customer.deliveryEndTime = patched.deliveryEndTime
                }
            }
            log.info("Patched {} with {}", patchable, patched)
            save(customer)
        } catch (ex: JsonPatchException) {
            log.warn("Attempt to patch a disallowed path. {}", patch)
            throw BadRequestException(
                message = "One or more paths could not be patched.",
                cause = ex
            )
        }
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.customers],
                key = "#customer.id"
            )
        ]
    )
    override fun createEngagementRule(customer: Customer, rule: EngagementRule): Response<EngagementRule> {
        val exists = customer.engagementRules.any { it.id.equals(rule.id, ignoreCase = true) }

        if (exists) {
            log.error("Cannot create new engagement rule with id {}: already exists", rule.id)
            return Response.failure(status = HttpStatus.CONFLICT)
        }

        try {
            rule.validate()
        } catch (ex: ConstraintViolationException) {
            return ex.toResponse()
        }

        if (isOverlapping(rule, customer.engagementRules)) {
            log.error("Cannot create new engagement rule with id: {}: overlaps with an existing rule.", rule.id)
            return Response.failure(
                status = HttpStatus.CONFLICT,
                ErrorDto(
                    path = "",
                    errorCode = Errors.OVERLAPPING_ENGAGEMENT_RULES.code,
                    message = Errors.OVERLAPPING_ENGAGEMENT_RULES.message
                )
            )
        }

        customer.engagementRules.add(rule)
        log.info("Added new engagement rule {}", rule.id)

        repository.save(customer)

        return Response.success(customer.engagementRules.first { it.id.equals(rule.id, ignoreCase = true) })
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.customers],
                key = "#customer.id"
            )
        ]
    )
    override fun saveEngagementRule(
        customer: Customer,
        ruleId: String,
        rule: EngagementRule
    ): Response<EngagementRule> {
        val existing = customer.engagementRules.firstOrNull { it.id.equals(ruleId, ignoreCase = true) }

        rule.id = ruleId

        try {
            rule.validate()
        } catch (ex: ConstraintViolationException) {
            return ex.toResponse()
        }

        if (isOverlapping(rule, customer.engagementRules)) {
            log.error("Cannot update engagement rule with id: {}: overlaps with an existing rule.", rule.id)
            return Response.failure(
                status = HttpStatus.CONFLICT,
                ErrorDto(
                    path = "",
                    errorCode = Errors.OVERLAPPING_ENGAGEMENT_RULES.code,
                    message = Errors.OVERLAPPING_ENGAGEMENT_RULES.message
                )
            )
        }

        if (existing != null) {
            customer.engagementRules.remove(existing)
        }
        customer.engagementRules.add(rule)
        log.info("Saved engagement rule {}", rule.id)

        repository.save(customer)

        return Response.success(customer.engagementRules.first { it.id == rule.id })
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.customers],
                key = "#customer.id"
            )
        ]
    )
    override fun deleteEngagementRule(customer: Customer, ruleId: String): Response<EngagementRule> {
        val rule = customer.engagementRules.firstOrNull { ruleId.equals(it.id, ignoreCase = true) }

        if (rule == null) {
            log.warn("Cannot delete engagement rule {}: does not exist", ruleId)
            return Response.failure(status = HttpStatus.NOT_FOUND)
        }

        customer.engagementRules.remove(rule)

        log.info("Deleted engagement rule {}", ruleId)

        repository.save(customer)

        return Response.success(rule)
    }

    override fun save(model: Customer): Customer {
        return repository.save(model)
    }

    @Caching(
        evict = [
            CacheEvict(
                value = [CacheKeys.customers],
                key = "#opmedId"
            ),
            CacheEvict(
                value = [CacheKeys.customersByCredentials],
                allEntries = true
            ),
        ]
    )
    override fun deleteById(opmedId: String) {
        repository.deleteById(opmedId)
    }

    fun getByUsername(username: String): Customer? {
        val result = repository.findByLegacyUsername(username)

        return if (result.isEmpty) {
            log.debug("Customer with legacyUsername {} was not found", username)
            null
        } else {
            result.get()
        }
    }

    @Cacheable(
        value = [CacheKeys.customersByCredentials],
        condition = "#username != null && #password != null",
        unless = "#result == null",
        keyGenerator = "credentialsKeyGenerator"
    )
    override fun authenticate(username: String, password: String): Customer? {
        val customer = getByUsername(username) ?: return null

        return if (customer.status == CustomerStatus.DISABLED) {
            log.warn("Customer {} is disabled", customer.id)
            null
        } else if (customer.status == CustomerStatus.PROVISIONING) {
            log.warn("Customer {} is still in the process of provisioning", customer.id)
            null
        } else if (passwordEncoder.matches(password, customer.legacyPassword)) {
            log.debug("Successfully authenticated Customer {}", customer.id)
            customer
        } else {
            log.warn("Failed authentication attempt for customer {}", customer.id)
            null
        }
    }

    override fun create(request: CreateCustomerRequest): CreateCustomerResponse {
        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            log.error("Unable to create customer because of validation errors", ex)
            return CreateCustomerResponse(
                success = false,
                errors = ex.constraintViolations.mapToMessage()
                    .map {
                        ErrorDto(
                            path = it.property,
                            message = it.message,
                            errorCode = Errors.VALIDATION_FAILED.code
                        )
                    }
            )
        }

        if (repository.findByIdOrNull(request.opmedId) != null) {
            log.error("Unable to create customer {} because customer already exists", request.opmedId)
            return CreateCustomerResponse(
                success = false,
                errors = listOf(
                    ErrorDto(
                        path = "opmedId",
                        message = "Customer already exists",
                        errorCode = Errors.DUPLICATE.code
                    )
                )
            )
        }

        if (repository.findByLegacyUsername(request.legacyUsername).isPresent) {
            log.error(
                "Unable to create customer {} with username {} because username is not unique",
                request.opmedId,
                request.legacyUsername
            )
            return CreateCustomerResponse(
                success = false,
                errors = listOf(
                    ErrorDto(
                        path = "legacyUsername",
                        message = "Must be unique",
                        errorCode = Errors.DUPLICATE_USERNAME.code
                    )
                )
            )
        }

        val customer = Customer(
            id = request.opmedId,
            name = request.name,
            legacyUsername = request.legacyUsername,
            legacyPassword = passwordEncoder.encode(request.legacyPassword),
            status = CustomerStatus.PROVISIONING
        )

        return try {
            log.info("Successfully create customer {}, waiting for provisioning", customer.id)
            CreateCustomerResponse(success = true, customer = save(customer))
        } catch (ex: Exception) {
            log.error("Unable to create customer", ex)
            CreateCustomerResponse(success = false)
        }
    }



    companion object {
        private val log = LoggerFactory.getLogger(CustomerServiceImpl::class.java)

        fun isOverlapping(rule: EngagementRule, existing: Set<EngagementRule>): Boolean {
            val start = rule.startDate ?: Instant.MIN
            val end = rule.endDate ?: Instant.MAX
            val appointmentTypes = rule.appointmentTypes ?: emptySet()
            val staff = rule.staff ?: emptySet()
            val practiceLocations = rule.practiceLocations ?: emptySet()

            log.debug("Checking that rule with id {} and workflow: {} overlaps with existing engagement rules", rule.id, rule.workflow)

            // find any rules where dates overlap
            val overlapping = existing
                .filter {
                    if (it.workflow != rule.workflow) return@filter false

                    val otherStart = it.startDate ?: Instant.MIN
                    val otherEnd = it.endDate ?: Instant.MAX
                    val otherAppointmentTypes = it.appointmentTypes ?: emptySet()
                    val otherStaff = it.staff ?: emptySet()
                    val otherPracticeLocations = it.practiceLocations ?: emptySet()

                    (start <= otherEnd && end >= otherStart) &&
                            (it.allAppointmentTypes || rule.allAppointmentTypes || appointmentTypes.intersect(otherAppointmentTypes).isNotEmpty()) &&
                            (it.allStaff || rule.allStaff || staff.intersect(otherStaff).isNotEmpty()) &&
                            (it.allPracticeLocations || rule.allPracticeLocations || isOverlapping(practiceLocations, otherPracticeLocations)) &&
                            !it.id.equals(rule.id, ignoreCase = true)
                }
            if (overlapping.isNotEmpty()) {
                log.error("Engagement rule with id: {} overlaps with existing rules, ids: {}", rule.id, overlapping.map { it.id })
                return true
            }

            return false
        }

        private fun isOverlapping(
            first: Set<PracticeLocationRule>,
            second: Set<PracticeLocationRule>
        ): Boolean {
            return first.any { p ->
                val locations = p.locations ?: emptySet()
                second.any { p2 ->
                    val otherLocations = p2.locations ?: emptySet()
                    p2.practiceId.equals(p.practiceId, ignoreCase = true)
                            && (p.allLocations || p2.allLocations || locations.intersect(otherLocations).isNotEmpty())
                }
            }
        }
    }
}