package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.ApiException
import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.ConflictException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Errors
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.ErrorResponse
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.web.bind.annotation.*
import org.springframework.web.context.request.ServletWebRequest
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.time.Instant
import javax.servlet.http.HttpServletRequest

@ControllerAdvice
class ApiExceptionHandlerAdvice : ResponseEntityExceptionHandler() {
    @ExceptionHandler(value = [BadRequestException::class])
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @RequestMapping(
        method = [RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.PATCH],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun handleBadRequestException(request: HttpServletRequest, ex: BadRequestException): ResponseEntity<ErrorResponse> {
        return createErrorResponse(request, HttpStatus.BAD_REQUEST, ex)
    }

    @ExceptionHandler(value = [ConflictException::class])
    @ResponseStatus(HttpStatus.CONFLICT)
    @RequestMapping(
        method = [RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.PATCH],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun handleConflictException(request: HttpServletRequest, ex: ConflictException): ResponseEntity<ErrorResponse> {
        return createErrorResponse(request, HttpStatus.CONFLICT, ex)
    }

    @ExceptionHandler(value = [NotFoundException::class])
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @RequestMapping(
        method = [RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.PATCH],
        produces = [MediaType.APPLICATION_JSON_VALUE]
    )
    fun handleNotFoundException(request: HttpServletRequest, ex: NotFoundException): ResponseEntity<ErrorResponse> {
        return createErrorResponse(request, HttpStatus.NOT_FOUND, ex)
    }

    override fun handleHttpMessageNotReadable(
        ex: HttpMessageNotReadableException,
        headers: HttpHeaders,
        status: HttpStatus,
        request: WebRequest
    ): ResponseEntity<Any> {
        val result = ErrorResponse(
            timestamp = Instant.now(),
            status = HttpStatus.BAD_REQUEST.value(),
            message = "Bad Request",
            path = (request as ServletWebRequest).request.requestURI,
            errors = listOf(
                ErrorDto(
                    path = "",
                    errorCode = Errors.MALFORMED_REQUEST.code,
                    message = Errors.MALFORMED_REQUEST.message,
                    details = ex.localizedMessage
                )
            )
        )

        log.warn("{} error occurred for {} resulting in {}", status, request, result)

        return ResponseEntity<Any>(
            result, HttpStatus.BAD_REQUEST
        )
    }

    private fun createErrorResponse(
        request: HttpServletRequest,
        status: HttpStatus,
        ex: ApiException
    ): ResponseEntity<ErrorResponse> {
        val result = ErrorResponse(
            timestamp = Instant.now(),
            status = status.value(),
            message = ex.message ?: status.reasonPhrase,
            path = request.requestURI,
            errors = ex.errors
        )

        log.warn("{} error occurred for {} resulting in {}", status, request, result)

        return ResponseEntity<ErrorResponse>(result, status)
    }

    companion object {
        private val log = LoggerFactory.getLogger(ApiExceptionHandlerAdvice::class.java)
    }
}