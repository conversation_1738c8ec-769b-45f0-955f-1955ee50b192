package com.connexin.pmx.server.services.impl

object TelnyxConstants {
    const val CALL_CONTROL_ID = "call_control_id"
    const val PAYLOAD_PATH = "/data/payload"
    const val MESSAGE_SENT = "message.sent"
    const val MESSAGE_FINALIZED = "message.finalized"
    const val MESSAGE_RECEIVED = "message.received"
    const val DELIVERED = "delivered"
    const val ID = "id"
    const val EVENT_TYPE_PATH = "/data/event_type"
    const val EVENT_ID = "/data/id"
    const val CALL_INITIATED = "call.initiated"
    const val DIRECTION_PATH = "/data/payload/direction"
    const val INCOMING = "incoming"
}