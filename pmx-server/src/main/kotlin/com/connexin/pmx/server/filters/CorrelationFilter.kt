package com.connexin.pmx.server.filters

import com.connexin.pmx.server.models.MdcKeys
import org.slf4j.MDC
import org.springframework.web.filter.OncePerRequestFilter
import java.io.IOException
import java.util.*
import javax.servlet.FilterChain
import javax.servlet.ServletException
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * Ensures each request has a correlation ID that is included in logging context and is appended to the response headers.
 */
class CorrelationFilter : OncePerRequestFilter() {
    @Throws(ServletException::class, IOException::class)
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        // looking for x-correlation-id first allows the client to specify a value
        // and thus enables us to trace a value all the way across the system.
        var correlationId = request.getHeader(XCORRELATIONID_HEADER)

        // edge currently provides its own x-cxn-trace, which we should
        // use if x-correlation-id is not available.
        if (correlationId == null) {
            correlationId = request.getHeader(XCXNTRACE_HEADER)
        }

        // otherwise, generate one
        if (correlationId == null) {
            correlationId = UUID.randomUUID().toString()
        }

        // NOTE: correlationId is removed from MDC in MDCCleanupListener
        // clearing it here after doFilter will result in the key being
        // removed and thus not logged if an unhandled exception occurs
        MDC.put(MdcKeys.MDC_CORRELATION_ID, correlationId)
        // ensures the correlation id is included in the response so
        // if a client didn't specify one, they can reference the one we generated
        // if we need to debug in the future.
        response.addHeader(XCORRELATIONID_HEADER, correlationId)
        // complete the request as normal
        filterChain.doFilter(request, response)
    }

    companion object {
        private const val XCXNTRACE_HEADER = "x-cxn-trace"
        private const val XCORRELATIONID_HEADER = "x-correlation-id"
    }
}
