package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.MdcKeys
import com.connexin.pmx.server.models.dtos.ErrorResponse
import com.connexin.pmx.server.models.dtos.FindResponsesCriteria
import com.connexin.pmx.server.models.dtos.ResponseDto
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springdoc.api.annotations.ParameterObject
import org.springdoc.core.converters.models.PageableAsQueryParam
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v2/responses")
@Tag(name = "Response", description = "Endpoints for managing responses from contacts.")
class ResponseController(
    private val customerService: CustomerService,
    private val engagementService: EngagementService
) {

    @Operation(
        summary = "Gets a paginated list of responses from contacts.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A page of responses."
            ),
            ApiResponse(
                responseCode = "404",
                description = "Customer not found.",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(implementation = ErrorResponse::class)
                    )
                ]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized",
                content = [
                    Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = Schema(hidden = true)
                    )
                ]
            )
        ]
    )
    @GetMapping("", produces = [MediaType.APPLICATION_JSON_VALUE])
    @PageableAsQueryParam
    fun getResponses(
        @RequestHeader(X_OPMED, required = true)
        @Parameter(hidden = true)
        customerId: String,
        @ParameterObject
        criteria: FindResponsesCriteria,
        @PageableDefault(size = 100, sort = ["occurredAt"], direction = Sort.Direction.ASC)
        @Parameter(hidden = true)
        pageable: Pageable
    ): Page<ResponseDto> {
        log.info("PUT: /api/v2/responses/ Update engagement request for customer with id {}", customerId);
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                validateCustomer(customerId)

                return engagementService.findResponses(customerId, criteria, pageable)
            }
    }

    private fun validateCustomer(customerId: String) {
        log.info("Validating customer with id {}", customerId)
        customerService.getById(customerId)
            ?: throw NotFoundException(message = "Customer not found.")
    }
    companion object {
        private val log = LoggerFactory.getLogger(ResponseController::class.java)
    }
}