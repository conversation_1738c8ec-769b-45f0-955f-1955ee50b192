package com.connexin.pmx.server.config

import com.connexin.authentication.service.ManagedToken
import com.connexin.authentication.service.impl.GenericManagedToken
import com.connexin.pmx.server.client.OAuth2UnirestAuthenticationInterceptor
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import kong.unirest.Config
import kong.unirest.Unirest
import kong.unirest.UnirestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.ConstructorBinding
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.annotation.PostConstruct


@Configuration
@EnableConfigurationProperties(value = [UnirestConfig.KeyCloakProperties::class])
class UnirestConfig {
    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Bean
    fun unirestInstant(): UnirestInstance {
        val instance = Unirest.spawnInstance()

        setObjectMapper(instance.config(), objectMapper)
        return instance
    }

    @Bean("bridgeUnirestInstance")
    fun bridgeUnirestInstance(@Qualifier("bridgeManagedToken") managedToken: ManagedToken): UnirestInstance {
        return spawnCustomUnirestInstance(managedToken)
    }

    @Bean("bridgeManagedToken")
    fun bridgeManagedToken(properties: KeyCloakProperties): ManagedToken {
        val builder = GenericManagedToken.Builder(
            properties.tokenUrl,
            properties.clientId,
            properties.clientSecret
        )
        return builder.setScopes(properties.bridgeScope).build()
    }

    private fun spawnCustomUnirestInstance(managedToken: ManagedToken): UnirestInstance {
        val instance = Unirest.spawnInstance()
        instance.config().interceptor(OAuth2UnirestAuthenticationInterceptor(managedToken))
        return instance
    }

    @PostConstruct
    fun postConstruct() {
        setObjectMapper(Unirest.config(), objectMapper)
    }

    companion object {
        fun setObjectMapper(config: Config, mapper: ObjectMapper) {
            config.objectMapper = object : ObjectMapper(), kong.unirest.ObjectMapper {
                override fun writeValue(value: Any?): String? {
                    return try {
                        mapper.writeValueAsString(value)
                    } catch (e: JsonProcessingException) {
                        throw RuntimeException(e)
                    }
                }

                override fun <T> readValue(value: String, valueType: Class<T>): T {
                    return try {
                        mapper.readValue(value, valueType)
                    } catch (e: Exception) {
                        throw RuntimeException(e)
                    }
                }
            }
        }
    }

    @ConstructorBinding
    @ConfigurationProperties("keycloak")
    data class KeyCloakProperties (
        val tokenUrl: String? = null,
        val clientId: String? = null,
        val clientSecret: String? = null,
        val bridgeScope: String? = null
    )
}