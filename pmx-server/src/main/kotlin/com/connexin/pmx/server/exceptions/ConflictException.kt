package com.connexin.pmx.server.exceptions

import com.connexin.pmx.server.models.dtos.ErrorDto
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(code = HttpStatus.CONFLICT)
class ConflictException(
    errors: List<ErrorDto>? = null,
    message: String? = null,
    cause: Throwable? = null
): ApiException(errors, message, cause)