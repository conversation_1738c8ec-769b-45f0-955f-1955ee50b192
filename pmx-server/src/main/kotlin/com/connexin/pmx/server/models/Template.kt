package com.connexin.pmx.server.models

import org.springframework.data.annotation.Id
import org.springframework.data.mongodb.core.mapping.Document
import java.io.Serializable

@Document("templates")
data class Template(
    @Id val id: String? = null,
    val name: String,
    val workflow: EngagementWorkflow,
    @Deprecated("Use scenarios instead")
    val variations: Map<MessageType, Map<Language, Segments>> = emptyMap(),
    val scenarios: Map<TemplateScenario, Map<MessageType, Map<Language, Segments>>> = mapOf(TemplateScenario.DEFAULT to variations)
) : Serializable {

    companion object {
        private const val serialVersionUID: Long = 1
    }

    data class Segments(
        val main: String,
        val subject: String? = null,
        val instructions: String? = null
    ) : Serializable {
        companion object {
            private const val serialVersionUID: Long = 1
        }
    }
}