package com.connexin.pmx.server.utils

import com.connexin.pmx.server.exceptions.ZoneNotFoundException
import com.connexin.pmx.server.models.Errors
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.Response
import org.springframework.http.HttpStatus
import org.valiktor.ConstraintViolationException
import org.valiktor.i18n.mapToMessage
import java.util.*

fun <Result> ConstraintViolationException.toResponse(): Response<Result> {
    return Response.failure(
        status = HttpStatus.BAD_REQUEST,
        errors = this.constraintViolations.mapToMessage(baseName = "messages", locale = Locale.ENGLISH)
            .map { ErrorDto(path = it.property, message = Errors.VALIDATION_FAILED.message, errorCode = Errors.VALIDATION_FAILED.code, details = it.message) }
    )
}

fun <Result> ZoneNotFoundException.toResponse(): Response<Result> {
    return Response.failure(
        status = HttpStatus.BAD_REQUEST,
        ErrorDto("zipCode", this.message!!, Errors.VALIDATION_FAILED.code)
    )
}