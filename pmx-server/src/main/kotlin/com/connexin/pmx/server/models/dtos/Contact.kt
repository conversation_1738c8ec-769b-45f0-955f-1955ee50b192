package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.ContactMethod
import com.connexin.pmx.server.models.ContactResource
import com.connexin.pmx.server.models.Language
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "A patient's contact."
)
data class Contact(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The contact's ID in the customer system."
    )
    val id: String,
    @field:Schema(
        description = "The contact's given name."
    )
    val givenName: String? = null,
    @field:Schema(
        description = "The contact's family name"
    )
    val familyName: String? = null,
    @field:Schema(
        description = "The contact's specified method for receiving communications."
    )
    val contactMethod: ContactMethod = ContactMethod.NONE,
    @field:Schema(
        description = "The contacts preferred language for receiving communications."
    )
    val language: Language = Language.ENGLISH,
    @field:Schema(
        description = "The contact's email address."
    )
    val email: String? = null,
    @field:Schema(
        description = "The contact's phone number."
    )
    val phone: String? = null
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(Contact::id).isNotBlank()
            validate(Contact::customerId).isNotBlank()
        }
    }

    companion object {
        fun from(resource: ContactResource) = Contact(
            id = resource.id,
            givenName = resource.givenName,
            familyName = resource.familyName,
            contactMethod = resource.contactMethod,
            language = resource.language,
            email = resource.email,
            phone = resource.phone
        )
    }
}