package com.connexin.pmx.server.web

import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.ConflictException
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.utils.use
import com.github.fge.jsonpatch.JsonPatch
import org.slf4j.MDC
import org.springframework.http.HttpStatus

abstract class AbstractCustomerController<CustomerRep : CustomerRepresentation, Patchable : PatchableCustomer>(
    protected val service: CustomerService
) {
    protected fun doGetCustomer(customerId: String): CustomerRep {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                return mapCustomer(getCustomerResponse(customerId))
            }
    }

    protected fun doPatchCustomer(customerId: String, patch: Json<PERSON>atch, admin: Boolean): CustomerRep {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                val customer = getCustomerResponse(customerId)
                val patchable = when (admin) {
                    true -> PatchAdminCustomer.from(customer)
                    false -> PatchSelfManagedCustomer.from(customer)
                }
                return mapCustomer(service.patch(customer, patchable, patch))
            }
    }

    protected fun doGetEngagementRules(customerId: String): Set<EngagementRule> {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                val customer = getCustomerResponse(customerId)
                return customer.engagementRules
            }
    }

    protected fun doCreateEngagementRule(customerId: String, rule: EngagementRule): EngagementRule {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                val customer = getCustomerResponse(customerId)

                val res = service.createEngagementRule(customer, rule)

                return if (res.success) res.get()
                else if (res.status == HttpStatus.CONFLICT) throw ConflictException(
                    errors = res.errors
                )
                else throw BadRequestException(
                    errors = res.errors,
                    message = "Could not create engagement rule."
                )
            }
    }

    protected fun doSaveEngagementRule(customerId: String, ruleId: String, rule: EngagementRule): EngagementRule {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_RULE_ID, ruleId)
        ).use {
            val customer =
                getCustomerResponse(customerId)

            val res = service.saveEngagementRule(customer, ruleId, rule)

            return if (res.success) res.result!!
            else if (res.status == HttpStatus.CONFLICT) throw ConflictException(
                errors = res.errors
            )
            else throw BadRequestException(
                errors = res.errors,
                message = "Could not update engagement rule."
            )
        }
    }

    protected fun doGetEngagementRule(customerId: String, ruleId: String): EngagementRule {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                val customer =
                    getCustomerResponse(customerId)

                return customer.engagementRules.find { ruleId.equals(it.id, ignoreCase = true) }
                    ?: throw NotFoundException(message = "Engagement rule not found.")
            }
    }

    protected fun doDeleteEngagementRule(customerId: String, ruleId: String) {
        MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, customerId)
            .use {
                val customer =
                    getCustomerResponse(customerId)

                val res = service.deleteEngagementRule(customer, ruleId)


                if (!res.success) throw NotFoundException(errors = res.errors, message = "Engagement rule not found.")
            }
    }

    protected abstract fun mapCustomer(customer: Customer): CustomerRep

    private fun getCustomerResponse(
        customerId: String
    ): Customer {
        return service.getById(customerId, true)
            ?: throw NotFoundException(
                errors = listOf(
                    ErrorDto(
                        path = "customerId",
                        message = "Customer not found.",
                        errorCode = Errors.NOT_FOUND.code
                    )
                ), message = "Customer not found."
            )
    }
}