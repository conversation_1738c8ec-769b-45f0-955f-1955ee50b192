package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.MdcKeys.MDC_CUSTOMER_ID
import com.connexin.pmx.server.services.CustomerProvisioningService
import com.connexin.pmx.server.services.CustomerService
import io.swagger.v3.oas.annotations.Hidden
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*

@RestController
@Hidden
@RequestMapping("/api/v1/customers")
@PreAuthorize("hasRole('ROLE_ADMIN')")
class CustomerV1Controller(
    private val customerService: CustomerService,
    private val customerProvisioningService: CustomerProvisioningService
) {
    @PostMapping
    fun createCustomer(@RequestBody model: Customer): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.save(model))
    }

    @GetMapping("/{id}")
    fun getById(@PathVariable id: String): ResponseEntity<Customer> {
        val auth = SecurityContextHolder.getContext().authentication
        val customer = customerService.getById(id)

        return if(customer == null) ResponseEntity.notFound().build() else ResponseEntity.ok(customer)
    }

    @GetMapping
    fun findAll(pageable: Pageable = PageRequest.of(0, 50)): ResponseEntity<Page<Customer>> {
        return ResponseEntity.ok(customerService.findAll(pageable))
    }

    @DeleteMapping("/{id}")
    fun deleteById(@PathVariable id: String): ResponseEntity<List<String>> {
        MDC.put(MDC_CUSTOMER_ID, id)
        log.info("Received request to DELETE customer")
        try {
            val customer = customerService.getById(id)
            return if (customer != null) {
                log.info("Deprovisioning Telnyx services for ${customer.name}")
                val deprovisionResult = customerProvisioningService.deprovision(id)
                if (deprovisionResult.success) {
                    log.info("Deleting internal record for ${customer.name}")
                    customerService.deleteById(id)
                    ResponseEntity.noContent().build()
                } else {
                    ResponseEntity.internalServerError().body(deprovisionResult.errors)
                }
            } else {
                ResponseEntity.notFound().build()
            }
        } finally {
            MDC.remove(MDC_CUSTOMER_ID)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(CustomerV1Controller::class.java)
    }
}