package com.connexin.pmx.server.statemachine

import org.slf4j.Logger

abstract class StateMachine<StateEnum, EventEnum, S: State<StateEnum, S>, C: Context>(
    protected val log: Logger
) {
    abstract val states: Map<StateEnum, StateTransition<StateEnum, EventEnum, S, C>>

    open fun <E: Event<EventEnum>> transition(state: S, event: E, context: C): S {
        val test = states[state.status]?.on?.get(event.event)
        val transition = if(test == null) {
            log.warn("No {} transition configured from {}, therefore state will not change.", event.event, state.status)
            Transition(id = event.event, target = state.status)
        } else test

        val nextState = state.copyWithState(transition.target)
        log.info("Current state: {}, next state: {}", state.status, nextState.status)

        return if(transition.action == null) {
            nextState
        } else {
            log.info("Executing {}.{} action", state.status, transition.id)
            val nextStateAfterAction = transition.action.invoke(nextState, event, context)
            nextStateAfterAction
        }
    }

    companion object {
        fun <StateEnum, EventEnum, S: State<StateEnum, S>, C: Context> builder(): StateMachineBuilder<StateEnum, EventEnum, S, C> {
            return StateMachineBuilder()
        }
    }

}


