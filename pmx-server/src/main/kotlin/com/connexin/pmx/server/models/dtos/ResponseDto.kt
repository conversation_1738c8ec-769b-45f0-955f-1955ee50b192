package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.*
import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

@Schema(
    description = "A response from one or more contacts to a particular workflow in an engagement.",
    name = "Response",
    oneOf = [ErrorResponseDto::class, CancellationResponseDto::class, ConfirmationResponseDto::class, CheckInResponseDto::class, CompleteResponseDto::class, MessageResponseDto::class]
)
interface ResponseDto {
    val type: ResponseType
    val id: String
    val occurredAt: Instant
    val customerId: String
    val engagementId: String
    @get:JsonProperty("isFinal") val isFinal: Boolean
    val respondents: List<ContactResource>
    val appointments: List<AppointmentResource>
}

@Schema(
    description = "A response from the system that an error occurred."
)
data class ErrorResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>,
    val errors: List<ErrorDto>
) : ResponseDto {
    override val type: ResponseType
        get() = ResponseType.ERROR

    override val isFinal: Boolean
        get() = true
}

@Schema(
    description = "A response from the system regarding an appointment confirmation."
)
data class CancellationResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override var isFinal: Boolean,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>,
    val cancellationStatus: CancellationStatus
) : ResponseDto {
    override val type: ResponseType
        get() = ResponseType.CANCELLATION
}

@Schema(
    description = "A response from one or more contacts regarding confirmation of one or more appointments."
)
data class ConfirmationResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override var isFinal: Boolean,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>,
    val confirmationStatus: ConfirmationStatus,
    val messageId: String?
) : ResponseDto {
    override val type: ResponseType
        get() = ResponseType.CONFIRMATION
}

@Schema(
    description = "A response from one or more contacts regarding check-in for one or more appointments."
)
data class CheckInResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override var isFinal: Boolean,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>,
    val checkInStatus: CheckInStatus
) : ResponseDto {
    override val type: ResponseType
        get() = ResponseType.CHECK_IN
}

@Schema(
    description = "A response that engagement for the following appointments has completed successfully."
)
data class CompleteResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override val isFinal: Boolean,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>

): ResponseDto {
    override val type: ResponseType
        get() = ResponseType.COMPLETE
}

@Schema(
    description = "A response regarding the successful or failed delivery of a message."
)
data class MessageResponseDto(
    override val id: String,
    override val occurredAt: Instant,
    override val customerId: String,
    override val engagementId: String,
    override val isFinal: Boolean,
    override val respondents: List<ContactResource>,
    override val appointments: List<AppointmentResource>,
    val workflow: EngagementWorkflow,
    val status: MessageStatus,
    val messageId: String?,
    val errors: List<ErrorDto>?,
    val message: String?,
    val altMessage: String?,
    val subject: String?
): ResponseDto {
    override val type: ResponseType
        get() = ResponseType.MESSAGE
}