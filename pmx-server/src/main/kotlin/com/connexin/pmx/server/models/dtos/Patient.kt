package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.PatientResource
import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "A patient."
)
data class Patient(
    @field:Schema(
        description = "The customer's OPMED ID.",
        hidden = true
    )
    val customerId: String? = null,
    @field:Schema(
        description = "The patient's ID in the customer system."
    )
    val id: String,
    @field:Schema(
        description = "The patient's given name."
    )
    val givenName: String,
    @field:Schema(
        description = "The patient's family name."
    )
    val familyName: String
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(Patient::id).isNotBlank()
            validate(Patient::customerId).isNotBlank()
            validate(Patient::givenName).isNotBlank()
            validate(Patient::familyName).isNotBlank()
        }
    }

    companion object {
        fun from(resource: PatientResource): Patient {
            return Patient(
                id = resource.id,
                givenName = resource.givenName,
                familyName = resource.familyName
            )
        }
    }
}