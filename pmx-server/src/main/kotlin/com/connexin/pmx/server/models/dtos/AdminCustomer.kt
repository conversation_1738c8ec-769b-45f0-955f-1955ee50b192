package com.connexin.pmx.server.models.dtos

import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.DeliveryDay
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Duration
import java.time.Instant
import java.time.LocalTime

data class AdminCustomer(
    @field:Schema(
        description = "The vendor's customer ID. Must be unique across the entire system."
    )
    val id: String,
    @field:Schema(
        description = "Business name for the customer (not to be confused with the name of a practice or location within the customer's hierarchy)"
    )
    val name: String,
    @field:Schema(
        description = "The customer's status in PMX."
    )
    val status: CustomerStatus,
    @field:Schema(
        description = "The username used to authenticate to the legacy PMX APIs."
    )
    val legacyUsername: String?,
    @field:Schema(
        description = "When the rule was created.",
        readOnly = true
    )
    val createdAt: Instant,
    @field:Schema(
        description = "When the customer was last updated.",
        readOnly = true
    )
    val updatedAt: Instant,
    @field:Schema(
        description = "The days when messages can be sent. Messages will be sent on the next allowed delivery day."
    )
    val deliveryDays: Set<DeliveryDay>,
    @field:Schema(
        description = "The beginning of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.",
        implementation = String::class,
        type = "string",
        format = "time",
        pattern = "\\d{2}:\\d{2}:\\{d}{2}",
        example = "09:00:00"
    )
    val deliveryStartTime: LocalTime,
    @field:Schema(
        description = "The ending of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.",
        implementation = String::class,
        type = "string",
        format = "time",
        pattern = "\\d{2}:\\d{2}:\\{d}{2}",
        example = "17:00:00"
    )
    val deliveryEndTime: LocalTime,
    @field:Schema(
        description = "The minimum timeframe (in seconds) before an appointment start when the contact can cancel an appointment.",
        type = "number",
        format = "float",
        example = "43200.0"
    )
    val cancellationDeadline: Duration?,
    @field:Schema(
        description = "The period of time (in seconds) prior to scheduled appointment time during which patient is asked to arrive.",
        type = "number",
        format = "float",
        example = "900.0"
    )
    val appointmentTimeDisplayOffset: Duration?,
    @field:Schema(
        description = "The Telnyx-specific configuration for the customer.",
    )
    val telnyxConfig: Customer.TelnyxConfig
): CustomerRepresentation {
    companion object {
        fun from(customer: Customer): AdminCustomer {
            return AdminCustomer(
                id = customer.id!!,
                name = customer.name,
                status = customer.status,
                legacyUsername = customer.legacyUsername,
                createdAt = customer.createdAt!!,
                updatedAt = customer.updatedAt!!,
                deliveryDays = customer.deliveryDays,
                deliveryStartTime = customer.deliveryStartTime,
                deliveryEndTime = customer.deliveryEndTime,
                cancellationDeadline = customer.cancellationDeadline,
                appointmentTimeDisplayOffset = customer.appointmentTimeDisplayOffset,
                telnyxConfig = customer.telnyxConfig
            )
        }
    }
}