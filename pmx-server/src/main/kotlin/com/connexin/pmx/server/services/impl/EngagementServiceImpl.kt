package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.annotation.TraceExecutionTime
import com.connexin.pmx.server.config.MDCCoroutineContext
import com.connexin.pmx.server.exceptions.BadRequestException
import com.connexin.pmx.server.exceptions.ZoneNotFoundException
import com.connexin.pmx.server.mappers.EngagementMapper
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.repositories.EngagementRepository
import com.connexin.pmx.server.repositories.ResponseRepository
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.PhoneNumberUtil
import com.connexin.pmx.server.utils.hasApplicableRule
import com.connexin.pmx.server.utils.toResponse
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.fge.jsonpatch.JsonPatch
import com.github.fge.jsonpatch.JsonPatchException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.valiktor.ConstraintViolationException
import java.time.*
import kotlin.math.max
import kotlin.math.min

@Service
class EngagementServiceImpl(
    private val engagementRepository: EngagementRepository,
    private val responseRepository: ResponseRepository,
    private val engagementMapper: EngagementMapper,
    private val mapper: ObjectMapper,
    private val zipCodeService: ZipCodeService,
    private val stateMachine: EngagementStateMachine,
    private val customerService: CustomerService,
    private val pmxMessageService: PmxMessageService,
    @Value("\${op.pmx.engagement-checkpoint-delay-time-seconds}")
    private val engagementCheckpointDelayTimeSeconds: Int
) : EngagementService {
    @TraceExecutionTime
    override fun getById(id: String): Engagement? {
        return engagementRepository.findByIdOrNull(id)
    }

    override fun getByAppointmentId(customerId: String, appointmentId: String): Engagement? {
        val engagements = engagementRepository.findByResource(customerId, appointmentId, AppointmentResource::class.java.name, Pageable.ofSize(1))
        return if (engagements.isEmpty()) null else engagements.first()
    }

    @TraceExecutionTime
    override fun deleteById(id: String): Response<Engagement> {
        log.debug("Received request to delete engagement {}", id)

        val engagement = getById(id)

        val messages = pmxMessageService.findQueuedForEngagement(id)
        if (messages.isNotEmpty()) {
            log.info("Deleting {} queued messages for engagement {}", messages.size, id)
            messages.forEach { pmxMessageService.delete(it) }
        }

        return if (engagement != null) {
            engagementRepository.delete(engagement)

            log.info("Deleted engagement {}", id)

            Response.success(engagement)
        } else {
            log.warn("Cannot delete engagement {}: does not exist", id)
            Response.failure(status = HttpStatus.NOT_FOUND)
        }
    }

    @TraceExecutionTime
    override fun save(model: Engagement): Engagement {
        return engagementRepository.save(model)
    }

    @TraceExecutionTime
    override fun create(request: CreateEngagementRequest): Response<Engagement> {
        val appointmentIds = request.appointments.map { it.id }.toSet()
        MDC.putCloseable(
            MdcKeys.MDC_APPOINTMENT_IDS,
            appointmentIds.toString()
        ).use {
            log.info("Received request to create engagement")

            try {
                request.validate()

                val customerId = request.customerId!!

                val customer = customerService.getById(customerId)

                if (customer == null || customer.status != CustomerStatus.ENABLED) {
                    log.error("Customer {} not found or not enabled", customerId)
                    return Response.failure(
                        status = HttpStatus.NOT_FOUND,
                        ErrorDto(
                            path = "customerId",
                            message = Errors.NOT_FOUND.message,
                            errorCode = Errors.NOT_FOUND.code,
                            details = "Customer not found."
                        )
                    )
                }

                if (request.appointments.all { it.cancelled }) {
                    log.error("Cannot create engagement if all appointments in request are cancelled.")
                    return Response.failure(
                        status = HttpStatus.BAD_REQUEST, ErrorDto(
                            path = "appointments",
                            details = "All appointments in request are cancelled. At least one appointment must be active.",
                            errorCode = Errors.VALIDATION_FAILED.code,
                            message = Errors.VALIDATION_FAILED.message
                        )
                    )
                }

                if (engagementRepository.existsByResources(customerId, appointmentIds, AppointmentResource::class.java.name)) {
                    log.error("An engagement for one of the appointments already exists")
                    return Response.failure(status = HttpStatus.CONFLICT)
                }

                val stableCheckpointDelayTime = min(MAX_ENGAGEMENT_CHECKPOINT_DELAY_TIME_S,
                    max(MIN_ENGAGEMENT_CHECKPOINT_DELAY_TIME_S, engagementCheckpointDelayTimeSeconds.toLong()))

                val engagement = Engagement(
                    customerId = customerId,
                    eventDate = Instant.MAX, // true event date will be calculated and set in mapResources below
                    // Next checkpoint safeguard. This engagement will be processed instantly, this nextCheckpoint is for it to be able
                    // to finish before the next checkpoint query triggers
                    nextCheckpoint = Instant.now().plusSeconds(stableCheckpointDelayTime)
                )
                mapResources(engagement, request.appointments, request.contacts)

                if (!customer.hasApplicableRule(engagement)) {
                    log.warn("Customer has no enabled rules configure that apply to the engagement, so the engagement will not be created.")
                    return Response.failure(
                        status = HttpStatus.CONFLICT, errors = listOf(
                            ErrorDto(path = "", errorCode = Errors.ENGAGEMENT_NO_APPLICABLE_RULES.code, message = Errors.ENGAGEMENT_NO_APPLICABLE_RULES.message)
                        )
                    )
                }

                val created = engagementRepository.save(engagement)

                log.info("Created new engagement with id: {} ", created.id)
                CoroutineScope(Dispatchers.IO + MDCCoroutineContext(MDC.getCopyOfContextMap())).launch {
                    sendEvent(
                        CheckpointEvent(
                            created,
                            customer,
                        )
                    )
                }

                return Response.success(created)
            } catch (ex: ConstraintViolationException) {
                log.error("Could not create engagement, constraint violation exception: ", ex)
                return ex.toResponse()
            } catch (ex: ZoneNotFoundException) {
                log.error("Could not create engagement, zone not found exception: ", ex)
                return ex.toResponse()
            }
        }
    }

    @TraceExecutionTime
    override fun update(request: UpdateEngagementRequest): Response<Engagement> {
        MDC.putCloseable(
            MdcKeys.MDC_APPOINTMENT_IDS,
            "[${request.appointments.joinToString(",") { it.id }}]"
        ).use {
            log.info("Received request to update engagement {}", request.id)

            try {
                request.validate()

                val existing = getById(request.id!!)
                if (existing == null) {
                    log.error("Engagement with id {} not found.", request.id)
                    return Response.failure(status = HttpStatus.NOT_FOUND)
                }

                val customer = customerService.getById(existing.customerId)

                if (customer == null || customer.status != CustomerStatus.ENABLED) {
                    log.error("Customer {} not found or not enabled", existing.customerId)
                    return Response.failure(
                        status = HttpStatus.NOT_FOUND,
                        ErrorDto(
                            path = "customerId",
                            message = Errors.NOT_FOUND.message,
                            errorCode = Errors.NOT_FOUND.code,
                            details = "Customer not found."
                        )
                    )
                }

                val lastEventDate = existing.eventDate
                val newEventDate = earliestStartTime(request.appointments)

                if (lastEventDate != newEventDate) {
                    log.info("Appointment date has changed, resetting the engagement")
                    existing.reset()
                    existing.eventDate = newEventDate
                    existing.nextCheckpoint = Instant.now()
                } else if (existing.status.equals(EngagementStatus.ERROR)) {
                    log.info("Engagement is an error state, resetting the engagement")
                    existing.reset()
                }

                val existingEngagementAppointments = existing.getAppointments()

                if (request.appointments.size > existingEngagementAppointments.size &&
                    existing.status > EngagementStatus.CONFIRM && existing.status != EngagementStatus.BOOK) {
                    log.info("Attempting to attach new appointment to already advanced engagement, creating new engagement instead")
                    return create(CreateEngagementRequest(
                        request.customerId,
                        listOf(getNewAttachedAppointment(existingEngagementAppointments, request.appointments)!!),
                        request.contacts)
                    )
                }

                mapResources(existing, request.appointments, request.contacts)

                val updated = engagementRepository.save(existing)

                if (updated.resources.filterIsInstance<AppointmentResource>().all { it.cancelled }) {
                    log.info("All appointments in engagement are cancelled, sending cancellation event")
                    return Response.success(
                        sendEvent(
                            CancellationEvent(
                                engagement = updated,
                                customer = customer
                            )
                        )
                    )
                }

                if (updated.confirmationStatus == ConfirmationStatus.CONFIRMED) {
                    log.info("All appointments in engagement are confirmed, sending confirmation event")
                    return Response.success(sendEvent(ConfirmationResponseEvent.confirmed(updated, customer)))
                }

                log.info("Updated engagement {}", updated.id)

                return Response.success(updated)
            } catch (ex: ConstraintViolationException) {
                log.error("Could not create engagement, constraint violation exception: ", ex)
                return ex.toResponse()
            } catch (ex: ZoneNotFoundException) {
                log.error("Could not create engagement, zone not found exception: ", ex)
                return ex.toResponse()
            }
        }
    }

    @TraceExecutionTime
    override fun updateContact(request: Contact): Response<ContactResource> {
        log.debug("Received request to update contact {}", request.id)

        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            val response = ex.toResponse<ContactResource>()
            log.error("Unable to update contact. Bad Request: {}", response.errors)
            return response
        }

        val resource = ContactResource(
            id = request.id,
            familyName = request.familyName?.trim(),
            givenName = request.givenName?.trim(),
            email = request.email?.trim(),
            phone = PhoneNumberUtil.canonicalize(request.phone),
            contactMethod = request.contactMethod,
            language = request.language
        )
        val count = engagementRepository.updateResources(request.customerId!!, Instant.now(), resource)

        if (count > 0) {
            log.info("Updated contact {} in {} engagements", request.id, count)
        } else {
            log.info("Contact {} was not found in any engagements", request.id)
        }

        return Response.success(resource)
    }

    @TraceExecutionTime
    override fun updateLocation(request: Location): Response<LocationResource> {
        log.debug("Received request to update location {}", request.id)

        try {
            request.validate()

            val resource = LocationResource(
                id = request.id,
                practiceId = request.id,
                name = request.name.trim(),
                zipCode = request.zipCode.trim(),
                zoneId = zipCodeService.getZoneId(request.zipCode.trim()),
                address = request.address?.trim(),
                phone = PhoneNumberUtil.canonicalize(request.phone),
                email = request.email?.trim()
            )
            val count = engagementRepository.updateResources(request.customerId!!, Instant.now(), resource)

            if (count > 0) {
                log.info("Updated location {} in {} engagements", request.id, count)
            } else {
                log.info("Location {} was not found in any engagements", request.id)
            }

            return Response.success(resource)
        } catch (ex: ConstraintViolationException) {
            val response = ex.toResponse<LocationResource>()
            log.error("Unable to update location. Bad Request: {}", response.errors)
            return response
        } catch (ex: ZoneNotFoundException) {
            return ex.toResponse()
        }
    }

    @TraceExecutionTime
    override fun updatePatient(request: Patient): Response<PatientResource> {
        log.debug("Received request to update patient {}", request.id)

        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            val response = ex.toResponse<PatientResource>()
            log.error("Unable to update patient. Bad Request: {}", response.errors)
            return response
        }

        val resource = PatientResource(
            id = request.id,
            familyName = request.familyName.trim(),
            givenName = request.givenName.trim()
        )
        val count = engagementRepository.updateResources(request.customerId!!, Instant.now(), resource)

        if (count > 0) {
            log.info("Updated patient {} in {} engagements", request.id, count)
        } else {
            log.info("Patient {} was not found in any engagements", request.id)
        }

        return Response.success(resource)
    }

    @TraceExecutionTime
    override fun updateStaff(request: Staff): Response<StaffResource> {
        log.debug("Received request to update staff {}", request.id)

        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            val response = ex.toResponse<StaffResource>()
            log.error("Unable to update staff. Bad Request: {}", response.errors)
            return response
        }

        val resource = StaffResource(
            id = request.id,
            name = request.name.trim()
        )
        val count = engagementRepository.updateResources(request.customerId!!, Instant.now(), resource)

        if (count > 0) {
            log.info("Updated staff {} in {} engagements", request.id, count)
        } else {
            log.info("Staff {} was not found in any engagements", request.id)
        }

        return Response.success(resource)
    }

    @TraceExecutionTime
    override fun updatePractice(request: Practice): Response<PracticeResource> {
        log.debug("Received request to update practice {}", request.id)

        try {
            request.validate()
        } catch (ex: ConstraintViolationException) {
            val response = ex.toResponse<PracticeResource>()
            log.error("Unable to update practice. Bad Request: {}", response.errors)
            return response
        }

        val resource = PracticeResource(
            id = request.id,
            name = request.name.trim()
        )
        val count = engagementRepository.updateResources(request.customerId!!, Instant.now(), resource)

        if (count > 0) {
            log.info("Updated practice {} in {} engagements", request.id, count)
        } else {
            log.info("Practice {} was not found in any engagements", request.id)
        }

        return Response.success(resource)
    }

    @TraceExecutionTime
    override fun patch(engagement: Engagement, patchable: PatchEngagement, patch: JsonPatch): Engagement {
        return try {
            val patchedNode = patch.apply(mapper.convertValue(patchable, JsonNode::class.java))
            val patched = mapper.treeToValue(patchedNode, patchable.javaClass)

            if (patched.status != null) {
                engagement.status = patched.status
            }
            if (patched.confirmationStatus != null) {
                engagement.confirmationStatus = patched.confirmationStatus
            }

            log.info("Patched {} with {}", patchable, patched)

            save(engagement)
        } catch (ex: JsonPatchException) {
            log.warn("Attempt to patch a disallowed path. {}", patch)
            throw BadRequestException(
                message = "One or more paths could not be patched.",
                cause = ex
            )
        }
    }

    @TraceExecutionTime
    override fun findResponses(
        customerId: String,
        criteria: FindResponsesCriteria,
        pageable: Pageable
    ): Page<ResponseDto> {
        val responses = responseRepository.findByCriteria(customerId, criteria, pageable)
        return responses.map { engagementMapper.mapToDto(it) }
    }

    @TraceExecutionTime
    override fun <M : EventMessage> sendEvent(message: M, context: EngagementContext?): Engagement {
        log.debug("Sending {} event to engagement", message.event)
        val result = stateMachine.sendEvent(message, context)
        log.debug("Saving changes to engagement state")
        return engagementRepository.save(result)
    }

    @TraceExecutionTime
    override fun findAwaitingCheckpoint(now: Instant, pageable: Pageable): List<Engagement> {
        return engagementRepository.findAwaitingCheckpoint(now, CHECKPOINT_AWAITING_STATUSES, pageable)
    }

    private fun earliestStartTime(appointments: List<Appointment>) = appointments.minOf {
        val zipCode = it.location.zipCode.trim()
        val zoneId = zipCodeService.getZoneId(zipCode)
        it.startTime ?: it.localStartTime!!.atZone(ZoneId.of(zoneId)).toInstant()
    }

    @TraceExecutionTime
    private fun mapResources(target: Engagement, appointments: List<Appointment>, contacts: List<Contact>) {
        // used to preserve previous check-in statuses, if any
        val currentAppointmentStatuses = target.resources
            .filterIsInstance<AppointmentResource>()
            .associateBy({ it.id }, { it.checkInStatus })
        val currentTargetStaff = target.getStaff()

        target.resources.clear()

        if (appointments.all { it.confirmationStatus == ConfirmationStatus.CONFIRMED }) {
            target.confirmationStatus = ConfirmationStatus.CONFIRMED
        }



        // adds resources contained within appointments
        appointments.forEach {
            val zipCode = it.location.zipCode.trim()
            val zoneId = zipCodeService.getZoneId(zipCode)
            val startTime = it.startTime ?: it.localStartTime!!.atZone(ZoneId.of(zoneId)).toInstant()

            target.eventDate = minOf(target.eventDate, startTime)
            val appointmentStaff = if (it.staff != null) {
                StaffResource(id = it.staff.id, name = it.staff.name.trim())
            } else currentTargetStaff

            target.resources.add(
                AppointmentResource(
                    id = it.id,
                    startTime = startTime,
                    reason = it.reason.trim(),
                    location = it.location.id,
                    staff = appointmentStaff?.id,
                    patient = it.patient.id,
                    appointmentType = it.appointmentTypeId,
                    practice = it.practice?.id,
                    cancelled = it.cancelled,
                    checkInStatus = currentAppointmentStatuses.getOrDefault(it.id, CheckInStatus.NOT_CHECKED_IN)
                )
            )
            target.resources.add(
                PatientResource(
                    id = it.patient.id,
                    givenName = it.patient.givenName.trim(),
                    familyName = it.patient.familyName.trim()
                )
            )
            if (appointmentStaff != null) {
                target.resources.add(
                    StaffResource(
                        id = appointmentStaff.id,
                        name = appointmentStaff.name.trim()
                    )
                )
            }
            target.resources.add(
                LocationResource(
                    id = it.location.id,
                    practiceId = it.location.practiceId,
                    name = it.location.name.trim(),
                    zipCode = zipCode,
                    zoneId = zoneId,
                    address = it.location.address?.trim(),
                    phone = PhoneNumberUtil.canonicalize(it.location.phone),
                    email = it.location.email?.trim()
                )
            )
            if (it.practice != null) {
                target.resources.add(
                    PracticeResource(
                        id = it.practice.id,
                        name = it.practice.name.trim()
                    )
                )
            }
        }

        // adds contact resources
        contacts.forEach {
            target.resources.add(
                ContactResource(
                    id = it.id,
                    familyName = it.familyName?.trim(),
                    givenName = it.givenName?.trim(),
                    email = it.email?.trim(),
                    phone = PhoneNumberUtil.canonicalize(it.phone),
                    contactMethod = it.contactMethod,
                    language = it.language
                )
            )
        }
    }

    private fun getNewAttachedAppointment(existingEngagementAppointments: List<AppointmentResource>, requestAppointments: List<Appointment>): Appointment? {
        val existingEngagementIds = existingEngagementAppointments
            .map { it.id }
            .toHashSet()
        for (requestAppointment in requestAppointments) {
            if (!existingEngagementIds.contains(requestAppointment.id)) {
                return requestAppointment
            }
        }

        return null
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementServiceImpl::class.java)

        // Minimum of 5 minutes delay on engagement creation to next checkpoint trigger
        private const val MIN_ENGAGEMENT_CHECKPOINT_DELAY_TIME_S = 5 * 60L
        // Maximum of 1 hour delay on engagement creation to next checkpoint triggers
        private const val MAX_ENGAGEMENT_CHECKPOINT_DELAY_TIME_S = 1 * 60 * 60L
        private val CHECKPOINT_AWAITING_STATUSES = setOf(
            EngagementStatus.INITIAL,
            EngagementStatus.BOOK,
            EngagementStatus.CONFIRM,
            EngagementStatus.CONFIRMED,
            EngagementStatus.CHECK_IN,
            EngagementStatus.REMIND,
            EngagementStatus.APPOINTMENT_SURVEY,
            EngagementStatus.AWAIT_COMPLETED
        )
    }
}