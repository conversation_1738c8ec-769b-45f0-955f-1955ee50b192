package com.connexin.pmx.server.models.dtos

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.telnyx.sdk.model.OutboundCallRecording

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class OutBoundVoiceProfile(
    val id: String,
    val recordType: String,
    val name: String,
    val connectionsCount: Int,
    val trafficType: String,
    val servicePlan: String,
    val concurrentCallLimit: Int,
    val enabled: Boolean,
    val tags: List<String>,
    val usagePaymentMethod: String,
    val whitelistedDestinations: List<String>,
    val maxDestinationRate: Int,
    val dailySpendLimit: String,
    val dailySpendLimitEnabled: Boolean,
    val callRecording: OutboundCallRecording,
    val billingGroupId: String,
    val createdAt: String,
    val updatedAt: String
)
