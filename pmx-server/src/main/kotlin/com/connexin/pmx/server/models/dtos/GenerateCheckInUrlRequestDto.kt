package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "Object to be used as request body to generate patient check-in url using bridge external api"
)
data class GenerateCheckInUrlRequestDto(
    @field:Schema(
        description = "The appointment's OP Local Id"
    )
    val appointmentLocalId: String,
    @field:Schema(
        description = "The patient's OP Local Id"
    )
    val patientLocalId: String,
    @field:Schema(
        description = "The contact's OP Local Id"
    )
    val contactLocalId: String
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(GenerateCheckInUrlRequestDto::appointmentLocalId).isNotBlank()
            validate(GenerateCheckInUrlRequestDto::patientLocalId).isNotBlank()
            validate(GenerateCheckInUrlRequestDto::contactLocalId).isNotBlank()
        }
    }
}