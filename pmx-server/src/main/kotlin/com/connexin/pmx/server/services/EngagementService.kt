package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.models.dtos.Response
import com.github.fge.jsonpatch.JsonPatch
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.Instant

interface EngagementService {
    /**
     * Retrieves an engagement by ID.
     * @param id The ID of the engagement record.
     * @return The engagement whose ID matches id, or null if not found.
     */
    fun getById(id: String): Engagement?

    /**
     * Retrieves an engagement by appointment ID.
     * @param customerId The ID of the customer.
     * @param appointmentId The ID of the appointment.
     * @return The engagement that contains an appointment resource whose ID matches appointmentId, or null if not found.
     */
    fun getByAppointmentId(customerId: String, appointmentId: String): Engagement?

    /**
     * Deletes an engagement by ID.
     * @param id The ID of the engagement record.
     */
    fun deleteById(id: String): Response<Engagement>

    /**
     * Saves an engagement.
     * @param model The engagement entity to save.
     */
    fun save(model: Engagement): Engagement

    /**
     * Creates an engagement.
     * @return The result of the action.
     */
    fun create(request: CreateEngagementRequest): Response<Engagement>

    /**
     * Updates an engagement.
     * @return The result of the action.
     */
    fun update(request: UpdateEngagementRequest): Response<Engagement>

    /**
     * Updates a contact resource.
     * @param request A DTO containing modified contact fields.
     * @return The result of the action.
     */
    fun updateContact(request: Contact): Response<ContactResource>

    /**
     * Updates a location resource.
     * @param request A DTO containing modified location fields.
     * @return The result of the action.
     */
    fun updateLocation(request: Location): Response<LocationResource>

    /**
     * Updates a patient resource.
     * @param request A DTO containing modified patient fields.
     * @return The result of the action.
     */
    fun updatePatient(request: Patient): Response<PatientResource>

    /**
     * Updates a staff resource.
     * @param request A DTO containing modified staff fields.
     * @return The result of the action.
     */
    fun updateStaff(request: Staff): Response<StaffResource>

    /**
     * Updates a practice resource.
     * @param request A DTO containing modified practice fields.
     * @return The result of the action.
     */
    fun updatePractice(request: Practice): Response<PracticeResource>

    /**
     * Patches specific fields on the engagement.
     * @param engagement The engagement to patch.
     * @param patchable The copy of the engagement values to patch.
     * @param patch The JSON Patch containing the desired changes.
     * @return The patched engagement.
     */
    fun patch(engagement: Engagement, patchable: PatchEngagement, patch: JsonPatch): Engagement

    /**
     * Returns a list of open engagements that are waiting for a checkpoint since the specified time has passed.
     * @param now Include all open engagements whose checkpoint is before this time.
     * @param pageable Paging information.
     * @return A list of open engagements that are awaiting a checkpoint.
     */
    fun findAwaitingCheckpoint(now: Instant, pageable: Pageable): List<Engagement>

    /**
     * Sends an event to the engagement.
     * @param message The message describing the event.
     * @param context Optional context to include when processing the event.
     * @return The new state of the engagement following the processing of the event.
     */
    fun <M: EventMessage> sendEvent(message: M, context: EngagementContext? = null): Engagement

    /**
     * Finds responses matching the given criteria
     * @param customerId The customer ID to filter responses by.
     * @param criteria Additional criteria used to filter responses.
     * @param pageable Paging information.
     * @return A page of responses matching the criteria.
     */
    fun findResponses(customerId: String, criteria: FindResponsesCriteria, pageable: Pageable): Page<ResponseDto>
}