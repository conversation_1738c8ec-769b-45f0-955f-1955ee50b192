package com.connexin.pmx.server.web

import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.SubscriptionPreference
import com.connexin.pmx.server.models.dtos.UnsubscribeDto
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.SubscriptionService
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.server.ResponseStatusException
import java.net.URLEncoder

@Controller
class SubscriptionsController(
    private val pmxMessageService: PmxMessageService,
    private val subscriptionService: SubscriptionService
) {

    companion object {
        private val log = LoggerFactory.getLogger(SubscriptionsController::class.java)
    }

    @GetMapping("/unsubscribe")
    fun unsubscribe(@RequestParam id: String, @RequestParam email: String, model: Model): String {
        log.info("[messageId={}, email={}] GET /unsubscribe", id, email)

        val message = pmxMessageService.getById(id)
        val matched = when (message?.type) {
            MessageType.EMAIL -> message.to == email
            MessageType.EMAIL_BROADCAST -> message.emailRecipients?.any { it.address == email } == true
            else -> false
        }

        if (!matched) {
            log.warn("No email found matching requested parameters")
            throw ResponseStatusException(HttpStatus.BAD_REQUEST)
        }

        model.addAttribute("model", UnsubscribeDto(id, email))

        return "unsubscribe"
    }

    @PostMapping("/unsubscribe")
    fun unsubscribeSubmit(@ModelAttribute values: UnsubscribeDto, model: Model): String {
        log.info("[messageId={}, email={}] POST /unsubscribe", values.messageId, values.email)

        val message = pmxMessageService.getById(values.messageId)
        if (message?.type == MessageType.EMAIL_BROADCAST) {
            val index = message.emailRecipients?.indexOfFirst { it.address == values.email } ?: -1
            if (index > -1) {
                // update message for reporting purposes
                pmxMessageService.patch(message.id!!, mapOf(
                    "emailRecipients.$index.unsubscribed" to true
                ))
            }
        }

        // unsubscribe the address
        val pref = subscriptionService.getById(values.email) ?: SubscriptionPreference(values.email, false)
        pref.subscribed = false
        subscriptionService.save(pref)

        return "redirect:/unsubscribed?email=${URLEncoder.encode(values.email, "utf-8")}"
    }

    @GetMapping("/unsubscribed")
    fun unsubscribed(@RequestParam email: String, model: Model): String {
        model.addAttribute("email", email)
        return "unsubscribed"
    }
}