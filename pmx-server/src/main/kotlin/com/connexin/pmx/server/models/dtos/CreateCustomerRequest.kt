package com.connexin.pmx.server.models.dtos

import io.swagger.v3.oas.annotations.media.Schema
import org.valiktor.functions.isNotBlank

@Schema(
    description = "Describes a new PMX customer record that will be created."
)
data class CreateCustomerRequest(
    @field:Schema(
        description = "The customer's OPMED ID."
    )
    val opmedId: String,
    @field:Schema(
        description = "The customer's name."
    )
    val name: String,
    @field:Schema(
        description = "The customer's legacy PMX username."
    )
    val legacyUsername: String,
    @field:Schema(
        description = "The customer's legacy PMX password."
    )
    val legacyPassword: String,
) {
    fun validate() {
        org.valiktor.validate(this) {
            validate(CreateCustomerRequest::opmedId).isNotBlank()
            validate(CreateCustomerRequest::name).isNotBlank()
            validate(CreateCustomerRequest::legacyUsername).isNotBlank()
            validate(CreateCustomerRequest::legacyPassword).isNotBlank()
        }
    }
}