package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.MdcKeys.MDC_CUSTOMER_ID
import com.connexin.pmx.server.models.MdcKeys.MDC_MESSAGE_ID
import com.connexin.pmx.server.models.MdcKeys.MDC_REMOTE_ID
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.InitiateResult
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.models.dtos.RespondResult
import com.connexin.pmx.server.services.*
import com.connexin.pmx.server.utils.TelnyxErrorCodes
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.telnyx.sdk.model.Error
import io.micrometer.core.instrument.Timer
import net.javacrumbs.shedlock.core.LockAssert
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.PageRequest
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class MessageDispatcherFacadeImpl(
    private val voiceMessageDispatcher: VoiceMessageDispatcher,
    private val smsMessageDispatcher: SmsMessageDispatcher,
    private val emailMessageDispatcher: EmailMessageDispatcher,
    private val messageService: PmxMessageService,
    private val customerService: CustomerService,
    @Value("\${op.pmx.dispatcher-page-size}") private val pageSize: Int,
    private val meterService: MeterService,
    private val engagementService: EngagementService,
    private val responseHandler: EngagementResponseHandler,
    private val mapper: ObjectMapper
) : MessageDispatcherFacade {
    override fun initiate(message: PmxMessage): InitiateResult {
        MDC.put(MDC_MESSAGE_ID, message.id)
        MDC.put(MDC_CUSTOMER_ID, message.customerId)
        log.debug("Initiating message")
        val sample = meterService.startTimer()

        try {
            val customer = customerService.getById(message.customerId)

            if (customer == null) {
                sample.stop(meterService.getDispatchInitiateTimer(message.customerId, message.type, false))
                log.error("Customer with OPMED ID {} could no longer be found when trying to dispatch a message. Customer record was possibly deleted or corrupted, or PmxMessage was corrupted!", message.customerId)
                return InitiateResult(success = false, errors = "Customer ${message.customerId} could not be found!")
            }
            if (!message.engagementId.isNullOrBlank()) {
                val engagement = getEngagement(message)
                if (engagement == null) {
                    log.info("Deleting message associated with deleted engagement {}", message.engagementId)
                    messageService.delete(message)
                    sample.stop(meterService.getDispatchInitiateTimer(message.customerId, message.type, false))
                    return InitiateResult(false, errors = "Engagement ${message.engagementId} could not be found!")
                }
            }
            val dispatcher = resolveDispatcherForMessageType(message.type)
            val result = dispatcher.initiate(message, customer)
            message.attempts++
            val updates = mutableMapOf<String, Any?>("attempts" to message.attempts)

            if (result.success) {
                message.remoteId = result.remoteId
                message.status = MessageStatus.DISPATCHED
                updates["remoteId"] = message.remoteId
                updates["status"] = message.status
                log.debug("Initiated message, vendor assigned remoteId {}", message.remoteId)
                handleEngagementMessageStatusUpdate(message, customer)
            } else if (result.retry) {
                log.debug("Retrying message in {}", result.retryDelay)
                message.sendAfter = message.sendAfter.plus(result.retryDelay)
                updates["sendAfter"] = message.sendAfter
            } else {
                message.status = MessageStatus.FAILED
                message.errors = result.errors
                updates["status"] = message.status
                updates["errors"] = message.errors
                log.debug("Unable to initiate message, attempts {}, errors {}", message.attempts, message.errors)
                handleEngagementMessageStatusUpdate(message, customer, reason = result.reason)
            }

            messageService.patch(message.id!!, updates)

            sample.stop(meterService.getDispatchInitiateTimer(message.customerId, message.type, result.success))

            return result
        } finally {
            MDC.remove(MDC_MESSAGE_ID)
            MDC.remove(MDC_CUSTOMER_ID)
        }
    }

    override fun respond(type: MessageType, payload: String): RespondResult {
        val sample = meterService.startTimer()
        var context = RespondContext(
            type = type,
            payload = payload
        )
        val dispatcher = resolveDispatcherForMessageType(type)

        try {
            val response = dispatcher.beforeRespond(context)

            if (!response.success || !response.accepted) {
                sample.stop(meterService.getDispatchRespondTimer("unknown", type, false))
                log.debug("Ignoring unacceptable response")
                return RespondResult(success = false)
            }

            context = response.context
        } catch (ex: Exception) {
            sample.stop(meterService.getDispatchRespondTimer("unknown", type, false))
            log.error("An unexpected error occurred checking if a response payload is acceptable", ex)
            return RespondResult(success = false)
        }

        // at this point the response is valid and should be processed

        var message: PmxMessage? = null
        if (context.remoteId != null) {
            message = messageService.getByRemoteId(context.remoteId!!)
            if (message == null) {
                sample.stop(meterService.getDispatchRespondTimer("unknown", type, false))
                log.warn("Cannot find message with remoteId {}", context.remoteId)
                return RespondResult(success = false)
            }
        } else if (type == MessageType.SMS && context.receivedMessage?.from != null) {
            val responder = context.receivedMessage!!.from
            message = messageService.getMostRecentlyDeliveredEngagementSmsMessage(responder)
            if (message == null) {
                sample.stop(meterService.getDispatchRespondTimer("unknown", type, false))
                log.warn("Cannot find delivered SMS message to {}", responder)
                return RespondResult(success = false)
            }
        }

        if (message == null) {
            log.warn("Unprocessable response")
            return RespondResult(success = false)
        }

        context = context.copy(message = message)

        return respondToMessageActivity(message, context, dispatcher, sample)
    }

    /**
     * Updates the message status
     * @param [message] the message that was sent to the recipient from PMX+
     * @param [context] the context of the recipient response
     * @param [dispatcher] the type of message handler
     * @param [sample] sample of time
     */
    private fun respondToMessageActivity(
        message: PmxMessage,
        context: RespondContext,
        dispatcher: MessageDispatcher,
        sample: Timer.Sample
    ): RespondResult {

        MDC.put(MDC_MESSAGE_ID, message.id)
        MDC.put(MDC_CUSTOMER_ID, message.customerId)
        MDC.put(MDC_REMOTE_ID, message.remoteId)

        val previousStatus = message.status

        log.debug("Responding to message activity")

        try {
            val response = dispatcher.respond(context)

            val newStatus = message.status

            log.debug("Status before response: {}, current status: {}", previousStatus, newStatus)


            val customer = customerService.getById(message.customerId)!!
            val engagement = getEngagement(message)
            if (newStatus != previousStatus) {
                handleEngagementMessageStatusUpdate(message, customer, engagement)
            }
            handleEngagementConfirmationResponse(message, customer, engagement)

            messageService.update(message)

            sample.stop(meterService.getDispatchRespondTimer(message.customerId, message.type, response.success))

            return response
        } finally {
            MDC.remove(MDC_MESSAGE_ID)
            MDC.remove(MDC_CUSTOMER_ID)
            MDC.remove(MDC_REMOTE_ID)
        }
    }

    private fun getEngagement(message: PmxMessage): Engagement? {
        return if (!message.engagementId.isNullOrBlank()) {
            val engagement = engagementService.getById(message.engagementId)

            if (engagement == null) {
                log.error(
                    "Engagement {} related to message could not be found. Was possibly deleted after messages were sent.",
                    message.engagementId
                )
            }
            engagement
        } else null
    }

    /**
     * Updates the engagement event and verifies if the ContactResource matches with
     * the sender of the message. It uses EngagementService
     * @param [message] the message that was sent to the recipient from PMX+
     * @param [customer] practice information
     * @param [engagement] engagement information
     */
    private fun handleEngagementConfirmationResponse(message: PmxMessage, customer: Customer, engagement: Engagement?) {
        if (engagement == null
            || message.type !in setOf(MessageType.VOICE, MessageType.SMS)
            || message.status != MessageStatus.DELIVERED
        ) {
            return
        }

        val event = when(message.confirmationStatus) {
            ConfirmationStatus.CONFIRMED -> EngagementEvent.CONFIRMED
            ConfirmationStatus.DECLINED -> EngagementEvent.DECLINED
            else -> null
        } ?: return

        val contact = engagement.getContact(message.type, message.to!!)

        log.info(
            "Sending {} result from {} message as {} event to engagement",
            message.confirmationStatus,
            message.type,
            event
        )

        MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, engagement.id)
            .use {
                engagementService.sendEvent(
                    ConfirmationResponseEvent(
                        event = event,
                        engagement = engagement,
                        customer = customer,
                        result = message.confirmationStatus,
                        respondents = if(contact == null) emptySet() else setOf(contact),
                        messageId = message.id
                    )
                )
            }
    }

    private fun handleEngagementMessageStatusUpdate(
        message: PmxMessage,
        customer: Customer,
        engagement: Engagement? = null,
        reason: InitiateResult.Reason? = null
    ) {
        val result = engagement ?: (getEngagement(message) ?: return)

        MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, result.id).use {
            try {
                createMessageResponse(message, result, customer, reason)
            } catch (ex: Exception) {
                log.error("Unable to create engagement message response.", ex)
            }
        }
    }

    private fun createMessageResponse(
        message: PmxMessage,
        engagement: Engagement,
        customer: Customer,
        reason: InitiateResult.Reason?
    ) {
        val contact = engagement.getContact(message.type, message.to)
        val rule = customer.engagementRules.first { message.engagementRuleId.equals(it.id, ignoreCase = true) }

        val errors = if (reason == InitiateResult.Reason.CONSENT) {
            listOf(
                ErrorDto(
                    path = "",
                    message = Errors.BLOCKED_OPTED_OUT.message,
                    errorCode = Errors.BLOCKED_OPTED_OUT.code,
                    details = message.errors
                )
            )
        } else {
            when (message.type) {
                MessageType.SMS, MessageType.VOICE -> mapPhoneErrorsToDtos(message)
                MessageType.EMAIL_BROADCAST, MessageType.EMAIL -> mapEmailErrorsToDtos(message)
            }
        }

        responseHandler.recordMessageResponse(
            engagement = engagement,
            workflow = rule.workflow,
            messageId = message.id,
            contact = contact,
            status = message.status,
            errors = errors,
            message = message.message,
            altMessage = message.altMessage,
            subject = message.subject
        )
    }

    fun mapEmailErrorsToDtos(message: PmxMessage): List<ErrorDto> {
        if (message.emailDeliveryFailureReason == null) {
            return emptyList()
        }

        val error = when (message.emailDeliveryFailureReason) {
            EmailDeliveryFailureReason.ATTACHMENT_REJECTED,
            EmailDeliveryFailureReason.CONTENT_REJECTED,
            EmailDeliveryFailureReason.MESSAGE_TOO_LARGE -> Errors.INVALID_MESSAGE_CONTENT

            EmailDeliveryFailureReason.SPAM_DETECTED -> Errors.BLOCKED_SPAM_TEMPORARY

            EmailDeliveryFailureReason.SENDING_IP_BLOCKED -> Errors.BLOCKED_SPAM_PERMANENT

            EmailDeliveryFailureReason.MAILBOX_FULL -> Errors.MAILBOX_FULL

            EmailDeliveryFailureReason.UNSUBSCRIBED -> Errors.BLOCKED_OPTED_OUT

            EmailDeliveryFailureReason.COMMUNICATION_FAILURE,
            EmailDeliveryFailureReason.RECIPIENT_SERVER_ERROR,
            EmailDeliveryFailureReason.VENDOR_INTERNAL_ERROR,
            EmailDeliveryFailureReason.SOFT_BOUNCE -> Errors.DISPATCH_FAILURE_TEMPORARY

            EmailDeliveryFailureReason.HARD_BOUNCE -> Errors.INVALID_EMAIL_OR_PHONE
            else -> Errors.DELIVERY_FAILURE
        }

        return listOf(
            ErrorDto(
                path = "",
                errorCode = error.code,
                message = error.message,
                details = message.errors
            )
        )
    }

    fun mapPhoneErrorsToDtos(message: PmxMessage): List<ErrorDto> {
        if (message.errors.isNullOrEmpty()) {
            return emptyList()
        }
        val errors: Array<Error> = try {
            val node = mapper.readTree(message.errors!!)

            if (node.isArray) {
                mapper.convertValue(node)
            } else if (node.has("errors")) {
                mapper.convertValue(node.findValue("errors"))
            } else {
                emptyArray()
            }
        } catch (ex: Exception) {
            log.error("Unable to parse error message as JSON", ex)
            emptyArray()
        }

        return errors.map {
            val error = when (it.code) {
                // message is spam
                TelnyxErrorCodes.BLOCKED_AS_SPAM_INTERNAL,
                TelnyxErrorCodes.BLOCKED_AS_SPAM_TEMPORARY,
                TelnyxErrorCodes.BLOCKED_DUE_TO_CONTENT,
                TelnyxErrorCodes.TOO_MANY_REQUESTS -> Errors.BLOCKED_SPAM_TEMPORARY

                // recipient marked our number as spam
                TelnyxErrorCodes.BLOCKED_AS_SPAM_PERMANENT -> Errors.BLOCKED_SPAM_PERMANENT

                // recipient opted out
                TelnyxErrorCodes.BLOCKED_DUE_TO_STOP_MESSAGE -> Errors.BLOCKED_OPTED_OUT

                // recipient number is bad
                TelnyxErrorCodes.INVALID_PHONE_NUMBER,
                TelnyxErrorCodes.INVALID_PHONE_NUMBER_FORMAT,
                TelnyxErrorCodes.NOT_ROUTABLE,
                TelnyxErrorCodes.INVALID_DESTINATION_NUMBER,
                TelnyxErrorCodes.INVALID_TO_ADDRESS,
                -> Errors.INVALID_EMAIL_OR_PHONE

                // configuration issue
                TelnyxErrorCodes.INACTIVE_PHONE_NUMBER,
                TelnyxErrorCodes.INVALID_SOURCE_NUMBER,
                TelnyxErrorCodes.MESSAGING_PROFILE_DISABLED,
                TelnyxErrorCodes.NO_NUMBERS_IN_POOL,
                TelnyxErrorCodes.TOLL_FREE_NUMBER_NOT_VERIFIED,
                TelnyxErrorCodes.FROM_ADDRESS_TEMPORARILY_UNUSABLE -> Errors.CONFIGURATION_ERROR

                // problem with message content
                TelnyxErrorCodes.INVALID_MESSAGE_BODY,
                TelnyxErrorCodes.NO_CONTENT,
                TelnyxErrorCodes.SMS_EXCEEDS_RECOMMENDED_SIZE -> Errors.INVALID_MESSAGE_CONTENT

                // failed but retryable
                TelnyxErrorCodes.MESSAGE_EXPIRED_DURING_TRANSMISSION,
                TelnyxErrorCodes.DESTINATION_UNAVAILABLE,
                TelnyxErrorCodes.MESSAGE_EXPIRED_IN_QUEUE,
                TelnyxErrorCodes.UNHEALTHY_FROM_ADDRESS,
                TelnyxErrorCodes.QUEUE_FULL -> Errors.DISPATCH_FAILURE_TEMPORARY


                // anything else
                else -> Errors.DELIVERY_FAILURE

            }
            ErrorDto(
                path = "",
                errorCode = error.code,
                message = error.message,
                details = "Code: ${it.code} Title: ${it.title} Detail: ${it.detail}"
            )
        }

    }

    private fun resolveDispatcherForMessageType(type: MessageType) = when (type) {
        MessageType.VOICE -> voiceMessageDispatcher
        MessageType.SMS -> smsMessageDispatcher
        MessageType.EMAIL, MessageType.EMAIL_BROADCAST -> emailMessageDispatcher
    }

    @Scheduled(
        initialDelayString = "\${op.pmx.dispatcher-initial-delay}",
        fixedDelayString = "\${op.pmx.dispatcher-fixed-delay}",
    )
    @SchedulerLock(name = "scheduledDispatch")
    fun scheduledDispatch() {
        LockAssert.assertLocked()

        val start = Instant.now()
        log.info("Starting scheduledDispatch job. Dispatching queued PMX messages.")
        val sample = meterService.startTimer()
        try {
            val queued = try {
                messageService.findReadyToDispatch(Instant.now(), PageRequest.of(0, pageSize))
            } catch (ex: Exception) {
                sample.stop(meterService.getDispatchScheduledTimer())
                log.error("Could not fetch queued PmxMessages", ex)
                return
            }

            log.info("Found {} messages waiting to be dispatched", queued.size)

            for (message in queued) {
                try {
                    // TODO: This usually takes up to 5 minutes on processing 1000 messages on a single instance in production
                    //  come back here and analyze possibility of using coroutines while analyzing possible throttling from our vendors
                    initiate(message)
                } catch (ex: Exception) {
                    log.error("An unexpected exception occurred trying to initiate message {}", message.id, ex)
                }
            }

            sample.stop(meterService.getDispatchScheduledTimer())
            log.info("Finished dispatching queued messages.")
        } finally {
            val end = Instant.now()
            val duration = java.time.Duration.between(start, end)
            log.info("Finished scheduledDispatch job. Duration: {} ms", duration.toMillis())
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(MessageDispatcherFacadeImpl::class.java)
    }
}