package com.connexin.pmx.server.statemachine

class StateTransitionBuilder<StateEnum, EventEnum, S: State<StateEnum, S>, C: Context>(
    private val parent: StateMachineBuilder<StateEnum, EventEnum, S, C>,
    private val state: StateEnum
) {
    private val transitions = mutableMapOf<EventEnum, TransitionBuilder<StateEnum, EventEnum, S, C>>()

    fun and(): StateMachineBuilder<StateEnum, EventEnum, S, C> {
        return parent
    }

    fun on(event: EventEnum): TransitionBuilder<StateEnum, EventEnum, S, C> {
        val builder = TransitionBuilder(
            this, event
        )
        transitions[event] = builder
        return builder
    }

    fun build(): StateTransition<StateEnum, EventEnum, S, C> {
        return StateTransition(
            id = state,
            on = transitions.mapValues { it.value.build() }
        )
    }
}