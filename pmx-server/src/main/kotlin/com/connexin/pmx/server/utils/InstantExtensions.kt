package com.connexin.pmx.server.utils

import java.time.Instant

/**
 * Returns whether the instant is within the start and end instants inclusively. If start and end are both null, the result will always be true.
 * @param start The beginning of the window.
 * @param end The end of the window.
 * @return True if the instant is within the given instants; otherwise, false.
 */
fun Instant.isWithin(start: Instant?, end: Instant?): <PERSON><PERSON><PERSON> {
    return if(start != null && end != null) this.isAfter(start) && this.isBefore(end)
            else if (start != null) this.isAfter(start)
            else if (end != null) this.isBefore(end)
            else true
}

