package com.connexin.pmx.server.models.bli

import com.fasterxml.jackson.annotation.JsonRootName
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

@JsonRootName("Report")
data class ETReports(    // email (single) reports
    @JacksonXmlProperty(localName = "ET")
    @JacksonXmlElementWrapper(useWrapping = false)
    var etReport: ETReport
)

@JsonRootName("ET")
data class ETReport(

    @JacksonXmlProperty(localName = "formid")
    var formId: String?,

    @JacksonXmlProperty(localName = "unqid")
    var uniqueId: String?,

    @JacksonXmlProperty(localName = "orderid")
    var orderId: String?,

    @JacksonXmlProperty(localName = "Project")
    var project: String?,

    @JacksonXmlProperty(localName = "EmailAddress")
    var emailAddress: String?,

    @JacksonXmlProperty(localName = "OpenCount")
    var openCount: String?,

    @JacksonXmlProperty(localName = "LastOpened")
    var lastOpened: String?,

    @JacksonXmlProperty(localName = "jobstatus")
    var jobStatus: String?,

    @JacksonXmlProperty(localName = "result")
    var result: String?,

    @JacksonXmlProperty(localName = "error")
    var error: String?,

    @JacksonXmlProperty(localName = "timestamp")  // Ex: 08/12/2013 02:10:42 PM
    var timestamp: String?

)