package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema

/**
 * The status of an Engagement as it flows through the engagement state machine.
 */
@Schema(
    enumAsRef = true
)
enum class EngagementStatus {
    /**
     * The system is assessing the first step to take in engaging the contacts regarding appointments.
     */
    INITIAL,

    /**
     * The system is seeking appointment confirmation from one of the contacts.
     */
    CONFIRM,

    /**
     * A contact confirmed an appointment.
     */
    CONFIRMED,

    /**
     * The system is attempting to get a contact to perform check-in tasks.
     */
    CHECK_IN,

    /**
     * A contact cancelled or declined an appointment.
     */
    DECLINED,

    /**
     * The system is reminding the contacts about the pending appointment.
     */
    REMIND,

    /**
     * The system is notifying the contacts the practice has cancelled the appointment and is ceasing further engagement.
     */
    CANCEL,

    /**
     * Practice cancelled the appointment.
     */
    CANCELLED,

    /**
     * All tasks are done, but waiting for engagement date/time in case any last minute cancellations, etc. occur. Otherwise, should transition to COMPLETED
     */
    AWAIT_COMPLETED,

    /**
     * All engagement tasks have completed successfully.
     */
    COMPLETED,

    /**
     * An error occurred that prevented the engagement from completing as requested, such as missing configuration.
     */
    ERROR,

    /**
     * The system is sending an appointment booking notification to contacts.
     */
    BOOK,

    /**
     * The system is sending an appointment survey to the contacts, one for each pending survey
     */
    APPOINTMENT_SURVEY
}