package com.connexin.pmx.server.models

import io.swagger.v3.oas.annotations.media.Schema
import java.time.DayOfWeek

@Schema(
    enumAsRef = true
)
enum class DeliveryDay {
    SUNDAY,
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY;

    companion object {
        fun toDayOfWeek(original: Set<DeliveryDay>): Set<DayOfWeek> {
            return original.map {
                when (it) {
                    MONDAY -> DayOfWeek.MONDAY
                    TUESDAY -> DayOfWeek.TUESDAY
                    WEDNESDAY -> DayOfWeek.WEDNESDAY
                    THURSDAY -> DayOfWeek.THURSDAY
                    FRIDAY -> DayOfWeek.FRIDAY
                    SATURDAY -> DayOfWeek.SATURDAY
                    SUNDAY -> DayOfWeek.SUNDAY
                }
            }.toSet()
        }
    }
}