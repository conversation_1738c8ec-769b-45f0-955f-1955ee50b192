package com.connexin.pmx.server.config

import org.slf4j.LoggerFactory
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.lang.reflect.Method
import java.util.concurrent.Executor

@Configuration
@EnableAsync
class AsyncConfig: AsyncConfigurer {
  @Value("\${op.jobs.executor.core-pool-size:5}")
  var corePoolSize: Int = 5

  @Value("\${op.jobs.executor.max-pool-size:5}")
  var maxPoolSize: Int = 5

  @Value("\${op.jobs.executor.queue-size:10000}")
  var queueSize: Int = 10_000

  override fun getAsyncExecutor(): Executor {
    val executor = ThreadPoolTaskExecutor()
    executor.corePoolSize = corePoolSize
    executor.maxPoolSize= maxPoolSize
    executor.queueCapacity = queueSize
    executor.setWaitForTasksToCompleteOnShutdown(true)
    executor.initialize()

    return executor
  }

  override fun getAsyncUncaughtExceptionHandler(): AsyncUncaughtExceptionHandler? {
    return LoggingAsyncUncaughtExceptionHandler()
  }

  class LoggingAsyncUncaughtExceptionHandler: AsyncUncaughtExceptionHandler {
    override fun handleUncaughtException(ex: Throwable, method: Method, vararg params: Any?) {
      log.error("Async call to {} resulted in an uncaught exception", method.name, ex)
    }

    companion object {
      private val log = LoggerFactory.getLogger(LoggingAsyncUncaughtExceptionHandler::class.java)
    }

  }
}