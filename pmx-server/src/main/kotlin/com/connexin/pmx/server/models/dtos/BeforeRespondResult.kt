package com.connexin.pmx.server.models.dtos

/**
 * Defines the result of beforeRespond.
 */
data class BeforeRespondResult(
    /**
     * Whether the handler succeeded or not. Unless an unhandled exception was thrown, this is usually true.
     */
    val success: <PERSON><PERSON><PERSON>,
    /**
     * Whether the response is acceptable to continue handling or not. If false, the response will not be further processed.
     */
    val accepted: <PERSON><PERSON><PERSON>,
    /**
     * The new RespondContext that should be used as a result.
     */
    val context: RespondContext
)
