package com.connexin.pmx.server.security

import org.bouncycastle.crypto.AsymmetricCipherKeyPair
import org.bouncycastle.crypto.generators.Ed25519KeyPairGenerator
import org.bouncycastle.crypto.params.Ed25519KeyGenerationParameters
import java.security.SecureRandom

class AsymmetricCipherKeyPairUtil {
  companion object {
    fun generate(): AsymmetricCipherKeyPair {
      val gen = Ed25519KeyPairGenerator()
      gen.init(Ed25519KeyGenerationParameters(SecureRandom()))
      return gen.generateKeyPair()
    }
  }
}