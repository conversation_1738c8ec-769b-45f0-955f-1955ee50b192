package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.SubscriptionPreference
import com.connexin.pmx.server.repositories.SubscriptionPreferenceRepository
import com.connexin.pmx.server.services.SubscriptionService
import com.connexin.pmx.server.utils.CacheKeys
import org.springframework.cache.annotation.CacheConfig
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
@CacheConfig(
    cacheNames = [CacheKeys.subscriptionsById]
)
class SubscriptionServiceImpl(
    private val repository: SubscriptionPreferenceRepository
) : SubscriptionService {
    @Cacheable(
        key = "#address",
        condition = "#address != null",
        unless = "#address == null"
    )
    override fun getById(address: String): SubscriptionPreference? {
        return repository.findByIdOrNull(address)
    }

    override fun save(preference: SubscriptionPreference): SubscriptionPreference {
        return repository.save(preference)
    }
}