package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.ZoneNotFoundException
import com.connexin.pmx.server.mappers.EngagementMapper
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.repositories.EngagementRepository
import com.connexin.pmx.server.repositories.ResponseRepository
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementStateMachine
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.ZipCodeService
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.databind.node.TextNode
import com.github.fge.jackson.JacksonUtils
import com.github.fge.jsonpatch.JsonPatch
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpStatus
import java.time.Instant
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class EngagementServiceImplTest {

    @MockK
    private lateinit var engagementRepository: EngagementRepository

    @MockK
    private lateinit var responseRepository: ResponseRepository

    @MockK
    private lateinit var engagementMapper: EngagementMapper

    @MockK
    private lateinit var zipCodeService: ZipCodeService

    @MockK
    private lateinit var stateMachine: EngagementStateMachine

    @MockK
    private lateinit var customerService: CustomerService

    @MockK
    private lateinit var pmxMessageService: PmxMessageService

    private lateinit var sut: EngagementServiceImpl

    private lateinit var engagement: Engagement

    private val customer = Customer(
        id = "1",
        name = "test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "confirmation",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateIds = emptyMap(),
                allPracticeLocations = true,
                allAppointmentTypes = true,
                allStaff = true
            )
        )
    )
    private val disabledCustomer = Customer(id = "2", name = "disabled", status = CustomerStatus.DISABLED)
    private val noRulesCustomer = Customer(
        id = "3",
        name = "no rules",
        status = CustomerStatus.ENABLED
    )

    @BeforeEach
    fun setup() {
        engagement = Engagement(
            id = "1",
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now()
        )
        every { engagementRepository.findByIdOrNull("1") } returns engagement
        every { engagementRepository.findByIdOrNull("2") } returns null
        every {
            engagementRepository.findByResource(
                "1",
                "1",
                AppointmentResource::class.java.name,
                Pageable.ofSize(1)
            )
        } returns listOf(engagement)
        every { engagementRepository.findByResource(any(), eq("2"), any(), any()) } returns emptyList()
        every { zipCodeService.getZoneId(any()) } returns "America/New_York"
        every { customerService.getById("1") } returns customer
        every { customerService.getById("2") } returns disabledCustomer
        every { customerService.getById("3") } returns noRulesCustomer
        every { customerService.getById("999") } returns null

        sut = EngagementServiceImpl(
            engagementRepository,
            responseRepository,
            engagementMapper,
            ObjectMapper(),
            zipCodeService,
            stateMachine,
            customerService,
            pmxMessageService,
            0
        )
    }

    @Test
    fun `getById should return Engagement with matching id`() {
        val actual = sut.getById("1")

        assertThat(actual).isEqualTo(engagement)
    }

    @Test
    fun `getById should return null`() {
        val actual = sut.getById("2")

        assertThat(actual).isNull()
    }

    @Test
    fun `getByAppointmentId should return matching Engagement`() {
        val actual = sut.getByAppointmentId("1", "1")

        assertThat(actual).isEqualTo(engagement)
    }

    @Test
    fun `getByAppointmentId should return null`() {
        val actual = sut.getByAppointmentId("1", "2")

        assertThat(actual).isNull()
    }

    @Test
    fun `deleteById should delete`() {
        val message = PmxMessage(
            id = "queued-1",
            type = MessageType.SMS,
            customerId = "1",
            to = "+14405551234",
            message = "test queued",
            status = MessageStatus.QUEUED,
            sendAfter = Instant.now(),
            engagementId = engagement.id
        )
        every { engagementRepository.delete(engagement) } returns Unit
        every { pmxMessageService.findQueuedForEngagement(engagement.id!!) } returns listOf(message)
        every { pmxMessageService.delete(any()) } returns Unit

        val actual = sut.deleteById("1")

        assertThat(actual.success).isTrue
        assertThat(actual.result).isEqualTo(engagement)
        verify { pmxMessageService.delete(message) }
    }

    @Test
    fun `deleteById should return failure if engagement is not found`() {
        val message = PmxMessage(
            id = "queued-1",
            type = MessageType.SMS,
            customerId = "1",
            to = "+14405551234",
            message = "test queued",
            status = MessageStatus.QUEUED,
            sendAfter = Instant.now(),
            engagementId = "2"
        )
        every { pmxMessageService.findQueuedForEngagement("2") } returns listOf(message)
        every { pmxMessageService.delete(any()) } returns Unit

        val actual = sut.deleteById("2")

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
        verify { pmxMessageService.delete(message) }
    }

    @Test
    fun `save should save and return Engagement`() {
        val expected = Engagement(
            id = "1",
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now()
        )
        every { engagementRepository.save(expected) } returns expected

        val actual = sut.save(expected)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `create should fail as bad request if request is missing appointments`() {
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = emptyList(),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun `create should fail as bad request if appointment location is not valid`() {
        val appointment = getValidAppointment()
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                appointment.copy(location = appointment.location.copy(phone = null)),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun `create should fail as bad request if startTime and localStartTime are null`() {
        val appointment = getValidAppointment()
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                appointment.copy(startTime = null, localStartTime = null),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun `create should fail as conflict if engagement already exists for appointment`() {
        every { engagementRepository.existsByResources("1", setOf("1"), AppointmentResource::class.java.name) } returns true

        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.CONFLICT)
    }

    @Test
    fun `create should fail as conflict if engagement matches no rules`() {
        every { engagementRepository.existsByResources("3", setOf("1"), AppointmentResource::class.java.name) } returns false

        val request = CreateEngagementRequest(
            customerId = "3",
            appointments = listOf(
                getValidAppointment(),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.CONFLICT)
        assertThat(actual.errors?.map { it.errorCode }).contains(Errors.ENGAGEMENT_NO_APPLICABLE_RULES.code)
    }

    @Test
    fun `create should fail if customer is not found`() {
        val request = CreateEngagementRequest(
            customerId = "999",
            appointments = listOf(
                getValidAppointment(),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun `create should fail if customer is disabled`() {
        val request = CreateEngagementRequest(
            customerId = "2",
            appointments = listOf(
                getValidAppointment(),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun `create should fail as bad request if all appointments are cancelled`() {
        every { engagementRepository.existsByResources("1", setOf("1"), AppointmentResource::class.java.name) } returns false

        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(cancelled = true),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun `create should fail as bad request if timezone lookup failed`() {
        every { zipCodeService.getZoneId("test") } throws ZoneNotFoundException("Test")
        every { engagementRepository.existsByResources(any(), any(), any()) } returns false

        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(),
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun `create should save engagement using earliest startTime as eventDate`() {
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { engagementRepository.existsByResources(any(), any(), any()) } returns false

        val now = Instant.now()
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(id = "3", startTime = now.plusSeconds(30 * 60)),
                getValidAppointment(id = "1", startTime = now),
                getValidAppointment(id = "2", startTime = now.plusSeconds(15 * 60))
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        verify { engagementRepository.save(match { it.customerId == "1" && it.eventDate == now }) }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `create should save engagement using earliest localStartTime as eventDate`() {
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { engagementRepository.existsByResources(any(), any(), any()) } returns false

        val earliest = LocalDateTime.of(2023, 7, 13, 8, 0, 0)
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(id = "3", localStartTime = earliest.plusSeconds(30 * 60), startTime = null),
                getValidAppointment(id = "1", localStartTime = earliest, startTime = null),
                getValidAppointment(id = "2", localStartTime = earliest.plusSeconds(15 * 60), startTime = null)
            ),
            contacts = emptyList()
        )

        val actual = sut.create(request)

        verify {
            engagementRepository.save(match {
                it.customerId == "1" && it.eventDate == Instant.parse("2023-07-13T12:00:00.000Z")
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `create should save engagement with confirmation status CONFIRMED if all appointments are confirmed`() {
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { engagementRepository.existsByResources(any(), any(), any()) } returns false

        var request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(id = "1").copy(confirmationStatus = ConfirmationStatus.CONFIRMED),
                getValidAppointment(id = "2").copy(confirmationStatus = ConfirmationStatus.CONFIRMED),
            ),
            contacts = emptyList()
        )

        var actual = sut.create(request)

        verify {
            engagementRepository.save(match {
                it.customerId == "1" && it.confirmationStatus == ConfirmationStatus.CONFIRMED
            })
        }

        assertThat(actual.success).isTrue

        request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                getValidAppointment(id = "1").copy(confirmationStatus = ConfirmationStatus.CONFIRMED),
                getValidAppointment(id = "2").copy(confirmationStatus = ConfirmationStatus.UNCONFIRMED),
            ),
            contacts = emptyList()
        )

        actual = sut.create(request)

        verify {
            engagementRepository.save(match {
                it.customerId == "1" && it.confirmationStatus == ConfirmationStatus.UNCONFIRMED
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `create should map resources correctly`() {
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { engagementRepository.existsByResources(any(), any(), any()) } returns false

        val now = Instant.now()
        val request = CreateEngagementRequest(
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = now,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS,
                    phone = "************"
                )
            )
        )

        val actual = sut.create(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.resources.size == 6
                        && engagement.resources.any { it is AppointmentResource && it.id == "a" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" && it.phone == "+14405551234" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" && it.zoneId == "America/New_York" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `update should update existing engagement`() {
        val now = Instant.now()
        val originalTime = now.minusSeconds(30)

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = originalTime,
            nextCheckpoint = originalTime,
            status = EngagementStatus.CONFIRM,
            resources = mutableSetOf(
                AppointmentResource(
                    id = "a",
                    checkInStatus =  CheckInStatus.CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                ),
                AppointmentResource(
                    id = "b",
                    checkInStatus =  CheckInStatus.CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                )
            )
        )
        every { engagementRepository.save((any())) } answers { firstArg() }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
                Appointment(
                    id = "c",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p2", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.eventDate == originalTime
                        && engagement.nextCheckpoint == originalTime
                        && engagement.resources.size == 8
                        && engagement.status == EngagementStatus.CONFIRM
                        && engagement.resources.any { it is AppointmentResource && it.id == "a" && it.checkInStatus == CheckInStatus.CHECKED_IN }
                        && engagement.resources.any { it is AppointmentResource && it.id == "c" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is PatientResource && it.id == "p2" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }


    @Test
    fun `update attaching new engagement should attach it if not advanced engagement`() {
        val now = Instant.now()
        val originalTime = now.minusSeconds(30)

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = originalTime,
            nextCheckpoint = originalTime,
            status = EngagementStatus.CONFIRM,
            resources = mutableSetOf(
                AppointmentResource(
                    id = "a",
                    checkInStatus =  CheckInStatus.NOT_CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                ),
            )
        )
        every { engagementRepository.save((any())) } answers { firstArg() }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
                Appointment(
                    id = "c",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p2", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.eventDate == originalTime
                        && engagement.nextCheckpoint == originalTime
                        && engagement.resources.size == 8
                        && engagement.status == EngagementStatus.CONFIRM
                        && engagement.resources.any { it is AppointmentResource && it.id == "a" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is AppointmentResource && it.id == "c" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is PatientResource && it.id == "p2" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `create new engagement on update for new attached appointment when engagement already advanced from CONFIRM`() {
        val now = Instant.now()
        val originalTime = now.minusSeconds(30)

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = originalTime,
            nextCheckpoint = originalTime,
            status = EngagementStatus.CHECK_IN,
            resources = mutableSetOf(
                AppointmentResource(
                    id = "a",
                    checkInStatus =  CheckInStatus.NOT_CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                ),
            )
        )
        every { engagementRepository.save((any())) } answers { firstArg() }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
                Appointment(
                    id = "c",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p2", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        every { engagementRepository.existsByResources(request.customerId!!, setOf("c"), AppointmentResource::class.java.name) } returns false

        val actual = sut.update(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.eventDate == originalTime
                        && engagement.resources.size == 6
                        && engagement.status == EngagementStatus.INITIAL
                        && !engagement.resources.any { it is AppointmentResource && it.id == "a" && it.checkInStatus == CheckInStatus.CHECKED_IN }
                        && engagement.resources.any { it is AppointmentResource && it.id == "c" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && !engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is PatientResource && it.id == "p2" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `update should update reset engagement if the event date changes`() {
        val now = Instant.now()
        val originalTime = now.minusSeconds(30)

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = originalTime,
            nextCheckpoint = originalTime,
            status = EngagementStatus.CONFIRM,
            contactDeclined = false,
            confirmationStatus = ConfirmationStatus.CONFIRMED,
            confirmationAttempts = 1,
            checkInAttempts = 1,
            reminderAttempts = 1,
            cancellationAttempts = 1,
            resources = mutableSetOf(
                AppointmentResource(
                    id = "a",
                    checkInStatus =  CheckInStatus.CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                ),
                AppointmentResource(
                    id = "b",
                    checkInStatus =  CheckInStatus.CHECKED_IN,
                    appointmentType = "type",
                    startTime = originalTime,
                    patient = "p",
                    reason = "reason",
                    location = "l",
                    staff = "s",
                    practice = "p"
                )
            )
        )
        every { engagementRepository.save((any())) } answers { firstArg() }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = now,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
                Appointment(
                    id = "c",
                    appointmentTypeId = "type",
                    startTime = now,
                    patient = Patient(id = "p2", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.eventDate == now
                        && engagement.nextCheckpoint.isAfter(now)
                        && !engagement.contactDeclined
                        && engagement.confirmationStatus == ConfirmationStatus.UNCONFIRMED
                        && engagement.confirmationAttempts == 0
                        && engagement.checkInAttempts == 0
                        && engagement.cancellationAttempts == 0
                        && engagement.reminderAttempts == 0
                        && engagement.resources.size == 8
                        && engagement.status == EngagementStatus.INITIAL
                        && engagement.resources.any { it is AppointmentResource && it.id == "a" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is AppointmentResource && it.id == "c" && it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                        && engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is PatientResource && it.id == "p2" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `update should send cancellation event if all appointments are cancelled`() {
        val now = Instant.now()

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = now.minusSeconds(30),
            nextCheckpoint = now.minusSeconds(30)
        )
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { stateMachine.sendEvent(any<CancellationEvent>()) } answers { firstArg<CancellationEvent>().engagement }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = now,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test"),
                    cancelled = true
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(any())
            stateMachine.sendEvent(any<CancellationEvent>())
            engagementRepository.save(any())
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `update should send cancellation event if all appointments are cancelled even with null staff`() {
        val now = Instant.now()

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = now.minusSeconds(30),
            nextCheckpoint = now.minusSeconds(30),
            resources = mutableSetOf(
                AppointmentResource(id= "1", startTime = now.minusSeconds(30),
                    reason = "reason", location = "location2", staff = "test", patient = "test", appointmentType = "type", practice = "test"),
                StaffResource(id = "1", name="testStaff")
            )
        )
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { stateMachine.sendEvent(any<CancellationEvent>()) } answers { firstArg<CancellationEvent>().engagement }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = now.minusSeconds(30),
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test"),
                    cancelled = true
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(any())
            stateMachine.sendEvent(any<CancellationEvent>())
            engagementRepository.save(any())
        }

        assertThat(actual.success).isTrue
        assertThat(actual.result?.getStaff()?.name).isEqualTo("testStaff")
    }

    @Test
    fun `update should send confirmation event if all appointments are confirmed`() {
        val now = Instant.now()

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = now.minusSeconds(30),
            nextCheckpoint = now.minusSeconds(30)
        )
        every { engagementRepository.save((any())) } answers { firstArg() }
        every { stateMachine.sendEvent(any<ConfirmationResponseEvent>()) } answers { firstArg<ConfirmationResponseEvent>().engagement }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = now,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test"),
                    confirmationStatus = ConfirmationStatus.CONFIRMED
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(any())
            stateMachine.sendEvent(any<ConfirmationResponseEvent>())
            engagementRepository.save(any())
        }

        assertThat(actual.success).isTrue
    }

    @Test
    fun `update should return failure if engagement is not found`() {
        every { engagementRepository.findByIdOrNull("1") } returns null

        val actual = sut.update(
            UpdateEngagementRequest(
                id = "1",
                customerId = "1",
                appointments = listOf(
                    getValidAppointment(),
                ),
                contacts = emptyList()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun `update should return failure if customer is not found`() {
        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "999",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now()
        )
        val actual = sut.update(
            UpdateEngagementRequest(
                id = "1",
                customerId = "999",
                appointments = listOf(
                    getValidAppointment(),
                ),
                contacts = emptyList()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun `update should return failure if customer is disabled`() {
        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "2",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now()
        )

        val actual = sut.update(
            UpdateEngagementRequest(
                id = "1",
                customerId = "2",
                appointments = listOf(
                    getValidAppointment(),
                ),
                contacts = emptyList()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)
    }

    @Test
    fun `patch should update engagement properties`() {
        every { engagementRepository.save(any()) } answers { firstArg() }

        val objects = listOf<JsonNode>(
            ObjectNode(
                nodeFactory,
                mapOf(
                    "op" to TextNode("replace"),
                    "path" to TextNode("/status"),
                    "value" to TextNode("CONFIRM")
                )
            )
        )
        val patch = JsonPatch.fromJson(ArrayNode(nodeFactory, objects))

        sut.patch(engagement, PatchEngagement.from(engagement), patch)

        verify {
            engagementRepository.save(match {
                it.id == engagement.id
                        && it.status == EngagementStatus.CONFIRM
                        && it.confirmationStatus == ConfirmationStatus.UNCONFIRMED // this field was not patched
            })
        }
    }

    @Test
    fun updateContact() {
        every { engagementRepository.updateResources(any(), any(), any()) } returns 1

        val request = Contact(
            customerId = "1",
            id = "1",
            email = "<EMAIL>",
            phone = "************"
        )

        // valid
        var actual = sut.updateContact(request)
        assertThat(actual.success).isTrue

        // missing customer id
        actual = sut.updateContact(request.copy(customerId = ""))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // missing id
        actual = sut.updateContact(request.copy(id = ""))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // allow invalid email
        actual = sut.updateContact(request.copy(email = "bad"))
        assertThat(actual.success).isTrue

        // allow invalid phone
        actual = sut.updateContact(request.copy(phone = "bad"))
        assertThat(actual.success).isTrue

        verify { engagementRepository.updateResources(eq("1"), any(), any()) }
    }

    @Test
    fun updateLocation() {
        every { engagementRepository.updateResources(any(), any(), any()) } returns 1

        val request = Location(
            customerId = "1",
            id = "2",
            practiceId = "3",
            zipCode = "44444",
            name = "test",
            email = "<EMAIL>",
            phone = "************"
        )

        // valid
        var actual = sut.updateLocation(request)
        assertThat(actual.success).isTrue

        // valid
        actual = sut.updateLocation(request.copy(email = null))
        assertThat(actual.success).isTrue

        // missing customer id
        actual = sut.updateLocation(request.copy(customerId = ""))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // missing id
        actual = sut.updateLocation(request.copy(id = ""))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // invalid email
        actual = sut.updateLocation(request.copy(email = "bad"))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // blank phone
        actual = sut.updateLocation(request.copy(phone = null))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        // invalid phone
        actual = sut.updateLocation(request.copy(phone = "bad"))
        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)

        verify { engagementRepository.updateResources(eq("1"), any(), any()) }
    }

    @Test
    fun `sendEvent should interact with state machine and save result`() {
        every { stateMachine.sendEvent(any<CheckpointEvent>(), null) } answers { firstArg<CheckpointEvent>().engagement }
        every { engagementRepository.save(any()) } answers { firstArg() }
        sut.sendEvent(CheckpointEvent(
            engagement = engagement.copy(),
            customer = Customer(id = "1", status = CustomerStatus.ENABLED, name = "Test")
        ))

        verify {
            stateMachine.sendEvent(any(), any())
            engagementRepository.save(any())
        }
    }

    @Test
    fun `findResponses should find using criteria`() {
        every { responseRepository.findByCriteria(any(), any(), any()) } returns Page.empty()

        val criteria = FindResponsesCriteria(
            messageId = "123",
            since = Instant.now()
        )

        sut.findResponses("1", criteria, Pageable.unpaged())

        verify {
            responseRepository.findByCriteria("1", criteria, Pageable.unpaged())
        }
    }

    @Test
    fun `update should reset engagement if the engagement is in an error state`() {
        val now = Instant.now()
        val originalTime = now.minusSeconds(30)

        every { engagementRepository.findByIdOrNull("1") } returns Engagement(
            id = "1",
            customerId = "1",
            eventDate = originalTime,
            nextCheckpoint = originalTime,
            status = EngagementStatus.ERROR,
            contactDeclined = false,
            confirmationStatus = ConfirmationStatus.CONFIRMED,
            confirmationAttempts = 1,
            checkInAttempts = 1,
            reminderAttempts = 1,
            cancellationAttempts = 1
        )
        every { engagementRepository.save((any())) } answers { firstArg() }

        val request = UpdateEngagementRequest(
            id = "1",
            customerId = "1",
            appointments = listOf(
                Appointment(
                    id = "a",
                    appointmentTypeId = "type",
                    startTime = originalTime,
                    patient = Patient(id = "p", givenName = "a", familyName = "test"),
                    staff = Staff(id = "s", name = "staff"),
                    location = Location(id = "l", practiceId = "p", name = "location", address = "address", phone = "************", zipCode = "test"),
                    reason = "reason",
                    practice = Practice(id = "p", name = "test")
                ),
            ),
            contacts = listOf(
                Contact(
                    id = "c",
                    givenName = "c",
                    familyName = "test",
                    contactMethod = ContactMethod.SMS
                )
            )
        )

        val actual = sut.update(request)

        verify {
            engagementRepository.save(match { engagement ->
                engagement.eventDate == originalTime
                        && engagement.nextCheckpoint.equals(originalTime)
                        && !engagement.contactDeclined
                        && engagement.confirmationStatus == ConfirmationStatus.UNCONFIRMED
                        && engagement.confirmationAttempts == 0
                        && engagement.checkInAttempts == 0
                        && engagement.cancellationAttempts == 0
                        && engagement.reminderAttempts == 0
                        && engagement.resources.size == 6
                        && engagement.status == EngagementStatus.INITIAL
                        && engagement.resources.any { it is AppointmentResource && it.id == "a" }
                        && engagement.resources.any { it is PatientResource && it.id == "p" }
                        && engagement.resources.any { it is ContactResource && it.id == "c" }
                        && engagement.resources.any { it is StaffResource && it.id == "s" }
                        && engagement.resources.any { it is LocationResource && it.id == "l" && it.practiceId == "p" }
                        && engagement.resources.any { it is PracticeResource && it.id == "p" }
            })
        }

        assertThat(actual.success).isTrue
    }


    companion object {
        private val nodeFactory = JacksonUtils.nodeFactory()

        private fun getValidAppointment(
            id: String = "1",
            startTime: Instant? = Instant.now(),
            cancelled: Boolean = false,
            localStartTime: LocalDateTime? = null
        ) = Appointment(
            id = id,
            appointmentTypeId = "type",
            startTime = startTime,
            localStartTime = localStartTime,
            patient = Patient(id = "2", givenName = "test", familyName = "test"),
            location = Location(id = "1", practiceId = "1", name = "test", zipCode = "test", phone = "************"),
            staff = Staff(id = "1", name = "test"),
            reason = "test",
            practice = Practice(id = "p", name = "test"),
            cancelled = cancelled
        )
    }
}