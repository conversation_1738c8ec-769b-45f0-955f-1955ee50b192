package com.connexin.pmx.server.data

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.ETAttachment
import com.connexin.pmx.server.models.bli.ETMessage
import com.connexin.pmx.server.models.bli.ETMessages
import com.connexin.pmx.server.utils.InstantUtil
import java.time.Instant
import java.time.temporal.ChronoUnit

class ETTestData() {
    companion object {
        val etTestMessage = """
            UserName=96267&UserPassword=8993&XMLPost=
            <Orders>
            <Order Type="ET">
            <EmailTo><EMAIL></EmailTo>
            <EmailFrom><EMAIL></EmailFrom>
            <DisplayName><PERSON></DisplayName>
            <EmailReplyTo><EMAIL></EmailReplyTo>
            <EmailSubject>Appointment Reminder</EmailSubject>
            <HtmlFile/><HtmlID/><HTMLBinary/>
            <TextFile>message.txt</TextFile><TextID/>
            <TextBinary>VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</TextBinary>
            <RtfFile/><RtfID/><EnrichedFile/><EnrichedID/><XmlFile/><XmlID/>
            <ReplaceLink>No</ReplaceLink>
            <IsForward>No</IsForward>
            <IsUnsubscribe>No</IsUnsubscribe>
            <Attachments>
            <Attachment>
            <AttachmentID/>
            <AttachmentName>ETAttachment1.pdf</AttachmentName>
            <AttachmentBinary>VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</AttachmentBinary>
            </Attachment>
            <Attachment>
            <AttachmentID/>
            <AttachmentName>ETAttachment2.txt</AttachmentName>
            <AttachmentBinary>VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</AttachmentBinary>
            </Attachment>
            </Attachments>
            </Order></Orders>&PostWay=sync"""
    }
}

fun testETMessages(): ETMessages {

    val sampleMessage = testETMessage1()
    val etMessages = ETMessages(sampleMessage)
    return etMessages

}

fun testETMessage1(): ETMessage {

    val sampleETAttachments: ArrayList<ETAttachment> = ArrayList<ETAttachment>()
    sampleETAttachments.add(testETAttachment1())
    sampleETAttachments.add(testETAttachment2())
    
    val testOrder = ETMessage (
        type = "ET",
        emailTo = "<EMAIL>",
        emailFrom = "<EMAIL>",
        displayName = "Joe Smith",
        emailReplyTo = "<EMAIL>",
        emailSubject = "Appointment Reminder",
        htmlFile = "",
        htmlID = "",
        htmlBinary = "",
        textFile = "message.txt",
        textID = "",
        textBinary = "VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh",
        rtfFile = "",
        rtfID = "",
        enrichedFile = "",
        enrichedID = "",
        xmlFile = "",
        xmlID = "",
        replaceLink = "No",
        isForward = "No",
        isUnsubscribe = "No",
        etAttachments = sampleETAttachments)

    return testOrder
}

fun testETAttachment1(): ETAttachment {

    val testAttachment = ETAttachment (
        attachmentID = "22",
        attachmentName = "TEST1.PNG",
        attachmentBinary = "VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0b")

    return testAttachment
}

fun testETAttachment2(): ETAttachment {

    val testAttachment = ETAttachment (
        attachmentID = "33",
        attachmentName = "TEST2.TXT",
        attachmentBinary = "yZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwA1V3R62811a")

    return testAttachment
}

// test pmx message for testing ET Reports
fun testPMXMessageForEmailReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "200",
        customerId = "10",
        type = MessageType.EMAIL,
        status = MessageStatus.DELIVERED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 2,
        createdAt = InstantUtil.toInstant("09/17/2020 03:09:40 PM"),
        updatedAt = InstantUtil.toInstant("09/17/2020 03:10:10 PM"),
        completedAt = InstantUtil.toInstant("09/17/2020 03:12:15 PM"),
        to = "<EMAIL>",
        from = "<EMAIL>",
        subject = "Appointment Reminder",
        message = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        altMessage = null,   // used for voice messages only
        remoteId = "5228871-864d-4d32-8f1d-7a07d2ef449a",
        errors = null,
        replyTo = "<EMAIL>",
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,    // used for voice messages only
        emailRecipients = null   // used for email broadcast only
    )

    return testMessage
}

// test pmx message for testing bounced ET Reports
fun testBouncedPMXMessageForEmailReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "201",
        customerId = "10",
        type = MessageType.EMAIL,
        status = MessageStatus.FAILED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 2,
        createdAt = InstantUtil.toInstant("09/17/2020 03:09:40 PM"),
        updatedAt = InstantUtil.toInstant("09/17/2020 03:10:10 PM"),
        completedAt = InstantUtil.toInstant("09/17/2020 03:12:15 PM"),
        to = "<EMAIL>",
        from = "<EMAIL>",
        subject = "Appointment Reminder",
        message = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        altMessage = null,   // used for voice messages only
        remoteId = "5228871-864d-4d32-8f1d-7a07d2ef449a",
        emailDeliveryFailureReason = EmailDeliveryFailureReason.HARD_BOUNCE,
        errors = "{\n" +
                "    \"feedbackId\": \"01000180e1de5a74-67263a04-533a-4976-9749-7f049ac837ce-000000\",\n" +
                "    \"bounceType\": \"Permanent\",\n" +
                "    \"bounceSubType\": \"OnAccountSuppressionList\",\n" +
                "    \"bouncedRecipients\": [\n" +
                "      {\n" +
                "        \"emailAddress\": \"<EMAIL>\",\n" +
                "        \"action\": \"failed\",\n" +
                "        \"status\": \"5.1.1\",\n" +
                "        \"diagnosticCode\": \"Amazon SES did not send the message to this address because it is on the suppression list for your account. For more information about removing addresses from the suppression list, see the Amazon SES Developer Guide at https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-email-suppression-list.html\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"timestamp\": \"2022-05-20T14:28:07.493Z\",\n" +
                "    \"reportingMTA\": \"dns; amazonses.com\"\n" +
                "  }",
        replyTo = "<EMAIL>",
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,    // used for voice messages only
        emailRecipients = null   // used for email broadcast only
    )

    return testMessage
}

// test pmx message for testing delayed ET Reports
fun testDelayedPMXMessageForEmailReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "202",
        customerId = "10",
        type = MessageType.EMAIL,
        status = MessageStatus.FAILED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 2,
        createdAt = InstantUtil.toInstant("09/17/2020 03:09:40 PM"),
        updatedAt = InstantUtil.toInstant("09/17/2020 03:10:10 PM"),
        completedAt = InstantUtil.toInstant("09/17/2020 03:12:15 PM"),
        to = "<EMAIL>",
        from = "<EMAIL>",
        subject = "Appointment Reminder",
        message = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        altMessage = null,   // used for voice messages only
        remoteId = "5228871-864d-4d32-8f1d-7a07d2ef449a",
        emailDeliveryFailureReason = EmailDeliveryFailureReason.COMMUNICATION_FAILURE,
        errors = "{\n" +
                "    \"timestamp\": \"2022-05-20T14:28:58.410Z\",\n" +
                "    \"delayType\": \"TransientCommunicationFailure\",\n" +
                "    \"expirationTime\": \"2022-05-20T16:14:57.405Z\",\n" +
                "    \"delayedRecipients\": [\n" +
                "      {\n" +
                "        \"emailAddress\": \"<EMAIL>\",\n" +
                "        \"status\": \"4.4.0\",\n" +
                "        \"diagnosticCode\": \"smtp; 421 4.4.0 Unable to lookup DNS for localhost\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }",
        replyTo = "<EMAIL>",
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,    // used for voice messages only
        emailRecipients = null   // used for email broadcast only
    )

    return testMessage
}

// test pmx message for testing rejected ET Reports
fun testRejectedPMXMessageForEmailReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "203",
        customerId = "10",
        type = MessageType.EMAIL,
        status = MessageStatus.FAILED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 2,
        createdAt = InstantUtil.toInstant("09/17/2020 03:09:40 PM"),
        updatedAt = InstantUtil.toInstant("09/17/2020 03:10:10 PM"),
        completedAt = InstantUtil.toInstant("09/17/2020 03:12:15 PM"),
        to = "<EMAIL>",
        from = "<EMAIL>",
        subject = "Appointment Reminder",
        message = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        altMessage = null,   // used for voice messages only
        remoteId = "5228871-864d-4d32-8f1d-7a07d2ef449a",
        emailDeliveryFailureReason = EmailDeliveryFailureReason.CONTENT_REJECTED,
        errors = "{\n" +
                "    \"reason\": \"Email rejected\"\n" +
                "}",
        replyTo = "<EMAIL>",
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,    // used for voice messages only
        emailRecipients = null   // used for email broadcast only
    )

    return testMessage
}

/*
======================================================================================
XML from OP:
    '<Orders><Order Type="' + BLI_MSG_TYPE_EMAIL_SINGLE + '">' +
    '<EmailTo>' + CleanEmailAddress(FieldByName('CONTACT_VALUE').AsString) + '</EmailTo>' +
    '<EmailFrom>' + CleanEmailAddress(FieldByName('REPLY_TO').AsString) + '</EmailFrom>' +
    '<DisplayName>' + FieldByName('REPLY_TO').AsString + '</DisplayName>' +
    '<EmailReplyTo>' + CleanEmailAddress(FieldByName('REPLY_TO').AsString) + '</EmailReplyTo>' +
    '<EmailSubject>' + CleanEmailSubject(FieldByName('EMAIL_SUBJECT').AsString) + '</EmailSubject>' +
    '<HtmlFile/><HtmlID/><HTMLBinary/>' +
    '<TextFile>message.txt</TextFile><TextID/>' +
    '<TextBinary>' + String(DIMime.MimeEncodeStringNoCRLF(AnsiString(FieldByName('MESSAGE_DATA').AsString))) + '</TextBinary>' +
    '<RtfFile/><RtfID/><EnrichedFile/><EnrichedID/><XmlFile/><XmlID/>' +
    '<ReplaceLink>No</ReplaceLink>' +
    '<IsForward>No</IsForward>' +
    '<IsUnsubscribe>No</IsUnsubscribe>' +
    '<Attachments>' +
    '<Attachment><AttachmentID/><AttachmentName/><AttachmentBinary/></Attachment>' +
    '</Attachments>' +
    '</Order></Orders>';
======================================================================================
Sample xml:
<Orders>
	<Order Type="ET">
		<EmailTo><EMAIL></EmailTo>
		<EmailFrom>PatientRequests@ - - - - - -.com</EmailFrom>
		<DisplayName>PatientRequests@ - - - - - -.com</DisplayName>
		<EmailReplyTo>PatientRequests@ - - - - - -.com</EmailReplyTo>
		<EmailSubject>Appointment reminder from  - - - - -  Pediatrics</EmailSubject>
		<HtmlFile/>
		<HtmlID/>
		<HTMLBinary/>
		<TextFile>message.txt</TextFile>
		<TextID/>
		<TextBinary>************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</TextBinary>
		<RtfFile/>
		<RtfID/>
		<EnrichedFile/>
		<EnrichedID/>
		<XmlFile/>
		<XmlID/>
		<ReplaceLink>No</ReplaceLink>
		<IsForward>No</IsForward>
		<IsUnsubscribe>No</IsUnsubscribe>
		<Attachments>
			<Attachment>
				<AttachmentID/>
				<AttachmentName/>
				<AttachmentBinary/>
			</Attachment>
		</Attachments>
	</Order>
</Orders>
 */

/*
XML Sample of ET Report:

<Report>
	<ET>
		<formid>3723346</formid>
		<unqid>9f8ded33-2c52-4844-baab-0e9d2cf1ffa7</unqid>
		<orderid>3723346</orderid>
		<Project>Transactional Email,8,2013</Project>
		<EmailAddress><EMAIL></EmailAddress>
		<OpenCount></OpenCount>
		<LastOpened></LastOpened>
		<jobstatus>Sent</jobstatus>
		<result></result>
		<error></error>
		<timestamp>08/12/2013 02:10:42 PM</timestamp>
	</ET>
</Report>
*/