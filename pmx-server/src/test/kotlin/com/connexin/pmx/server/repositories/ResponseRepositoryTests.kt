package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.FindResponsesCriteria
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.findAllAndRemove
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class ResponseRepositoryTests {
    @Autowired
    lateinit var template: MongoTemplate

    @Autowired
    lateinit var sut: ResponseRepository

    @AfterEach
    fun cleanup() {
        template.findAllAndRemove<Response>(
            Query(Criteria.where("customerId").`in`("test", "test2"))
        )
    }

    @Test
    fun `save should create variety of response types`() {
        val expected = listOf(
            ErrorResponse(
                customerId = "test",
                engagementId = "test",
                respondents = setOf(
                    ContactResource(
                        id = "1",
                        familyName = "Test",
                        givenName = "User",
                        contactMethod = ContactMethod.EMAIL,
                        email = "<EMAIL>",
                        language = Language.ENGLISH,
                        phone = null
                    ),
                    ContactResource(
                        id = "2",
                        familyName = "Test",
                        givenName = "User 2",
                        contactMethod = ContactMethod.SMS,
                        email = null,
                        language = Language.ENGLISH,
                        phone = "+15551234"
                    ),
                ),
                appointments = emptySet(),
                errors = listOf(
                    ErrorDto(
                        path = "",
                        message = "test",
                        errorCode = Errors.NOT_FOUND.code
                    )
                )
            ),
            CancellationResponse(
                customerId = "test",
                engagementId = "test",
                respondents = setOf(
                    ContactResource(
                        id = "1",
                        familyName = "Test",
                        givenName = "User",
                        contactMethod = ContactMethod.EMAIL,
                        email = "<EMAIL>",
                        language = Language.ENGLISH,
                        phone = null
                    ),
                    ContactResource(
                        id = "2",
                        familyName = "Test",
                        givenName = "User 2",
                        contactMethod = ContactMethod.SMS,
                        email = null,
                        language = Language.ENGLISH,
                        phone = "+15551234"
                    ),
                ),
                appointments = setOf(
                    AppointmentResource(
                        id = "1",
                        startTime = Instant.now(),
                        reason = "test",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    ),
                    AppointmentResource(
                        id = "2",
                        startTime = Instant.now(),
                        reason = "test 2",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    )
                ),
                result = CancellationStatus.CANCELLED
            ),
            CheckInResponse(
                customerId = "test",
                engagementId = "test",
                respondents = setOf(
                    ContactResource(
                        id = "1",
                        familyName = "Test",
                        givenName = "User",
                        contactMethod = ContactMethod.EMAIL,
                        email = "<EMAIL>",
                        language = Language.ENGLISH,
                        phone = null
                    ),
                    ContactResource(
                        id = "2",
                        familyName = "Test",
                        givenName = "User 2",
                        contactMethod = ContactMethod.SMS,
                        email = null,
                        language = Language.ENGLISH,
                        phone = "+15551234"
                    ),
                ),
                appointments = setOf(
                    AppointmentResource(
                        id = "1",
                        startTime = Instant.now(),
                        reason = "test",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    ),
                    AppointmentResource(
                        id = "2",
                        startTime = Instant.now(),
                        reason = "test 2",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    )
                ),
                isFinal = false,
                result = CheckInStatus.NO_RESPONSE
            ),
            ConfirmationResponse(
                customerId = "test",
                engagementId = "test",
                respondents = setOf(
                    ContactResource(
                        id = "1",
                        familyName = "Test",
                        givenName = "User",
                        contactMethod = ContactMethod.EMAIL,
                        email = "<EMAIL>",
                        language = Language.ENGLISH,
                        phone = null
                    ),
                    ContactResource(
                        id = "2",
                        familyName = "Test",
                        givenName = "User 2",
                        contactMethod = ContactMethod.SMS,
                        email = null,
                        language = Language.ENGLISH,
                        phone = "+15551234"
                    ),
                ),
                appointments = setOf(
                    AppointmentResource(
                        id = "1",
                        startTime = Instant.now(),
                        reason = "test",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    ),
                    AppointmentResource(
                        id = "2",
                        startTime = Instant.now(),
                        reason = "test 2",
                        location = "test",
                        staff = "test",
                        patient = "Test",
                        appointmentType = "test",
                        practice = "test"
                    )
                ),
                isFinal = true,
                result = ConfirmationStatus.DECLINED
            )
        )

        val actual = sut.saveAll(expected)

        assertThat(actual[0])
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(expected[0])
        assertThat(actual[1])
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(expected[1])
        assertThat(actual[2])
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(expected[2])
        assertThat(actual[3])
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(expected[3])
        assertThat(actual).allMatch { !it.id.isNullOrEmpty() }
    }

    @Test
    fun `findForCustomerSince should return responses for customer with occurredAt greater than or equal to`() {
        val expected = sut.saveAll(
            listOf(
                ErrorResponse(
                    customerId = "test",
                    engagementId = "test",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 5, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test",
                    engagementId = "test",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 6, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test2",
                    engagementId = "test2",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 5, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test2",
                    engagementId = "test2",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 6, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                )
            )
        )
        val since = OffsetDateTime.of(2023, 3, 5, 14, 0, 0, 0, ZoneOffset.UTC).toInstant()

        val actual = sut.findByCriteria(
            "test", FindResponsesCriteria(
                since = since,
                archived = false
            ), Pageable.ofSize(10)
        )

        assertThat(actual).containsExactly(
            expected.find { it.customerId == "test" && it.occurredAt.isAfter(since) }
        )
        assertThat(actual.totalElements).isEqualTo(1)
    }

    @Test
    fun `findByCustomerIdAndEngagementId should return responses for an engagement`() {
        val expected = sut.saveAll(
            listOf(
                ErrorResponse(
                    customerId = "test",
                    engagementId = "test1",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 5, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test",
                    engagementId = "test1",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 6, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test2",
                    engagementId = "test1",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 5, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                ),
                ErrorResponse(
                    customerId = "test",
                    engagementId = "test2",
                    respondents = emptySet(),
                    appointments = emptySet(),
                    errors = emptyList(),
                    occurredAt = OffsetDateTime.of(2023, 3, 6, 12, 0, 0, 0, ZoneOffset.UTC).toInstant()
                )
            )
        )

        val actual = sut.findByCriteria(
            "test", FindResponsesCriteria(
                engagementId = "test1",
                archived = false
            ), Pageable.ofSize(10)
        )

        assertThat(actual.totalElements).isEqualTo(2)
        assertThat(actual.content[0]).isEqualTo(expected[0])
        assertThat(actual.content[1]).isEqualTo(expected[1])
    }
}