package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.MessageApplication
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.dtos.DeliveryStatisticResult
import com.connexin.pmx.server.models.dtos.GetDeliveryStatisticsCriteria
import com.connexin.pmx.server.services.StatisticsService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(AdminStatsController::class)
class AdminStatsControllerTests {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var statsService: StatisticsService

    @Test
    @WithMockUser(authorities = ["admin.stats:read"])
    fun `getDeliveryStatistics should return values`() {
        val from = Instant.now().minusSeconds(5)
        val until = from.plusSeconds(10)
        val expected = listOf(
            DeliveryStatisticResult(from, until, MessageApplication.ENGAGEMENT, MessageType.VOICE, 0, 0, 0, 0, 0.0, 0.0, 0.0, emptyMap())
        )

        every { statsService.getDeliveryStatistics(GetDeliveryStatisticsCriteria(from = from, until = until)) } returns expected

        mockMvc.perform(
            get("/api/v2/admin/stats/delivery")
                .param("from", from.toString())
                .param("until", until.toString())
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isOk)
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }

    @Test
    fun `getDeliveryStatistics should return 401`() {
        val from = Instant.now().minusSeconds(5)
        val until = from.plusSeconds(10)

        mockMvc.perform(
            get("/api/v2/admin/stats/delivery")
                .param("from", from.toString())
                .param("until", until.toString())
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().isUnauthorized)
    }
}