package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.MessageApplication
import com.connexin.pmx.server.models.MessageStatistic
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.dtos.GetDeliveryStatisticsCriteria
import com.connexin.pmx.server.repositories.PmxMessageRepository
import com.connexin.pmx.server.utils.TestVariation
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.SpyK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kong.unirest.HttpRequestWithBody
import kong.unirest.HttpResponse
import kong.unirest.RequestBodyEntity
import kong.unirest.UnirestInstance
import net.javacrumbs.shedlock.core.LockAssert
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant

@ExtendWith(MockKExtension::class)
class StatisticServiceImplTests {
    @MockK
    lateinit var messageRepository: PmxMessageRepository

    @SpyK
    private var mapper = ObjectMapper()

    @MockK
    lateinit var client: UnirestInstance

    @InjectMockKs
    lateinit var sut: StatisticsServiceImpl

    @BeforeEach
    fun setup() {
        LockAssert.TestHelper.makeAllAssertsPass(true)

        every { client.post("https://hooks.slack.com/services/good/webhook") } answers {
            val request = mockk<HttpRequestWithBody>()
            val entity = mockk<RequestBodyEntity>()
            val response = mockk<HttpResponse<Any>>()
            every { request.contentType(any()) } returns request
            every { request.body(any<String>()) } returns entity
            every { entity.asEmpty() } returns response
            every { response.isSuccess } returns true
            request
        }


    }

    private fun setupWebhook(success: Boolean) {
        every { client.post(any()) } answers {
            val request = mockk<HttpRequestWithBody>()
            val entity = mockk<RequestBodyEntity>()
            val response = mockk<HttpResponse<Any>>()
            every { request.contentType(any()) } returns request
            every { request.body(any<String>()) } returns entity
            every { entity.asEmpty() } returns response
            every { response.isSuccess } returns success

            if (!success) {
                every { response.status } returns 401
                every { response.statusText } returns "Unauthorized"
                every { response.body } returns "body"
            }

            request
        }
    }

    @Test
    fun `getDeliveryStatistics should return list of statistics by application and type`() {
        val variations = TestVariation.generate()
        val stats = variations.map {
            MessageStatistic(
                engagement = it.application == MessageApplication.ENGAGEMENT,
                type = it.type,
                status = it.status,
                count = it.count.toLong()
            )
        }

        every { messageRepository.getMessageStatistics(any(), any(), any(), any()) } returns stats

        val actual = sut.getDeliveryStatistics(
            GetDeliveryStatisticsCriteria(
                from = Instant.now(),
                until = Instant.now()
            )
        )

        assertThat(actual).isNotEmpty

        verify {
            messageRepository.getMessageStatistics(any(), any(), MessageStatus.values(), MessageType.values())
        }
    }

    @Test
    fun `generateDeliveryStatisticsReport should post formatted results to webhook`() {
        setupWebhook(true)

        val variations = TestVariation.generate()
        val stats = variations.map {
            MessageStatistic(
                engagement = it.application == MessageApplication.ENGAGEMENT,
                type = it.type,
                status = it.status,
                count = it.count.toLong()
            )
        }

        every { messageRepository.getMessageStatistics(any(), any(), any(), any()) } returns stats

        sut.generateDeliveryStatisticsReport()

        verify {
            client.post(any())
        }
    }

    @Test
    fun `generateDeliveryStatisticsReport should not post webhook if no results`() {
        setupWebhook(true)

        every { messageRepository.getMessageStatistics(any(), any(), any(), any()) } returns emptyList()

        sut.generateDeliveryStatisticsReport()

        verify(exactly = 0) {
            client.post(any())
        }
    }

    @Test
    fun `generateDeliveryStatisticsReport log webhook error`() {
        setupWebhook(false)
        val variations = TestVariation.generate()
        val stats = variations.map {
            MessageStatistic(
                engagement = it.application == MessageApplication.ENGAGEMENT,
                type = it.type,
                status = it.status,
                count = it.count.toLong()
            )
        }

        every { messageRepository.getMessageStatistics(any(), any(), any(), any()) } returns stats

        sut.generateDeliveryStatisticsReport()
    }
}