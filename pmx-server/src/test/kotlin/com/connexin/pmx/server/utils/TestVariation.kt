package com.connexin.pmx.server.utils

import com.connexin.pmx.server.models.MessageApplication
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import kotlin.random.Random

data class TestVariation(
    val application: MessageApplication,
    val type: MessageType,
    val status: MessageStatus,
    val count: Int
) {
    companion object {
        fun generate(): List<TestVariation> {
            val variations = mutableListOf<TestVariation>()
            for (e in arrayOf(MessageApplication.LEGACY, MessageApplication.ENGAGEMENT)) {
                for (t in MessageType.values()) {
                    for (s in MessageStatus.values()) {
                        variations.add(TestVariation(application = e, type = t, status = s, count = Random.nextInt(0, 10)))
                    }
                }
            }
            return variations
        }
    }
}