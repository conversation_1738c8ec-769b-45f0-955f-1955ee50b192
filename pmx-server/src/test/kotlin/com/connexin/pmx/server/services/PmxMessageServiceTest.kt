package com.connexin.pmx.server.services

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.repositories.PmxMessageRepository
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.*
import java.util.*


@ExtendWith(MockKExtension::class)
class PmxMessageServiceTest {
    @MockK(relaxed = true)
    lateinit var messageRepository: PmxMessageRepository

    @MockK(relaxed = true)
    lateinit var customerService: CustomerService

    @MockK(relaxed = true)
    lateinit var meterService: MeterService

    @InjectMockKs
    lateinit var sut: PmxMessageService

    @BeforeEach
    fun setup() {
        every { customerService.getById(any()) } returns null
        every { customerService.getById("5") } returns Customer(id = "5", organizationId = 5L, name = "Test", status = CustomerStatus.ENABLED)

        every { messageRepository.save(any()) } answers {
            val account = firstArg<PmxMessage>()

            account
        }
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `create should fail if customer could not be found`() {
        val request = CreateMessageRequest(
            customerId = "99",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test"
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.success).isFalse
        assertThat(actual.errors)
            .contains(
                ErrorDto(
                    path = "opmedId", message = "Unknown customer 99", errorCode = Errors.NOT_FOUND.code
                )
            )
    }

    @Test
    fun `create should queue new SMS message`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.create(request)

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.SMS,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = "+***********",
                message = "test",
                altMessage = null,
                confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                sendAfter = Instant.EPOCH,
                language = Language.ENGLISH
            )
        )
    }

    @Test
    fun `create should replace SMS message text`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "TristanJul 21@ 4:10 PMChester Pediatrics P.C.Click to confirm:https://patients.op.health/confirm/BIBAAAAAAAAAB6KTBAAAAAAAAA",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.create(request)

        assertThat(actual.message?.message).isEqualTo("TristanJul 21@ 4:10 PMChester Pediatrics P.C.Click to ")

        val request2 = request.copy(message = "CAC Vanc:A'miko, Jul 15@11:00 AM Confirm: https://patients.op.health/confirm/4YAAAAAAAAAABQDHBMAAAAAAAA. Check in by phone")

        val actual2 = sut.create(request2)

        assertThat(actual2.message?.message).isEqualTo("CAC Vanc:A'miko, Jul 15@11:00 AM . Check in by phone")

        // patients.dev
        val request3 = request.copy(message = "Peds APPT:John Friday July 15 9:30 AM Avista https://patients.dev.op.health/confirm/FABAAAAAAAAABQYBBEAAAAAAAA")

        val actual3 = sut.create(request3)

        assertThat(actual3.message?.message).isEqualTo("Peds APPT:John Friday July 15 9:30 AM Avista")
    }

    @Test
    fun `create should queue new Voice message`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.VOICE,
            to = setOf("************"), // phone numbers will be canonicalized to E164 format
            replyTo = "************",
            message = "test",
            altMessage = "voicemail test",
            instructions = "test",
            sendAfter = Instant.EPOCH,
            sendFrom = OffsetTime.of(9, 0, 0, 0, ZoneOffset.of("-5")),
            sendUntil = OffsetTime.of(21, 0, 0, 0, ZoneOffset.of("-5"))
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.VOICE,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = "+***********",
                replyTo = "+18002189916",
                message = "test",
                altMessage = "voicemail test",
                confirmationStatus = ConfirmationStatus.UNCONFIRMED,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                instructions = "test",
                sendAfter = Instant.EPOCH,
                sendWindow = PmxMessage.SendWindow(
                    from = OffsetDateTime.of(2022, 1, 1, 9, 0, 0, 0, ZoneOffset.of("-5")).toInstant(),
                    until = OffsetDateTime.of(2022, 1, 1, 21, 0, 0, 0, ZoneOffset.of("-5")).toInstant(),
                ),
                language = Language.ENGLISH
            )
        )

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)
    }

    @Test
    fun `create should return errors for invalid Voice fields`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.VOICE,
            to = setOf(),
            message = "",
            replyTo = "asdasd"
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).contains(
            ErrorDto(path = "to", message = "Must not be empty", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "to", message = "Size must be between 1 and 1", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "message", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "replyTo", message = "Must be a valid phone number", errorCode = Errors.VALIDATION_FAILED.code),
        )
    }

    @Test
    fun `create should return errors for invalid sendFrom and sendUntil fields`() {
        // sendUntil must be specified if sendFrom is provided
        var actual = sut.create(
            CreateMessageRequest(
                customerId = "5",
                type = MessageType.VOICE,
                to = setOf("+***********"),
                message = "Test",
                sendFrom = OffsetTime.of(9, 0, 0, 0, ZoneOffset.of("-5"))
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.errors).contains(
            ErrorDto(path = "sendUntil", "Must not be null", errorCode = Errors.VALIDATION_FAILED.code)
        )

        // sendFrom must be specified if sendUntil is from.
        actual = sut.create(
            CreateMessageRequest(
                customerId = "5",
                type = MessageType.VOICE,
                to = setOf("+***********"),
                message = "Test",
                sendUntil = OffsetTime.of(9, 0, 0, 0, ZoneOffset.of("-5"))
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.errors).contains(
            ErrorDto(path = "sendFrom", "Must not be null", errorCode = Errors.VALIDATION_FAILED.code)
        )

    }

    @Test
    fun `create should return errors for invalid SMS fields`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.SMS,
            to = setOf(""),
            message = ""
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).contains(
            ErrorDto(path = "to", message = "Must have valid phone numbers", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "message", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code)
        )
    }

    @Test
    fun `create should queue new email message`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.EMAIL,
            to = setOf("<EMAIL>"),
            replyTo = "<EMAIL>",
            message = "test",
            subject = "email test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.EMAIL,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = "<EMAIL>",
                replyTo = "<EMAIL>",
                message = "test",
                subject = "email test",
                confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                sendAfter = Instant.EPOCH,
                language = Language.ENGLISH
            )
        )

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)
    }

    @Test
    fun `create should return errors for invalid email fields`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.EMAIL,
            to = setOf(""),
            message = "",
            replyTo = "invalid"
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).contains(
            ErrorDto(path = "to", message = "Must have valid email addresses", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "replyTo", message = "Must be a valid email address", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "message", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code)
        )
    }

    @Test
    fun `create should queue new email broadcast message`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.EMAIL_BROADCAST,
            to = setOf("<EMAIL>"),
            replyTo = "<EMAIL>",
            message = "test",
            subject = "email broadcast test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.EMAIL_BROADCAST,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = null,
                replyTo = "<EMAIL>",
                message = "test",
                subject = "email broadcast test",
                confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                emailRecipients = listOf(PmxMessage.EmailRecipient(address = "<EMAIL>", status = MessageStatus.QUEUED)),
                sendAfter = Instant.EPOCH,
                language = Language.ENGLISH
            )
        )

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)
    }

    @Test
    fun `email broadcast message should only have valid emails`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.EMAIL_BROADCAST,
            to = setOf("<EMAIL>", "usertest.com", "user@test", "<EMAIL>", "<EMAIL>", "<EMAIL>"),
            replyTo = "<EMAIL>",
            message = "test",
            subject = "email broadcast test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.message!!.emailRecipients!!.size).isEqualTo(4)
    }

    @Test
    fun `update should save`() {
        val message = PmxMessage(
            customerId = "5",
            type = MessageType.VOICE,
            to = "+***********",
            message = "test",
            status = MessageStatus.FAILED,
            errors = "test",
            sendAfter = Instant.EPOCH
        )

        sut.update(message)

        verify(exactly = 1) {
            messageRepository.save(message)
        }
    }

    @Test
    fun `getByRemoteId should return message`() {
        every { messageRepository.findByRemoteId("test") } returns Optional.of(
            PmxMessage(
                id = "test",
                customerId = "5",
                type = MessageType.VOICE,
                to = "+***********",
                message = "test",
                status = MessageStatus.FAILED,
                errors = "test",
                remoteId = "test",
                sendAfter = Instant.EPOCH
            )
        )

        val actual = sut.getByRemoteId("test")

        assertThat(actual).isNotNull
        assertThat(actual?.remoteId).isEqualTo("test")
    }

    @Test
    fun `getByRemoteId should return null`() {
        every { messageRepository.findByRemoteId("test") } returns Optional.empty()

        val actual = sut.getByRemoteId("test")

        assertThat(actual).isNull()
    }

    @Test
    fun `findReadyToDispatch should queued messages and dispatch them`() {
        every {
            messageRepository.findReadyToDispatch(
                any(),
                PageRequest.of(0, 100, Sort.Direction.ASC, "sendAfter")
            )
        } returns emptyList()

        sut.findReadyToDispatch(Instant.now(), PageRequest.of(0, 100))

        verifySequence {
            messageRepository.findReadyToDispatch(any(), PageRequest.of(0, 100, Sort.Direction.ASC, "sendAfter"))
        }
    }

    @Test
    fun `getSendWindow should return null`() {
        val actual = CreateMessageRequest(
            customerId = "1",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test"
        ).getSendWindow()

        assertThat(actual).isNull()
    }

    @Test
    fun `getSendWindow should return instant relative to epoch date`() {
        val actual = CreateMessageRequest(
            customerId = "1",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test",
            sendFrom = OffsetTime.of(LocalTime.of(9, 0), ZoneOffset.of("-5")),
            sendUntil = OffsetTime.of(LocalTime.of(21, 0), ZoneOffset.of("-5"))
        ).getSendWindow()

        assertThat(actual).isNotNull
        assertThat(actual!!.from).isEqualTo(OffsetDateTime.of(2022, 1, 1, 9, 0, 0, 0, ZoneOffset.of("-5")).toInstant())
        assertThat(actual.until).isEqualTo(OffsetDateTime.of(2022, 1, 1, 21, 0, 0, 0, ZoneOffset.of("-5")).toInstant())

    }

    @Test
    fun `getSendWindow should return Send Window with until equal to 3am next day`() {
        val actual = CreateMessageRequest(
            customerId = "1",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test",
            sendFrom = OffsetTime.of(LocalTime.of(9, 0), ZoneOffset.of("-5")),
            sendUntil = OffsetTime.of(LocalTime.of(3, 0), ZoneOffset.of("-5"))
        ).getSendWindow()

        assertThat(actual).isNotNull
        assertThat(actual!!.from).isEqualTo(OffsetDateTime.of(2022, 1, 1, 9, 0, 0, 0, ZoneOffset.of("-5")).toInstant())
        assertThat(actual.until).isEqualTo(OffsetDateTime.of(2022, 1, 2, 3, 0, 0, 0, ZoneOffset.of("-5")).toInstant())

    }

    @Test
    fun `create should queue new SMS message with sendUnil equal to 1am next day`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.SMS,
            to = setOf("+***********"),
            message = "test",
            sendAfter = Instant.EPOCH,
            sendFrom = OffsetTime.of(9, 0, 0, 0, ZoneOffset.of("-5")),
            sendUntil = OffsetTime.of(1, 0, 0, 0, ZoneOffset.of("-5"))
        )

        val actual = sut.create(request)

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.SMS,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = "+***********",
                message = "test",
                altMessage = null,
                confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                sendAfter = Instant.EPOCH,
                sendWindow = PmxMessage.SendWindow(
                    from = OffsetDateTime.of(2022, 1, 1, 9, 0, 0, 0, ZoneOffset.of("-5")).toInstant(),
                    until = OffsetDateTime.of(2022, 1, 2, 1, 0, 0, 0, ZoneOffset.of("-5")).toInstant()
                ),
                language = Language.ENGLISH
            )
        )
    }

    @Test
    fun `create should queue new Voice message with sendUnil equal to 2am next day`() {
        val request = CreateMessageRequest(
            customerId = "5",
            type = MessageType.VOICE,
            to = setOf("************"), // phone numbers will be canonicalized to E164 format
            replyTo = "************",
            message = "test",
            altMessage = "voicemail test",
            sendAfter = Instant.EPOCH,
            sendFrom = OffsetTime.of(9, 0, 0, 0, ZoneOffset.of("-5")),
            sendUntil = OffsetTime.of(2, 0, 0, 0, ZoneOffset.of("-5"))
        )

        val actual = sut.create(request)

        assertThat(actual).isNotNull
        assertThat(actual.success).isTrue
        assertThat(actual.message).isEqualTo(
            PmxMessage(
                type = MessageType.VOICE,
                status = MessageStatus.QUEUED,
                customerId = "5",
                to = "+***********",
                replyTo = "+18002189916",
                message = "test",
                altMessage = "voicemail test",
                confirmationStatus = ConfirmationStatus.UNCONFIRMED,
                voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
                sendAfter = Instant.EPOCH,
                sendWindow = PmxMessage.SendWindow(
                    from = OffsetDateTime.of(2022, 1, 1, 9, 0, 0, 0, ZoneOffset.of("-5")).toInstant(),
                    until = OffsetDateTime.of(2022, 1, 2, 2, 0, 0, 0, ZoneOffset.of("-5")).toInstant(),
                ),
                language = Language.ENGLISH
            )
        )

        verifySequence {
            customerService.getById("5")
            messageRepository.save(any())
        }

        confirmVerified(messageRepository, customerService)
    }

    @Test
    fun `getMostRecentlyDeliveredEngagementSmsMessage returns null when no messages found`() {
        every { messageRepository.getMostRecentlyDeliveredEngagementSmsMessage(any()) } returns null

        val actual = sut.getMostRecentlyDeliveredEngagementSmsMessage("test")

        assertThat(actual).isNull()

        verify {
            messageRepository.getMostRecentlyDeliveredEngagementSmsMessage("test")
        }
    }
}