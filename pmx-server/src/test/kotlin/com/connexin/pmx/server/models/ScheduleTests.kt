package com.connexin.pmx.server.models

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.DayOfWeek
import java.time.ZoneId
import java.time.ZonedDateTime

class ScheduleTests {
    private val zone = ZoneId.of("US/Eastern")
    private val days = setOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY)
    private val friday = ZonedDateTime.of(2023, 3, 24, 10, 30, 0, 0, zone)

    @Test
    fun `next should return correct values over time for default confirmation schedule`() {
        val now = ZonedDateTime.of(2023, 3, 17, 10, 30, 0, 0, zone)

        var actual = Schedule.CONFIRMATION_DEFAULT.next(now.minusDays(3), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 17, 10, 30, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_DEFAULT.next(now, friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 17, 10, 30, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_DEFAULT.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 21, 10, 30, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_DEFAULT.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 23, 10, 30, 1, 0, zone).toInstant())

        assertThat(Schedule.CONFIRMATION_DEFAULT.next(actual!!.atZone(zone), friday, days)).isNull()
    }

    @Test
    fun `next should return correct values over time for default reminder schedule`() {
        val now = ZonedDateTime.of(2023, 3, 17, 10, 30, 0, 0, zone)

        var actual = Schedule.REMINDER_DEFAULT.next(now.minusDays(3), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 21, 10, 30, 1, 0, zone).toInstant())

        actual = Schedule.REMINDER_DEFAULT.next(now, friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 21, 10, 30, 1, 0, zone).toInstant())

        actual = Schedule.REMINDER_DEFAULT.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 23, 10, 30, 1, 0, zone).toInstant())

        assertThat(Schedule.REMINDER_DEFAULT.next(actual!!.atZone(zone), friday, days)).isNull()
    }

    @Test
    fun `next should return correct values over time for test confirmation schedule`() {
        val now = ZonedDateTime.of(2023, 3, 24, 10, 0, 0, 0, zone)

        var actual = Schedule.CONFIRMATION_TEST.next(now.minusDays(3), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 25, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_TEST.next(now, friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 25, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_TEST.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 27, 1, 0, zone).toInstant())

        actual = Schedule.CONFIRMATION_TEST.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 29, 1, 0, zone).toInstant())

        assertThat(Schedule.CONFIRMATION_TEST.next(actual!!.atZone(zone), friday, days)).isNull()
    }

    @Test
    fun `next should return correct values over time for test reminder schedule`() {
        val now = ZonedDateTime.of(2023, 3, 24, 10, 0, 0, 0, zone)

        var actual = Schedule.REMINDER_TEST.next(now.minusDays(3), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 27, 1, 0, zone).toInstant())

        actual = Schedule.REMINDER_TEST.next(now, friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 27, 1, 0, zone).toInstant())


        actual = Schedule.REMINDER_TEST.next(actual!!.atZone(zone), friday, days)
        assertThat(actual).isEqualTo(ZonedDateTime.of(2023, 3, 24, 10, 29, 1, 0, zone).toInstant())

        assertThat(Schedule.REMINDER_TEST.next(actual!!.atZone(zone), friday, days)).isNull()
    }

    @Test
    fun `next should return null if no delivery days`() {
        val now = ZonedDateTime.of(2023, 3, 24, 10, 0, 0, 0, zone)

        assertThat(Schedule.NONE.next(now, friday, emptySet())).isNull()
    }
}