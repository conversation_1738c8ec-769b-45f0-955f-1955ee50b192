package com.connexin.pmx.server.web

import com.connexin.authentication.service.ManagedToken
import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.client.OAuth2UnirestAuthenticationInterceptor
import kong.unirest.Unirest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.beans.factory.annotation.Qualifier
import kong.unirest.UnirestInstance
import org.junit.jupiter.api.*
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class RestHttpClientTest {
    @Autowired
    @Qualifier("unirestInstant")
    lateinit var defaultUnirestInstance: UnirestInstance

    @Autowired
    @Qualifier("bridgeUnirestInstance")
    lateinit var bridgeUnirestInstance: UnirestInstance

    lateinit var defaultRestHttpClient: RestHttpClient
    lateinit var bridgeRestHttpClient: RestHttpClient

    @BeforeEach
    fun setUp() {
        val token: ManagedToken = mock(ManagedToken::class.java)
        val accessToken = "mockbridgeAccessToken"

        `when`(token.accessToken).thenReturn(accessToken)

        val interceptor = OAuth2UnirestAuthenticationInterceptor(token)

        bridgeUnirestInstance = Unirest.spawnInstance().apply {
            this.config().interceptor(interceptor)
        }

        bridgeRestHttpClient = RestHttpClient(bridgeUnirestInstance)
        defaultRestHttpClient = RestHttpClient(defaultUnirestInstance)
    }

    @Test
    fun `test default unirest get request`() {
        val url = "http://localhost:9988/api/default/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = defaultRestHttpClient.getAsString(url, headers)

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid default get response data }")
    }

    @Test
    fun `test default unirest post request`() {
        val url = "http://localhost:9988/api/default/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = defaultRestHttpClient.postAsString(url, headers, "post body")

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid default post response data }")
    }

    @Test
    fun `test default unirest patch request`() {
        val url = "http://localhost:9988/api/default/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = defaultRestHttpClient.patchAsString(url, headers, "patch body")

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid default patch response data }")
    }

    @Test
    fun `test default unirest delete request`() {
        val url = "http://localhost:9988/api/default/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = defaultRestHttpClient.deleteAsString(url, headers)

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid default delete response data }")
    }

    @Test
    fun `test bridge unirest get request`() {
        val url = "http://localhost:9988/api/bridge/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = bridgeRestHttpClient.getAsString(url, headers)

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid bridge get response data }")
    }

    @Test
    fun `test bridge unirest post request`() {
        val url = "http://localhost:9988/api/bridge/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = bridgeRestHttpClient.postAsString(url, headers, "post body")

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid bridge post response data }")
    }

    @Test
    fun `test bridge unirest patch request`() {
        val url = "http://localhost:9988/api/bridge/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = bridgeRestHttpClient.patchAsString(url, headers, "patch body")

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid bridge patch response data }")
    }

    @Test
    fun `test bridge unirest delete request`() {
        val url = "http://localhost:9988/api/bridge/test"
        val headers = mapOf("Content-Type" to "application/json")

        val response = bridgeRestHttpClient.deleteAsString(url, headers)

        Assertions.assertEquals(200, response.status)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals(response.body, "{ valid bridge delete response data }")
    }

    companion object {
        @BeforeAll
        @JvmStatic
        fun startUp() {
            InternalMockServer.startServer()
            InternalMockServer.mockDefaultRequest()
            InternalMockServer.mockBridgeRequest()
        }

        @AfterAll
        @JvmStatic
        fun shutdown() {
            InternalMockServer.stopServer()
        }
    }
}