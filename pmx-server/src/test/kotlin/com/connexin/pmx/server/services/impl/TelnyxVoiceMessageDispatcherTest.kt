package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.FakeUUIDSource
import com.connexin.pmx.server.TestFileUtil
import com.connexin.pmx.server.config.TelnyxProperties
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Language
import com.connexin.pmx.server.models.MessageStatus
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.UrlGenerator
import com.connexin.pmx.server.services.WebhookDeduper
import com.connexin.pmx.server.utils.Base64Util
import com.fasterxml.jackson.databind.JsonNode
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.api.CallCommandsApi
import com.telnyx.sdk.model.*
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Duration
import java.time.Instant
import java.util.*
import java.util.stream.Stream

@ExtendWith(MockKExtension::class)
class TelnyxVoiceMessageDispatcherTest {
    @MockK
    lateinit var api: CallCommandsApi

    @RelaxedMockK
    lateinit  var deduper: WebhookDeduper

    @MockK
    private lateinit  var pmxMessageService: PmxMessageService

    private val uuidSource = FakeUUIDSource(UUID.randomUUID())
    private lateinit var sut: TelnyxVoiceMessageDispatcher
    private val urlGenerator = UrlGenerator("http://localhost:8080", "http://localhost:8080")
    private val properties = TelnyxProperties.Engagement(
        englishMessagingProfileId = "test-english-messaging-profile-id",
        spanishMessagingProfileId = "test-spanish-messaging-profile-id",
        englishVoiceNumbers = "+14405551234",
        spanishVoiceNumbers = "+14405551235",
        callControlConnectionId = "test-call-control-id"
    )

    private val customer = Customer(
        id = "1",
        organizationId = 1L,
        name = "Test",
        telnyxConfig = Customer.TelnyxConfig(
            callControlConnectionId = "4000eba1-a0c0-4563-9925-b25e842a7cb6",
            phoneNumbers = mutableSetOf(
                Customer.TelnyxConfig.PhoneNumber(
                    id = "1234",
                    phoneNumber = "+18001234567",
                    defaultVoice = true,
                    defaultMessaging = true,
                    orderId = "test",
                    orderStatus = PhoneNumberOrderStatus.SUCCESS
                )
            )
        ),
        status = CustomerStatus.ENABLED
    )

    @BeforeEach
    fun setup() {
        sut = TelnyxVoiceMessageDispatcher(api, TestFileUtil.mapper, uuidSource,
            urlGenerator, 120, 3600, deduper, pmxMessageService, properties)
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `beforeRespond should return context with new remote ID and json payload`() {
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_answered_by_voicemail.json")
        val context = RespondContext(
            payload = json.get(0).toString(),
            type = MessageType.VOICE
        )
        every { pmxMessageService.getByRemoteId(any()) } returns null

        val actual = sut.beforeRespond(context)

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull
        assertThat(actual.context.payload).isEqualTo(context.payload)
        assertThat(actual.context.type).isEqualTo(context.type)
        assertThat(actual.context.remoteId).isEqualTo("v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A")
        assertThat(actual.context.decodedPayload).isNotNull
    }

    @ParameterizedTest(name = "beforeRespond should not accept if remoteId cannot be resolved for webhooks_invalid.json at index {0}")
    @MethodSource("webhooksInvalidIndexes")
    fun `beforeRespond should not accept if remoteId cannot be resolved`(jsonIndex: Int) {
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_invalid.json")
        val context = RespondContext(
            payload = json.get(jsonIndex).toString(),
            type = MessageType.VOICE
        )

        val actual = sut.beforeRespond(context)

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
        assertThat(actual.context).isNotNull
        assertThat(actual.context.payload).isEqualTo(context.payload)
        assertThat(actual.context.type).isEqualTo(context.type)
        assertThat(actual.context.decodedPayload).isNotNull
        assertThat(actual.context.remoteId).isNull()
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `initiate should create call`(legacy: Boolean, language: Language) {
        val response = TestFileUtil.get("/telnyx/calls/dial_success_response.json", RetrieveCallStatusResponse::class.java)
        val expected = CallRequest()
        expected.commandId = uuidSource.randomUUIDString()
        expected.to = "+14405551234"
        expected.from =when {
            !legacy && language == Language.ENGLISH -> properties.englishVoiceNumbers
            !legacy && language == Language.SPANISH -> properties.spanishVoiceNumbers
            else -> "+18001234567"
        }
        expected.connectionId = if (legacy) customer.telnyxConfig.callControlConnectionId!! else properties.callControlConnectionId
        expected.clientState = Base64Util.encode("init")
        expected.timeoutSecs = 120
        expected.timeLimitSecs = 3600
        expected.answeringMachineDetection = CallRequest.AnsweringMachineDetectionEnum.DETECT_WORDS
        expected.webhookUrl = "http://localhost:8080/webhooks/pmx/voice/incoming"
        expected.webhookUrlMethod = CallRequest.WebhookUrlMethodEnum.POST
        val message = PmxMessage(
            type = MessageType.VOICE,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            replyTo = "+14405554321",
            message = "test",
            altMessage = "voicemail test",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        every { api.dialCall(expected) } returns response

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isTrue
        assertThat(actual.remoteId).isEqualTo(response.data!!.callControlId)

        verifySequence {
            api.dialCall(expected)
        }
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `respond should deliver voicemail workflow`(legacy: Boolean, language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            message = "test",
            altMessage = "voicemail test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_answered_by_voicemail.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null

        // webhooks (w/ expected state) and expected actions to take when a call is left to voicemail
        // 1 call.initiated (init) - do nothing
        respondAndVerify(message, json, 0)

        // 2 call.answered (init) - start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "13"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // 3 call.machine.detection.ended- do nothing (wait until machine greeting ended)
        respondAndVerify(message, json, 2)

        // 4 call.gather.ended - do nothing, still waiting for answering maching beep
        respondAndVerify(message, json, 3)

        // 5 call.machine.greeting.ended - mark voicedeliverymethod as VOICEMAIL and speak altMessage
        respondAndVerify(message, json, 4) {
            val expected = SpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("say-vm")
            expected.payload = "voicemail test"
            expected.voice = SpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) SpeakRequest.LanguageEnum.EN_US else SpeakRequest.LanguageEnum.ES_US
            expected.stop = "all"
            api.speakCall(message.remoteId, expected)
        }
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.VOICE_MAIL)

        // 6 call.speak.started (say-vm) - do nothing
        respondAndVerify(message, json, 5)

        // 7 call.speak.ended (say-vm) - hang up
        respondAndVerify(message, json, 6) {
            val expected = HangupRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("hangup-vm")
            api.hangupCall(message.remoteId, expected)
        }

        // 8 call.hangup (hangup-vm) - mark status as DELIVERED
        respondAndVerify(message, json, 7)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `respond should hangup if voicemail reached with no altMessage`(legacy: Boolean, language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_answered_by_voicemail_no_alt_message.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null

        // webhooks (w/ expected state) and expected actions to take when a call is left to voicemail
        // 1 call.initiated (init) - do nothing
        respondAndVerify(message, json, 0)

        // 2 call.answered (init) - start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "13"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // 3 call.machine.detection.ended- do nothing (wait until machine greeting ended)
        respondAndVerify(message, json, 2)

        // 4 call.gather.ended - do nothing, still waiting for answering maching beep
        respondAndVerify(message, json, 3)

        // 5 call.machine.greeting.ended - hang up because no alt message
        respondAndVerify(message, json, 4) {
            val expected = HangupRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("hangup-vm")
            api.hangupCall(message.remoteId, expected)
        }
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.VOICE_MAIL)

        // 6 call.hangup (hangup-vm) - mark status as DELIVERED
        respondAndVerify(message, json, 5)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.VOICE_MAIL)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `respond should hangup if the user refuses to select an option`(legacy: Boolean, language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_user_silent_treatment.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null

        // call initiated
        respondAndVerify(message, json, 0)

        // call answered, start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "13"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // call.machine.detection.ended, it's a human, mark it as delivered live but otherwise still waiting for gather response
        respondAndVerify(message, json, 2)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)

        // call.gather.ended, they didn't enter anything, so apologize and hang up
        respondAndVerify(message, json, 3) {
            val expected = SpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("say-no-response")
            expected.payload = if (language == Language.ENGLISH) "Sorry, I didn't get your response. If you have any questions, please contact the practice. Goodbye!" else "lo siento, no llego su respuesta. Si tiene alguna pregunta, communiquese con la oficina. Adios!"
            expected.voice = SpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) SpeakRequest.LanguageEnum.EN_US else SpeakRequest.LanguageEnum.ES_US
            expected.stop = "all"
            api.speakCall(message.remoteId, expected)
        }

        // call.speak.started, starting apology
        respondAndVerify(message, json, 4)

        // call.speak.ended, hang up
        respondAndVerify(message, json, 5) {
            val expected = HangupRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("hangup-no-response")
            api.hangupCall(message.remoteId, expected)
        }

        // final hangup callback
        respondAndVerify(message, json, 6)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `respond should confirm and hang up when user picks option 1`(legacy: Boolean, language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_user_confirms.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null

        // call initiated
        respondAndVerify(message, json, 0)

        // call answered, start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "13"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // amd determined it's human, mark as delivered live
        respondAndVerify(message, json, 2)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)

        // gathered response, mark as confirmed and say thank you
        respondAndVerify(message, json, 3) {
            val expected = SpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("say-goodbye-confirm")
            expected.payload = if (language == Language.ENGLISH) "Thank you. Goodbye!" else "Gracias. Adios!"
            expected.voice = SpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) SpeakRequest.LanguageEnum.EN_US else SpeakRequest.LanguageEnum.ES_US
            expected.stop = "all"
            api.speakCall(message.remoteId, expected)
        }
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)

        // ignore dtmf when user presses a button
        respondAndVerify(message, json, 4)

        // say thank you started
        respondAndVerify(message, json, 5)

        // done saying thanks, hang up
        respondAndVerify(message, json, 6) {
            val expected = HangupRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("hangup-confirm")
            api.hangupCall(message.remoteId, expected)
        }

        // call complete
        respondAndVerify(message, json, 7)

        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
    }

    @Test
    fun `respond should transfer if a replyTo number is provided and the message is legacy and the user presses 2`() {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            engagementId = null
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_user_xfer.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null
        every { api.transferCall(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null

        // call initiated
        respondAndVerify(message, json, 0)

        // call answered, start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = GatherUsingSpeakRequest.LanguageEnum.EN_US
            expected.validDigits = "123"
            expected.invalidPayload = "Press 1 to confirm, 2 to speak to the practice, 3 to repeat, or hang up if you do not wish to confirm."
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // ignore amd
        respondAndVerify(message, json, 2)

        // gather ended, they pressed 2 so say please hold
        respondAndVerify(message, json, 3) {
            val expected = SpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("say-xfer")
            expected.payload = "Transferring your call. Please hold."
            expected.voice = SpeakRequest.VoiceEnum.FEMALE
            expected.language = SpeakRequest.LanguageEnum.EN_US
            expected.stop = "all"
            api.speakCall(message.remoteId, expected)
        }
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.UNCONFIRMED)

        // ignore DTMF event when user presses a button
        respondAndVerify(message, json, 4)

        // started saying please hold
        respondAndVerify(message, json, 5)

        // transfer the call after finished speaking
        respondAndVerify(message, json, 6) {
            val expected = TransferCallRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("xfer")
            expected.to = message.replyTo!!
            expected.targetLegClientState = Base64Util.encode("xfer-init")
            api.transferCall(message.remoteId, expected)
        }

        // ignore the call bridged event that occurs when the other caller is connected
        respondAndVerify(message, json, 7)

        // when they hang up, mark the message as delivered. they have to hang up! it will
        // eventually timeout after an hour, might be worth figuring out how to disconnect
        // if it's detected the transferred party disconnected
        respondAndVerify(message, json, 8)

        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
    }

    @ParameterizedTest
    @MethodSource("languages")
    fun `respond should decline and hang up if the message is related to an engagement and the user presses 2`(language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test instructions",
            altMessage = "test",
            instructions = "instructions",
            engagementId = "engagement",
            engagementRuleId = "rule",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            language = language
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_user_declines.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null

        // call initiated
        respondAndVerify(message, json, 0)

        // call answered, start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test instructions"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "123"
            expected.invalidPayload = "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }
        // ignore AMD
        respondAndVerify(message, json, 2)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)


        // gather finished, decline and play decline message
        respondAndVerify(message, json, 3) {
            val expected = SpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("say-goodbye-decline")
            expected.payload = if (language == Language.ENGLISH) "Your appointment has been cancelled. Goodbye!" else "Su cita ha sido cancelada"
            expected.voice = SpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) SpeakRequest.LanguageEnum.EN_US else SpeakRequest.LanguageEnum.ES_US
            expected.stop = "all"
            api.speakCall(message.remoteId, expected)
        }
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.DECLINED)

        // ignore DMTF
        respondAndVerify(message, json, 4)

        // started speaking
        respondAndVerify(message, json, 5)

        // finished speaking, now hang up
        respondAndVerify(message, json, 6) {
            val expected = HangupRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("hangup-decline")
            api.hangupCall(message.remoteId, expected)
        }

        // ensure message is marked as delivered on hangup
        respondAndVerify(message, json, 7)

        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isNotNull
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.DECLINED)
    }

    @ParameterizedTest
    @MethodSource("legacyAndLanguages")
    fun `respond should replay gather when they press 3`(legacy: Boolean, language: Language) {
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw",
            status = MessageStatus.DISPATCHED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH,
            instructions = if(legacy) null else "instructions",
            language = if(legacy) null else language,
            engagementId = if (legacy) null else "engagement",
            engagementRuleId = if(legacy) null else "rule"
        )
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_user_replays_message.json")
        every { api.speakCall(any(), any()) } returns null
        every { api.gatherUsingSpeak(any(), any()) } returns null
        every { api.hangupCall(any(), any()) } returns null

        // call initiated
        respondAndVerify(message, json, 0)

        // call answered, start gathering
        respondAndVerify(message, json, 1) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "123"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 2 to speak to the practice, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }

        // ignore amd
        respondAndVerify(message, json, 2)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)


        // get the keypress at the end of the gather action, which should be 3,
        // replay the menu
        respondAndVerify(message, json, 3) {
            val expected = GatherUsingSpeakRequest()
            expected.commandId = uuidSource.randomUUIDString()
            expected.clientState = Base64Util.encode("gather-confirm")
            expected.payload = "test"
            expected.voice = GatherUsingSpeakRequest.VoiceEnum.FEMALE
            expected.language = if (language == Language.ENGLISH) GatherUsingSpeakRequest.LanguageEnum.EN_US else GatherUsingSpeakRequest.LanguageEnum.ES_US
            expected.validDigits = "123"
            expected.invalidPayload = if (legacy) "Press 1 to confirm, 2 to speak to the practice, 3 to repeat, or hang up if you do not wish to confirm." else "instructions"
            expected.maximumDigits = 1
            expected.timeoutMillis = 10000
            expected.maximumTries = 3
            api.gatherUsingSpeak(message.remoteId, expected)
        }
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.UNCONFIRMED)

        // ignore DTMF event when user presses a button
        respondAndVerify(message, json, 4)

        // user hung up in the middle of the menu, ignore
        respondAndVerify(message, json, 5)

        // mark the message as delivered.
        respondAndVerify(message, json, 6)
        assertThat(message.voiceDeliveryMethod).isEqualTo(VoiceDeliveryMethod.LIVE)
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.NO_RESPONSE)
        assertThat(message.completedAt).isNotNull
    }

    @Test
    fun `deduperService should skip duplicate webhooks`() {
        val json = TestFileUtil.getAsTree("/telnyx/calls/webhooks_duplicate.json")
        //Sending unique webhook
        var context = RespondContext(
                payload = json.get(0).toString(),
                type = MessageType.VOICE
        )

        every { pmxMessageService.getByRemoteId(any()) } returns null

        every { deduper.isDuplicate(any()) } returnsMany listOf(false, true, false)

        //Should save this webhook and deduperService should return false
        var actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull

        //Sending duplicate webhook
        //Should skip duplicate webhook and deduperService should return true
        actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
        assertThat(actual.context).isNotNull

        //Sending unique webhook
        context = RespondContext(
                payload = json.get(1).toString(),
                type = MessageType.VOICE
        )

        //Should save this webhook and deduperService should return false
        actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull
    }


    @ParameterizedTest(name = "given the response status of {0} and error code {1}, initiate should retry if unavailable concurrency or rate limiting occurs")
    @MethodSource("retryErrorResponses")
    fun `initiate should retry if unavailable concurrency or rate limiting occurs`(httpStatus: Int, errorCode: String?) {
        every { api.dialCall(any()) } throws ApiException(httpStatus, "error", emptyMap(), "{\n" +
                "  \"errors\": [\n" +
                "    {\n" +
                "      \"code\": \"$errorCode\",\n" +
                "      \"detail\": \"User channel limit exceeded D1\",\n" +
                "      \"title\": null\n" +
                "    }\n" +
                "  ]\n" +
                "}")
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "72cefbc2-7b0a-11ec-82ee-02420a0d7e68",
            status = MessageStatus.QUEUED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(actual.retryDelay).isEqualTo(Duration.ofMinutes(5))
    }

    @Test
    fun `initiate should not request retry if unhandled exception`() {
        every { api.dialCall(any()) } throws Exception("test")
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "72cefbc2-7b0a-11ec-82ee-02420a0d7e68",
            status = MessageStatus.QUEUED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isFalse
        assertThat(actual.errors).isEqualTo("test")
    }

    @Test
    fun `initiate should not request retry if non-retryable ApiException`() {
        every { api.dialCall(any()) } throws ApiException(401, emptyMap(), "test")
        val message = PmxMessage(
            type = MessageType.VOICE,
            id = "test",
            remoteId = "72cefbc2-7b0a-11ec-82ee-02420a0d7e68",
            status = MessageStatus.QUEUED,
            voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,
            confirmationStatus = ConfirmationStatus.UNCONFIRMED,
            to = "+14405551234",
            replyTo = "+18002189916",
            message = "test",
            customerId = "1",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isFalse
        assertThat(actual.errors).isEqualTo("test")
    }

    @Test
    fun `beforeRespond should hang up on unsolicited inbound call`() {
        val webhookPayload = TestFileUtil.getAsString("/telnyx/calls/webhooks_unsolicited_call.json")
        val expected = RejectRequest()
        expected.commandId = uuidSource.randomUUIDString()
        expected.cause = RejectRequest.CauseEnum.CALL_REJECTED
        expected.clientState = Base64Util.encode(TelnyxVoiceMessageDispatcher.STATE_REJECT_UNSOLICITED)

        every { pmxMessageService.getByRemoteId(any()) } returns null

        every { api.rejectCall("18f722b2-7af8-11ec-8c3b-02420a0d4068", expected) } returns null

        val actual = sut.beforeRespond(
            RespondContext(
            payload = webhookPayload,
            type = MessageType.VOICE,

        ))

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse

        verifySequence {
            api.rejectCall("18f722b2-7af8-11ec-8c3b-02420a0d4068", expected)
        }
    }

    private fun respondAndVerify(
        message: PmxMessage,
        json: JsonNode,
        webhookIndex: Int,
        verifyBlock: (MockKVerificationScope.() -> Unit)? = null) {
        val actual = sut.respond(
            RespondContext(
                payload = "",
                type = MessageType.VOICE,
                message = message,
                decodedPayload = json.get(webhookIndex)
            )
        )

        assertThat(actual.success).isTrue
        if (verifyBlock == null) {
            confirmVerified(api)
        } else {
            verifySequence(verifyBlock = verifyBlock)
        }

        clearMocks(api, recordedCalls = true, answers = false)
    }

    companion object {
        @JvmStatic
        fun retryErrorResponses(): Stream<Arguments> = Stream.of(
            Arguments.of(429, null as String?),
            Arguments.of(403, "90041"),
            Arguments.of(403, "90042"),
            Arguments.of(403, "90043"),
            Arguments.of(504, null as String?),
        )

        @JvmStatic
        fun webhooksInvalidIndexes(): Stream<Arguments> = Stream.of(
            Arguments.of(0),
            Arguments.of(1)
        )

        @JvmStatic
        fun legacyAndLanguages() : Stream<Arguments> = Stream.of(
            Arguments.of(true, Language.ENGLISH),
            Arguments.of(false, Language.ENGLISH),
            Arguments.of(false, Language.SPANISH)
        )

        @JvmStatic
        fun languages() : Stream<Arguments> = Stream.of(
            Arguments.of(Language.ENGLISH),
            Arguments.of(Language.SPANISH)
        )
    }
}