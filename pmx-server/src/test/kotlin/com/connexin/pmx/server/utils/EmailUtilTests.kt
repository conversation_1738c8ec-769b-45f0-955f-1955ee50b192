package com.connexin.pmx.server.utils

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class EmailUtilTests {
  @ParameterizedTest
  @MethodSource("emailCases")
  fun `isValid should return correct value`(value: String, expected: Boolean) {

    val actual = EmailUtil.isValid(value)

    assertThat(actual).isEqualTo(expected)

  }

  companion object {
    @JvmStatic
    fun emailCases() = listOf(
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("\"email\"@officepracticum.com", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("much.”more\\ unusual”@officepracticum.com", true),
      Arguments.of("<EMAIL>", true),
      Arguments.of("あいうえお@officepracticum.com", true),
      // invalid addresses
      Arguments.of("very.unusual.”@”.<EMAIL>", false),
      Arguments.of("very.”(),:;<>[]”.VERY.”very@\\\\ \"very”.<EMAIL>", false),
      Arguments.of("ips.not.allowed@***************", false),
      Arguments.of("ips.not.allowed@[***************]", false),
      Arguments.of("<EMAIL>", false),
      Arguments.of("plainaddress", false),
      Arguments.of("#@%^%#$@#$@#.com", false),
      Arguments.of("@officepracticum.com", false),
      Arguments.of("Joe Smith <<EMAIL>>", false),
      Arguments.of("email.officepracticum.com", false),
      Arguments.of("email@<EMAIL>", false),
      Arguments.of(".<EMAIL>", false),
      Arguments.of("<EMAIL>", false),
      Arguments.of("<EMAIL>", false),
      Arguments.of("email@officepracticum", false),
      Arguments.of("<EMAIL>", false),
      Arguments.of("email@111.222.333.44444", false),
      Arguments.of("<EMAIL>", false),
      Arguments.of("<EMAIL>", false),
    )
  }
}