package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.Constants
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.dtos.ErrorResponseDto
import com.connexin.pmx.server.models.dtos.ResponseDto
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import java.time.OffsetDateTime
import java.time.ZoneOffset

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(ResponseController::class)
class ResponseControllerTests {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var customerService: CustomerService

    @MockkBean
    lateinit var engagementService: EngagementService

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        customer = Customer(
            id = "1",
            name = "Test",
            status = CustomerStatus.ENABLED
        )

        every { customerService.getById("1") } returns customer
    }

    @Test
    fun `findResponses returns page`() {
        val response: Page<ResponseDto> = PageImpl(
            listOf(
                ErrorResponseDto(
                    id = "error",
                    occurredAt = OffsetDateTime.of(2023, 4, 20, 4, 20, 0, 0, ZoneOffset.UTC).toInstant(),
                    customerId = "1",
                    engagementId = "100",
                    respondents = emptyList(),
                    appointments = emptyList(),
                    errors = emptyList()
                )
            )
        )

        every { engagementService.findResponses(any(), any(), any()) } returns response

        mockMvc.perform(
            get("/api/v2/responses")
                .header(Constants.X_OPMED, "1")
                .param("since", "2023-03-01T12:00:00.000Z")
                .param("size", "42")
                .param("page", "2")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json("{\n" +
                    "  \"content\": [\n" +
                    "    {\n" +
                    "      \"id\": \"error\",\n" +
                    "      \"occurredAt\": \"2023-04-20T04:20:00Z\",\n" +
                    "      \"customerId\": \"1\",\n" +
                    "      \"engagementId\": \"100\",\n" +
                    "      \"respondents\": [],\n" +
                    "      \"appointments\": [],\n" +
                    "      \"errors\": [],\n" +
                    "      \"type\": \"ERROR\",\n" +
                    "      \"isFinal\": true\n" +
                    "    }\n" +
                    "  ],\n" +
                    "  \"pageable\": \"INSTANCE\",\n" +
                    "  \"totalElements\": 1,\n" +
                    "  \"totalPages\": 1,\n" +
                    "  \"last\": true,\n" +
                    "  \"numberOfElements\": 1,\n" +
                    "  \"size\": 1,\n" +
                    "  \"number\": 0,\n" +
                    "  \"first\": true,\n" +
                    "  \"sort\": { \"unsorted\": true, \"sorted\": false, \"empty\": true },\n" +
                    "  \"empty\": false\n" +
                    "}"))

        verify { engagementService.findResponses(eq("1"), match { it.since != null && it.messageId == null && !it.archived }, match { it.pageSize == 42 && it.pageNumber == 2 }) }
    }
}