package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.Engagement
import com.connexin.pmx.server.models.dtos.CreateEngagementRequest
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.models.dtos.UpdateEngagementRequest
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementEventHandler
import com.connexin.pmx.server.services.EngagementService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(EngagementController::class)
@WithMockUser(authorities = ["admin.engagements:read", "admin.engagements:write"])
class EngagementControllerTests {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var customerService: CustomerService

    @MockkBean
    lateinit var engagementService: EngagementService

    @MockkBean
    lateinit var engagementEventHandler: EngagementEventHandler

    private lateinit var customer: Customer

    private lateinit var engagement: Engagement

    @BeforeEach
    fun setup() {
        customer = Customer(
            id = "1234",
            name = "Test",
            status = CustomerStatus.ENABLED
        )

        engagement = Engagement(
            id = "1",
            customerId = customer.id!!,
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now()
        )

        every { customerService.getById(customer.id!!) } returns customer
        every { customerService.getById("1") } returns null
        every { engagementService.getById("1") } returns engagement
        every { engagementService.getById("2") } returns null
        every { engagementService.getByAppointmentId(customer.id!!, "1") } returns engagement
        every { engagementService.getByAppointmentId(customer.id!!, "2") } returns null
        every { engagementService.getByAppointmentId("1", "1") } returns null
    }

    @Test
    fun `getById should return 404 if customer is not found`() {
        mockMvc.perform(
            get(ENGAGEMENT_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getById should return 404 if engagement is not found`() {
        mockMvc.perform(
            get(BAD_ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getById should return 200 if engagement is found`() {
        mockMvc.perform(
            get(ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
//            .andExpect(content().json(mapper.writeValueAsString(engagement)))
    }

    @Test
    fun `getByAppointmentId should return 404 if customer is not found`() {
        mockMvc.perform(
            get(ENGAGEMENTS_URL)
                .header(X_OPMED, "1")
                .param("appointmentId", "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getByAppointmentId should return 404 if engagement is not found`() {
        mockMvc.perform(
            get(ENGAGEMENTS_URL)
                .header(X_OPMED, customer.id)
                .param("appointmentId", "2")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getByAppointmentId should return 200 if engagement is found`() {
        mockMvc.perform(
            get(ENGAGEMENTS_URL)
                .header(X_OPMED, customer.id)
                .param("appointmentId", "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
    }

    @Test
    fun `create should return 409 if engagement conflicts`() {
        every {
            engagementService.create(
                CreateEngagementRequest(
                    customerId = customer.id,
                    appointments = emptyList(),
                    contacts = emptyList()
                )
            )
        } returns Response.failure(status = HttpStatus.CONFLICT)

        mockMvc.perform(
            post(ENGAGEMENTS_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        CreateEngagementRequest(
                            appointments = emptyList(),
                            contacts = emptyList()
                        )
                    )
                )
        )
            .andExpect(status().`is`(HttpStatus.CONFLICT.value()))
    }

    @Test
    fun `create should create Engagement`() {
        every {
            engagementService.create(
                CreateEngagementRequest(
                    customerId = customer.id,
                    appointments = emptyList(),
                    contacts = emptyList()
                )
            )
        } returns Response.success(engagement)

        mockMvc.perform(
            post(ENGAGEMENTS_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        CreateEngagementRequest(
                            appointments = emptyList(),
                            contacts = emptyList()
                        )
                    )
                )
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
//            .andExpect(content().json(mapper.writeValueAsString(engagement)))
    }

    @Test
    fun `update should return 404 if engagement is not found`() {
        every {
            engagementService.update(
                UpdateEngagementRequest(
                    customerId = customer.id,
                    id = "2",
                    appointments = emptyList(),
                    contacts = emptyList()
                )
            )
        } returns Response.failure(status = HttpStatus.NOT_FOUND)

        mockMvc.perform(
            put(BAD_ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        UpdateEngagementRequest(
                            appointments = emptyList(),
                            contacts = emptyList()
                        )
                    )
                )
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `update should update Engagement`() {
        every {
            engagementService.update(
                UpdateEngagementRequest(
                    id = "1",
                    customerId = customer.id,
                    appointments = emptyList(),
                    contacts = emptyList()
                )
            )
        } returns Response.success(engagement)

        mockMvc.perform(
            put(ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        UpdateEngagementRequest(
                            appointments = emptyList(),
                            contacts = emptyList()
                        )
                    )
                )
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
//            .andExpect(content().json(mapper.writeValueAsString(engagement)))
    }

    @Test
    fun `delete should return 404 if customer is not found`() {
        mockMvc.perform(
            delete(ENGAGEMENT_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `delete should return 404 if engagement is not found`() {
        every {
            engagementService.deleteById("2")
        } returns Response.failure(status = HttpStatus.NOT_FOUND)

        mockMvc.perform(
            delete(BAD_ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `patch should return 404 if customer is not found`() {
        mockMvc.perform(
            patch(ENGAGEMENT_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
                .contentType("application/json-patch+json")
                .content("[]")
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `patch should return 404 if engagement is not found`() {
        mockMvc.perform(
            patch(BAD_ENGAGEMENT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType("application/json-patch+json")
                .content("[]")
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    companion object {
        const val ENGAGEMENTS_URL = "/api/v2/engagements"
        const val ENGAGEMENT_URL = "/api/v2/engagements/1"
        const val BAD_ENGAGEMENT_URL = "/api/v2/engagements/2"
    }
}