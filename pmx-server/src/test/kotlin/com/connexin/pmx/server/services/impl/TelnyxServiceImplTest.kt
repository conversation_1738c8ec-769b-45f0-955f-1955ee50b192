package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.TestFileUtil
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.model.CreateMessageRequest
import kong.unirest.HttpMethod
import kong.unirest.MockClient
import kong.unirest.UnirestParsingException
import kong.unirest.json.JSONException
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class TelnyxServiceImplTest {
    @Autowired
    lateinit var sut: TelnyxServiceImpl

    @Test
    fun `createMessage should send request and return resulting message ID`() {
        val expectedJson = TestFileUtil.getAsString("/telnyx/messaging/message_success_response.json")
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.MESSAGES_URL.toString())
            .thenReturn(expectedJson)

        val actual = sut.createMessage(buildCreateMessageRequest())

        assertThat(actual).isEqualTo("40385f64-5717-4562-b3fc-2c963f66afa6")

        mock.verifyAll()
    }

    @Test
    fun `createMessage should throw ApiException if response body is not parsable JSON`() {
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.MESSAGES_URL.toString())
            .thenReturn("test")

        val actual = assertThrows<ApiException> {
            sut.createMessage(buildCreateMessageRequest())
        }

        assertThat(actual.code).isEqualTo(200)
        assertThat(actual.message).isEqualTo("Could not parse response from vendor")
        assertThat(actual.responseHeaders).isNotNull
        assertThat(actual.responseBody).isEqualTo("test")
        assertThat(actual.cause).isInstanceOf(UnirestParsingException::class.java)

        mock.verifyAll()
    }

    @Test
    fun `createMessage should throw ApiException if response body is not expected JSON`() {
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.MESSAGES_URL.toString())
            .thenReturn("{ \"data\": { \"rubbish\": true } }")

        val actual = assertThrows<ApiException> {
            sut.createMessage(buildCreateMessageRequest())
        }

        assertThat(actual.code).isEqualTo(200)
        assertThat(actual.message).isEqualTo("Could not parse response from vendor")
        assertThat(actual.responseHeaders).isNotNull
        assertThat(actual.responseBody).isEqualTo("{\"data\":{\"rubbish\":true}}")
        assertThat(actual.cause).isInstanceOf(JSONException::class.java)

        mock.verifyAll()
    }

    @Test
    fun `createBillingGroup should return result object`() {
        val expectedJson = TestFileUtil.getAsString("/telnyx/billinggroup/create_success_response.json")
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.BILLING_GROUP_URL.toString())
            .thenReturn(expectedJson)

        val actual = sut.createBillingGroup("My billing group name")

        assertThat(actual).isNotNull
        assertThat(actual.name).isEqualTo("My billing group name")

        mock.verifyAll()
    }

    @Test
    fun `createBillingGroup should throw ApiException if the response is not parsable`() {
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.BILLING_GROUP_URL.toString())
            .thenReturn("{\"errors\":[]}").withStatus(400)

        val actual = assertThrows<ApiException> {
            sut.createBillingGroup("My billing group name")
        }

        assertThat(actual.code).isEqualTo(400)
        assertThat(actual.message).isEqualTo("Could not parse response from vendor")
        assertThat(actual.responseHeaders).isNotNull
        assertThat(actual.responseBody).isEqualTo("{\"errors\":[]}")
        assertThat(actual.cause).isInstanceOf(UnirestParsingException::class.java)

        mock.verifyAll()
    }

    @Test
    fun `searchTollFreeNumbers should return result object`() {
        val expectedJson = TestFileUtil.getAsString("/telnyx/phonenumbers/search_success_response.json")
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.AVAILABLE_PHONE_NUMBERS_URL.toString())
            .thenReturn(expectedJson)

        val actual = sut.searchTollFreeNumbers()

        assertThat(actual).isNotNull
        assertThat(actual).hasSize(1)

        mock.verifyAll()
    }

    @Test
    fun `searchTollFreeNumbers should throw ApiException if the response is not parsable`() {
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.AVAILABLE_PHONE_NUMBERS_URL.toString())
            .thenReturn("{\"errors\":[]}").withStatus(400)

        val actual = assertThrows<ApiException> {
            sut.searchTollFreeNumbers()
        }

        assertThat(actual.code).isEqualTo(400)
        assertThat(actual.message).isEqualTo("Could not parse response from vendor")
        assertThat(actual.responseHeaders).isNotNull
        assertThat(actual.responseBody).isEqualTo("{\"errors\":[]}")
        assertThat(actual.cause).isInstanceOf(UnirestParsingException::class.java)

        mock.verifyAll()
    }

    @Test
    fun `createCallControlConnection should return result object`() {
        val expectedJson = TestFileUtil.getAsString("/telnyx/callcontrol/create_success_response.json")
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.CALL_CONTROL_APPLICATIONS_URL.toString())
            .thenReturn(expectedJson)

        val actual = sut.createCallControlConnection(
            "test",
            "https://backup.example.com/hooks",
            "4000eba1-a0c0-4563-9925-b25e842a7cb6"
        )

        assertThat(actual).isNotNull

        mock.verifyAll()
    }

    @Test
    fun `createCallControlConnection should throw ApiException if the response is not parsable`() {
        val mock = MockClient.register()
        mock.expect(HttpMethod.POST, TelnyxServiceImpl.CALL_CONTROL_APPLICATIONS_URL.toString())
            .thenReturn("{\"errors\":[]}").withStatus(400)

        val actual = assertThrows<ApiException> {
            sut.createCallControlConnection(
                "test",
                "https://backup.example.com/hooks",
                "4000eba1-a0c0-4563-9925-b25e842a7cb6"
            )
        }

        assertThat(actual.code).isEqualTo(400)
        assertThat(actual.message).isEqualTo("Could not parse response from vendor")
        assertThat(actual.responseHeaders).isNotNull
        assertThat(actual.responseBody).isEqualTo("{\"errors\":[]}")
        assertThat(actual.cause).isInstanceOf(UnirestParsingException::class.java)

        mock.verifyAll()
    }

    companion object {
        private fun buildCreateMessageRequest(): CreateMessageRequest {
            return CreateMessageRequest()
                .messagingProfileId("4000eba1-a0c0-4563-9925-b25e842a7cb6")
                .to("+18665550001")
                .text("test")
                .type(CreateMessageRequest.TypeEnum.SMS)
                .useProfileWebhooks(false)
                .webhookUrl("https://backup.example.com/hooks")
        }
    }
}