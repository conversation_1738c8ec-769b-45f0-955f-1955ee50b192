package com.connexin.pmx.server.utils

import com.connexin.pmx.server.models.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream

private const val CONFIRMATION_ALL = "confirmation_all"

private const val SHOULD_NOT_SEE_EXPIRED = "should_not_see_expired"

private const val SHOULD_NOT_SEE_NOT_EFFECTIVE_YET = "should_not_see_not_effective_yet"

private const val EFFECTIVE_ALL = "effective_all"

private const val BY_APPT_TYPE = "by_appt_type"

private const val BY_STAFF = "by_staff"

private const val BY_PRACTICE = "by_practice"

private const val BY_LOCATION = "by_location"

private const val COMPLEX = "complex"

class CustomerExtensionTests {
    private val customer = Customer(
        status = CustomerStatus.ENABLED,
        name = "Test",
        engagementRules = mutableSetOf(
            EngagementRule(
                id = CONFIRMATION_ALL,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap()
            ),
            EngagementRule(
                id = EFFECTIVE_ALL,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap(),
                endDate = Instant.now().plusSeconds(60),
                startDate = Instant.now().minusSeconds(60)
            ),
            EngagementRule(
                id = "disabled",
                workflow = EngagementWorkflow.CONFIRMATION,
                enabled = false,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap()
            ),
            EngagementRule(
                id = SHOULD_NOT_SEE_EXPIRED,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap(),
                endDate = Instant.now().minusSeconds(60)
            ),
            EngagementRule(
                id = SHOULD_NOT_SEE_NOT_EFFECTIVE_YET,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap(),
                startDate = Instant.now().plusSeconds(60)
            ),
            EngagementRule(
                id = BY_APPT_TYPE,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = false,
                allPracticeLocations = true,
                templateIds = emptyMap(),
                appointmentTypes = setOf("99")
            ),
            EngagementRule(
                id = BY_STAFF,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = false,
                allAppointmentTypes = true,
                allPracticeLocations = true,
                templateIds = emptyMap(),
                staff = setOf("99")
            ),
            EngagementRule(
                id = BY_PRACTICE,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = false,
                templateIds = emptyMap(),
                practiceLocations = setOf(
                    PracticeLocationRule(
                        practiceId = "99",
                        allLocations = true
                    )
                )
            ),
            EngagementRule(
                id = BY_LOCATION,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = true,
                allAppointmentTypes = true,
                allPracticeLocations = false,
                templateIds = emptyMap(),
                practiceLocations = setOf(
                    PracticeLocationRule(
                        practiceId = "88",
                        allLocations = false,
                        locations = setOf("88")
                    )
                )
            ),
            EngagementRule(
                id = COMPLEX,
                workflow = EngagementWorkflow.CONFIRMATION,
                allStaff = false,
                allAppointmentTypes = false,
                allPracticeLocations = false,
                templateIds = emptyMap(),
                appointmentTypes = setOf("55"),
                staff = setOf("66"),
                practiceLocations = setOf(
                    PracticeLocationRule(
                        practiceId = "77",
                        allLocations = false,
                        locations = setOf("88")
                    )
                )
            ),
        )
    )

    @ParameterizedTest
    @MethodSource("ruleSettings")
    fun `getApplicableRules should return valid results`(
        location: String,
        staff: String,
        appointmentType: String,
        practice: String,
        expected: Set<String>
    ) {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                AppointmentResource(
                    id = "test",
                    startTime = Instant.now(),
                    reason = "test",
                    location = location,
                    staff = staff,
                    patient = "1",
                    appointmentType = appointmentType,
                    practice = practice,
                    cancelled = false
                )
            )
        )

        val actual = customer.getApplicableRules(engagement)

        assertThat(actual).isNotNull
        assertThat(actual!!.map {it.id}.toSet()).isEqualTo(expected)
    }

    @Test
    fun `getApplicableRules should return null if engagement contains no appointment`() {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf()
        )

        val actual = customer.getApplicableRules(engagement)

        assertThat(actual).isNull()
    }

    @Test
    fun `hasApplicableRule should return true`() {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                AppointmentResource(
                    id = "test",
                    startTime = Instant.now(),
                    reason = "test",
                    location = "1",
                    staff = "1",
                    patient = "1",
                    appointmentType = "1",
                    practice = "1",
                    cancelled = false
                )
            )
        )

        val actual = customer.hasApplicableRule(engagement)

        assertThat(actual).isTrue
    }

    @Test
    fun `hasApplicableRule should return false`() {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf()
        )

        val actual = customer.hasApplicableRule(engagement)

        assertThat(actual).isFalse
    }

    @Test
    fun `getApplicableRulesByWorkflow should suggest first rule per workflow`() {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                AppointmentResource(
                    id = "test",
                    startTime = Instant.now(),
                    reason = "test",
                    location = "1",
                    staff = "1",
                    patient = "1",
                    appointmentType = "1",
                    practice = "1",
                    cancelled = false
                )
            )
        )

        val actual = customer.getApplicableRulesByWorkflow(engagement)

        assertThat(actual).isEqualTo(mapOf(EngagementWorkflow.CONFIRMATION to customer.engagementRules.first()))
    }

    @Test
    fun `getApplicableRulesByWorkflow should return null`() {
        val engagement = Engagement(
            customerId = "1",
            eventDate = Instant.now(),
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf()
        )

        val actual = customer.getApplicableRulesByWorkflow(engagement)

        assertThat(actual).isNull()
    }

    companion object {
        @JvmStatic
        fun ruleSettings(): Stream<Arguments> = Stream.of(
            // location, staff, apptType, practice, expected
            Arguments.of("1", "2", "3", "4", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL)),
            Arguments.of("4", "3", "2", "1", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL)),
            Arguments.of("4", "3", "99", "1", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL, BY_APPT_TYPE)),
            Arguments.of("4", "99", "2", "1", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL, BY_STAFF)),
            Arguments.of("4", "3", "2", "99", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL, BY_PRACTICE)),
            Arguments.of("88", "3", "2", "88", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL, BY_LOCATION)),
            Arguments.of("88", "66", "55", "77", setOf(CONFIRMATION_ALL, EFFECTIVE_ALL, COMPLEX)),
        )
    }
}