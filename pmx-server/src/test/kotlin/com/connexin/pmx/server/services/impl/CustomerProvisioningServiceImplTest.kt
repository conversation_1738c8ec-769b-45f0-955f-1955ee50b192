package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.TestFileUtil
import com.connexin.pmx.server.client.TelnyxClient
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Errors
import com.connexin.pmx.server.models.dtos.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.telnyx.BillingGroupResponse
import com.connexin.pmx.server.models.telnyx.CallControlApplicationRequest
import com.connexin.pmx.server.models.telnyx.SearchPhoneNumberResponse
import com.connexin.pmx.server.repositories.CustomerProvisioningOrderRepository
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.MeterService
import com.connexin.pmx.server.services.TelnyxService
import com.connexin.pmx.server.services.UrlGenerator
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.api.MessagingProfilesApi
import com.telnyx.sdk.api.NumberConfigurationsApi
import com.telnyx.sdk.api.NumberOrdersApi
import com.telnyx.sdk.api.OutboundVoiceProfilesApi
import com.telnyx.sdk.model.*
import io.micrometer.core.instrument.Timer
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import net.javacrumbs.shedlock.core.LockAssert
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.SliceImpl
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.*

@ExtendWith(MockKExtension::class)
class CustomerProvisioningServiceImplTest {
    @MockK(relaxed = true)
    lateinit var meterService: MeterService

    @MockK(relaxed = true)
    lateinit var orderRepository: CustomerProvisioningOrderRepository

    @MockK
    lateinit var customerService: CustomerService

    @MockK(relaxed = true)
    lateinit var telnyxClient: TelnyxClient

    @MockK(relaxed = true)
    lateinit var adminService: TelnyxService

    @MockK(relaxed = true)
    lateinit var outboundVoiceProfilesApi: OutboundVoiceProfilesApi

    @MockK(relaxed = true)
    lateinit var messagingProfilesApi: MessagingProfilesApi

    @MockK(relaxed = true)
    lateinit var numberConfigurationsApi: NumberConfigurationsApi

    @MockK(relaxed = true)
    lateinit var numberOrdersApi: NumberOrdersApi

    private lateinit var sut: CustomerProvisioningServiceImpl

    private lateinit var customer: Customer

    private val urlGenerator = UrlGenerator("http://localhost:8080", "http://localhost:8080")

    @BeforeEach
    fun setup() {
        every { meterService.startTimer() } returns Timer.start()

        customer = Customer(
            id = "1",
            name = "Test",
            status = CustomerStatus.PROVISIONING
        )
        sut = CustomerProvisioningServiceImpl(
            orderRepository,
            customerService,
            urlGenerator,
            adminService,
            outboundVoiceProfilesApi,
            messagingProfilesApi,
            numberOrdersApi,
            numberConfigurationsApi,
            meterService,
            telnyxClient,
            "DEV",
            3,
            false,
            3
        )

        every { customerService.save(any()) } answers {
            firstArg()
        }

        every { customerService.getById("1") } returns customer

        every { orderRepository.save(any()) } answers { firstArg() }

        LockAssert.TestHelper.makeAllAssertsPass(true)
    }

    @Test
    fun `performNewCustomerProvisionStep should handle ApiException`() {
        val context = NewCustomerProvisionContext(
            customer = customer,
            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
        )
        val actual = sut.performNewCustomerProvisionStep(context, NewCustomerProvisioningStep.BILLING_GROUP) {
                _, _ ->
            throw ApiException("test")
        }

        assertThat(actual).isFalse
        assertThat(context.order.activity)
            .allMatch { it.error && it.description.startsWith("Received error from Telnyx during BILLING_GROUP phase.") }
    }

    @Test
    fun `performNewCustomerProvisionStep should handle other Exception`() {
        val context = NewCustomerProvisionContext(
            customer = customer,
            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
        )
        val actual = sut.performNewCustomerProvisionStep(context, NewCustomerProvisioningStep.BILLING_GROUP) {
                _, _ ->
            throw Exception("test")
        }

        assertThat(actual).isFalse
        assertThat(context.order.activity)
            .allMatch { it.error && it.description.startsWith("An unexpected error occurred during BILLING_GROUP phase.") }
    }

    @Test
    fun `performOrderNumberStep should skip if customer already has a number`() {
        val context = NewCustomerProvisionContext(
            customer = customer.copy(
                telnyxConfig = Customer.TelnyxConfig(
                    phoneNumbers = mutableSetOf(
                        Customer.TelnyxConfig.PhoneNumber(
                            id = "test",
                            phoneNumber = "19006492568",
                            orderId = "test",
                            orderStatus = PhoneNumberOrderStatus.PENDING,
                            defaultVoice = true,
                            defaultMessaging = true
                        )
                    )
                )
            ),
            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
        )

        val actual = sut.performOrderNumberStep(context)

        assertThat(actual).isTrue
        assertThat(context.customer.telnyxConfig.phoneNumbers).hasSize(1)
    }

    @Test
    fun `performOrderNumberStep should record error and return false if no numbers are available`() {
        val context = NewCustomerProvisionContext(
            customer = customer,
            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
        )
        every {
            adminService.searchTollFreeNumbers()
        } returns emptyList()

        val actual = sut.performOrderNumberStep(context)

        assertThat(actual).isFalse
        assertThat(context.order.activity)
            .allMatch { it.error && it.description.startsWith("No suitable toll-free numbers") }
    }
//TODO: Refactor to use new Job APIs

//    @Test
//    fun `performUpdateNumberOrderStatusStep should log error activity when ordered number fails`() {
//        val context = NewCustomerProvisionContext(
//            customer = customer.copy(
//                telnyxConfig = Customer.TelnyxConfig(
//                    phoneNumbers = mutableSetOf(
//                        Customer.TelnyxConfig.PhoneNumber(
//                            id = "test",
//                            phoneNumber = "19006492568",
//                            orderId = "test",
//                            orderStatus = PhoneNumberOrderStatus.PENDING,
//                            defaultVoice = true,
//                            defaultMessaging = true
//                        )
//                    )
//                )
//            ),
//            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
//            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
//        )
//        val res = NumberOrderResponse()
//        val order = NumberOrder()
//        val number = mockk<PhoneNumber>(relaxed = true)
//        every { number.status } returns PhoneNumber.StatusEnum.FAILURE
//        order.phoneNumbers = listOf(number)
//        res.data = order
//        every { numberOrdersApi.retrieveNumberOrder("test") } returns res
//
//        val actual = sut.performUpdateNumberOrderStatusStep(context)
//
//        assertThat(actual).isTrue
//        assertThat(context.order.activity)
//            .allMatch { it.error && it.description.startsWith("Status for number order test has changed from PENDING to FAILURE") }
//    }

    @Test
    fun `performCompleteStep should set an order status to failed if any numbers fail`() {
        val context = NewCustomerProvisionContext(
            customer = customer.copy(
                telnyxConfig = Customer.TelnyxConfig(
                    phoneNumbers = mutableSetOf(
                        Customer.TelnyxConfig.PhoneNumber(
                            id = "test",
                            phoneNumber = "19006492568",
                            orderId = "test",
                            orderStatus = PhoneNumberOrderStatus.FAILURE,
                            defaultVoice = true,
                            defaultMessaging = true
                        )
                    )
                )
            ),
            order = NewCustomerProvisioningOrder(customerId = customer.id!!),
            remainingSteps = listOf(NewCustomerProvisioningStep.BILLING_GROUP)
        )

        val actual = sut.performCompleteStep(context)

        assertThat(actual).isTrue
        assertThat(context.order.status).isEqualTo(ProvisioningStatus.FAILED)
    }

    @Test
    fun `provision should increment attempts if it fails`() {
        val order = CsvMultiCustomerProvisioningOrder(csvContent = "")

        val actual = sut.provision(order)

        assertThat(actual.success).isFalse
        assertThat(order.attempts).isEqualTo(1)
    }

    @Test
    fun `deprovisionV2 should return error when customer not found`() {
        val customerId = "123"
        val billingGroupId = "billingGroup123"
        val outboundVoiceProfileId = "outboundVoiceProfile123"

        every { customerService.getById(customerId) } returns null

        val result = sut.deprovisionV2(customerId, billingGroupId, outboundVoiceProfileId)

        assertFalse(result.success)
        assertEquals(listOf("Customer not found."), result.errors)
        verify { customerService.getById(customerId) }
        verify(exactly = 0) { telnyxClient.deleteBillingGroup(any()) }
        verify(exactly = 0) { telnyxClient.deleteOutboundVoiceProfile(any()) }
        verify(exactly = 0) { customerService.deleteById(any()) }
    }
    @Test
    fun `deprovisionV2 should succeed when customer is found`() {
        val customerId = "123"
        val billingGroupId = "billingGroup123"
        val outboundVoiceProfileId = "outboundVoiceProfile123"
        val customer = mockk<Customer>()

        every { customerService.getById(customerId) } returns customer
        every { telnyxClient.deleteBillingGroup(billingGroupId) } returns null // Return null to indicate success
        every { telnyxClient.deleteOutboundVoiceProfile(outboundVoiceProfileId) } returns null // Return null to indicate success
        every { customerService.deleteById(customerId) } just Runs

        val result = sut.deprovisionV2(customerId, billingGroupId, outboundVoiceProfileId)

        assertTrue(result.success)
        result.errors?.let { assertTrue(it.isEmpty()) }
        verify { customerService.getById(customerId) }
        verify { telnyxClient.deleteBillingGroup(billingGroupId) }
        verify { telnyxClient.deleteOutboundVoiceProfile(outboundVoiceProfileId) }
        verify { customerService.deleteById(customerId) }
    }


    //TODO: Need to refactor this to use new Job API to get all phone numbers registered to a customer

//    @Test
//    fun `provision should complete all steps`() {
//        val order = NewCustomerProvisioningOrder(
//            id = "test",
//            customerId = "1"
//        )
//        val billingGroupId = UUID.randomUUID()
//        val organizationId = UUID.randomUUID()
//        // create billing group first
//        every {
//            adminService.createBillingGroup("DEV 1 Test")
//        } returns BillingGroupResponse.BillingGroupResult(
//            createdAt = OffsetDateTime.now(),
//            deletedAt = null,
//            id = billingGroupId.toString(),
//            name = "DEV 1 Test",
//            organizationId = organizationId.toString(),
//            recordType = "billing_group",
//            updatedAt = OffsetDateTime.now()
//        )
//
//        // then outbound profile
//        val outboundProfileRequest = CreateOutboundVoiceProfileRequest()
//        val callRecording = OutboundCallRecording()
//        callRecording.callRecordingType = OutboundCallRecording.CallRecordingTypeEnum.NONE
//        outboundProfileRequest.billingGroupId = billingGroupId
//        outboundProfileRequest.dailySpendLimitEnabled = false
//        outboundProfileRequest.enabled = true
//        outboundProfileRequest.name = "DEV 1 Test"
//        outboundProfileRequest.tags = listOf("DEV", "opmedid-1")
//        outboundProfileRequest.servicePlan = ServicePlan.GLOBAL
//        outboundProfileRequest.trafficType = TrafficType.CONVERSATIONAL
//        outboundProfileRequest.whitelistedDestinations = listOf("US", "CA", "PR", "VI")
//        outboundProfileRequest.callRecording = callRecording
//        val outboundProfileResponse = OutboundVoiceProfileResponse()
//        val outboundProfile = OutboundVoiceProfile()
//        outboundProfile.id = "1293384261075731499"
//        outboundProfileResponse.data = outboundProfile
//
//        every {
//            outboundVoiceProfilesApi.createVoiceProfile(any())
//        } returns outboundProfileResponse
//
//        // then messaging profile
//        val numberPoolSettings = NumberPoolSettings()
//        numberPoolSettings.geomatch = false
//        numberPoolSettings.skipUnhealthy = false
//        numberPoolSettings.longCodeWeight = BigDecimal.valueOf(1)
//        numberPoolSettings.tollFreeWeight = BigDecimal.valueOf(10)
//        val messagingProfileRequest = CreateMessagingProfileRequest()
//        messagingProfileRequest.name = "DEV 1 Test - PMX"
//        messagingProfileRequest.enabled = true
//        messagingProfileRequest.numberPoolSettings = numberPoolSettings
//        messagingProfileRequest.webhookApiVersion = CreateMessagingProfileRequest.WebhookApiVersionEnum._2
//        messagingProfileRequest.webhookUrl = urlGenerator.pmxMessagingWebhook().toString()
//
//        val messagingProfileResponse = MessagingProfileResponse()
//        val messagingProfile = mockk<MessagingProfile>()
//        val messagingProfileId = UUID.randomUUID()
//        every { messagingProfile.id } returns messagingProfileId
//        messagingProfileResponse.data = messagingProfile
//
//        every {
//            messagingProfilesApi.createMessagingProfile(any())
//        } returns messagingProfileResponse
//
//        // then call control connection
//        val callControlId = UUID.randomUUID().toString()
//
//        every { adminService.createCallControlConnection(any(), any(), any()) } returns com.connexin.pmx.server.models.telnyx.CallControlApplicationRequest.CallControlApplication(
//            id = callControlId
//        )
//
//        // search and order a number
//        val phoneNumber = mockk<PhoneNumber>(relaxed = true)
//        val phoneNumberId = UUID.randomUUID()
//        every { phoneNumber.id } returns phoneNumberId
//        every { phoneNumber.phoneNumber } returns "+19006492568"
//        every { phoneNumber.status } returns PhoneNumber.StatusEnum.PENDING
//        every {
//            adminService.searchTollFreeNumbers()
//        } returns listOf(
//            SearchPhoneNumberResponse.PhoneNumber(
//                phoneNumber = phoneNumber.phoneNumber!!,
//                bestEffort = false,
//                costInformation = SearchPhoneNumberResponse.CostInformation("USD", "1.00", "1.00"),
//                features = emptyList(),
//                recordType = "available_phone_number",
//                quickship = true,
//                regionInformation = emptyList(),
//                reservable = true,
//                vanityFormat = null,
//                phoneNumberType = "toll_free"
//            )
//        )
//        val orderRequest = CreateNumberOrderRequest()
//        orderRequest.billingGroupId = billingGroupId.toString()
//        orderRequest.connectionId = callControlId
//        orderRequest.messagingProfileId = messagingProfileId.toString()
//        orderRequest.customerReference = "DEV 1 PMX"
//        val orderedNumber = PhoneNumber()
//        orderedNumber.phoneNumber = phoneNumber.phoneNumber
//        orderRequest.phoneNumbers = listOf(orderedNumber)
//        val orderResponse = NumberOrderResponse()
//        val numberOrderId = UUID.randomUUID()
//        val numberOrder = mockk<NumberOrder>()
//        every { numberOrder.id } returns numberOrderId
//        every { numberOrder.phoneNumbers } returns listOf(phoneNumber)
//        orderResponse.data = numberOrder
//        every {
//            numberOrdersApi.createNumberOrder(orderRequest)
//        } returns orderResponse
//
//        val checkOrderResponse = NumberOrderResponse()
//        val checkNumber = mockk<PhoneNumber>(relaxed = true)
//        every { checkNumber.id } returns phoneNumberId
//        every { checkNumber.phoneNumber } returns "+19006492568"
//        every { checkNumber.status } returns PhoneNumber.StatusEnum.SUCCESS
//        val checkNumberOrder = mockk<NumberOrder>(relaxed = true)
//        every { checkNumberOrder.id } returns numberOrderId
//        every { checkNumberOrder.phoneNumbers } returns listOf(checkNumber)
//        checkOrderResponse.data = checkNumberOrder
//        every { numberOrdersApi.retrieveNumberOrder(numberOrderId.toString()) } returns checkOrderResponse
//
//        // ACT
//        val actual = sut.provision(order)
//
//        // ASSERT/VERIFY
//        assertThat(actual.success).isTrue
//        assertThat(order.status).isEqualTo(ProvisioningStatus.COMPLETED)
//        assertThat(customer.status).isEqualTo(CustomerStatus.ENABLED)
//        assertThat(customer.telnyxConfig.billingGroupId).isEqualTo(billingGroupId.toString())
//        assertThat(customer.telnyxConfig.outboundVoiceProfileId).isEqualTo(outboundProfile.id)
//        assertThat(customer.telnyxConfig.messagingProfileId).isEqualTo(messagingProfileId.toString())
//        assertThat(customer.telnyxConfig.callControlConnectionId).isEqualTo(callControlId)
//        assertThat(customer.telnyxConfig.phoneNumbers).containsExactly(
//            Customer.TelnyxConfig.PhoneNumber(
//                id = phoneNumberId.toString(),
//                phoneNumber = phoneNumber.phoneNumber!!,
//                orderId = numberOrderId.toString(),
//                orderStatus = PhoneNumberOrderStatus.SUCCESS,
//                defaultVoice = true,
//                defaultMessaging = true
//            )
//        )
//
//        verifySequence {
//            customerService.getById("1")
//            adminService.createBillingGroup("DEV 1 Test")
//            outboundVoiceProfilesApi.createVoiceProfile(outboundProfileRequest)
//            messagingProfilesApi.createMessagingProfile(messagingProfileRequest)
//            adminService.createCallControlConnection(
//                "DEV 1 Test - PMX",
//                urlGenerator.pmxVoiceWebhook().toString(),
//                outboundProfile.id.toString()
//            )
//            adminService.searchTollFreeNumbers()
//            numberOrdersApi.createNumberOrder(orderRequest)
//            numberOrdersApi.retrieveNumberOrder(numberOrderId.toString())
//            customerService.save(customer)
//            orderRepository.save(order)
//        }
//    }

    @Test
    fun `create should create a customer and new customer order`() {
        val createCustomerRequest = CreateCustomerRequest(
            opmedId = "1",
            name = "Test",
            legacyPassword = "test1234",
            legacyUsername = "test"
        )
        every { customerService.create(createCustomerRequest) } returns CreateCustomerResponse(
            success = true,
            customer = customer
        )
        val request = CreateOrderRequest(
            opmedId = "1",
            name = "Test",
            legacyPassword = "test1234",
            legacyUsername = "test"
        )

        val actual = sut.create(request)

        assertThat(actual.success).isTrue
        assertThat(actual.order).isInstanceOf(NewCustomerProvisioningOrder::class.java)
        assertThat((actual.order!! as NewCustomerProvisioningOrder).customerId).isEqualTo(createCustomerRequest.opmedId)
        assertThat(actual.order?.status).isEqualTo(ProvisioningStatus.IN_PROGRESS)
        assertThat((actual.order!! as NewCustomerProvisioningOrder).step).isEqualTo(NewCustomerProvisioningStep.BILLING_GROUP)

        verifySequence {
            customerService.create(createCustomerRequest)
            orderRepository.save(any())
        }
    }

    @Test
    fun `create should fail for new customer request because customer could not be created`() {

        every { customerService.create(any()) } returns CreateCustomerResponse(
            success = false,
            errors = listOf(
                ErrorDto("opmedId", "Customer already exists", Errors.DUPLICATE.code)
            )
        )
        val request = CreateOrderRequest(
            opmedId = "1",
            name = "Test",
            legacyPassword = "test1234",
            legacyUsername = "test"
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).containsExactly(
            ErrorDto("opmedId", "Customer already exists", Errors.DUPLICATE.code)
        )

        verify(exactly = 0) {
            orderRepository.save(any())
        }
    }

    @Test
    fun `create should fail new customer order because of validation`() {
        val request = CreateOrderRequest(
            opmedId = "",
            name = "",
            legacyUsername = "",
            legacyPassword = ""
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).containsExactly(
            ErrorDto("opmedId", "Must not be blank", Errors.VALIDATION_FAILED.code),
            ErrorDto("name", "Must not be blank", Errors.VALIDATION_FAILED.code),
            ErrorDto("legacyUsername", "Must not be blank", Errors.VALIDATION_FAILED.code),
            ErrorDto("legacyPassword", "Must not be blank", Errors.VALIDATION_FAILED.code),
        )
    }

    @Test
    fun `create should create CSV order`() {
        val csv = TestFileUtil.getAsString("/provisioning/order.csv")
        val request = CreateCsvOrderRequest(
            csv = csv
        )

        val actual = sut.create(request)

        assertThat(actual.success).isTrue
        assertThat(actual.order).isInstanceOf(CsvMultiCustomerProvisioningOrder::class.java)
        val csvOrder = actual.order as CsvMultiCustomerProvisioningOrder
        assertThat(csvOrder.csvContent).isEqualTo(csv)

        verifySequence {
            orderRepository.save(any())
        }
    }

    @Test
    fun `create should fail validation for CSV order because of validation`() {
        val request = CreateCsvOrderRequest(csv = "")

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).containsExactly(
            ErrorDto("csv", "Must not be blank", Errors.VALIDATION_FAILED.code)
        )
    }

    @Test
    fun `provision a csv order should create customers and queue new customer orders`() {
        val csv = TestFileUtil.getAsString("/provisioning/order.csv")
        val order = CsvMultiCustomerProvisioningOrder(
            csvContent = csv
        )
        every { customerService.create(any()) } answers {
            val req = firstArg<CreateCustomerRequest>()
            CreateCustomerResponse(
                success = true, customer = Customer(
                    id = req.opmedId,
                    name = req.name,
                    legacyUsername = req.legacyUsername,
                    legacyPassword = req.legacyPassword,
                    status = CustomerStatus.PROVISIONING
                )
            )
        }
        every {
            orderRepository.save(any())
        } answers {
            firstArg()
        }

        val actual = sut.provision(order)

        assertThat(actual.success).isTrue

        verifySequence {
            for (i in 10..23) {
                customerService.create(
                    CreateCustomerRequest(
                        opmedId = i.toString(),
                        name = "Test Customer $i",
                        legacyUsername = "test$i",
                        legacyPassword = "test1234"
                    )
                )

                orderRepository.save(NewCustomerProvisioningOrder(
                    customerId = i.toString()
                ))
            }

            orderRepository.save(order)
        }
    }

    @Test
    fun `scheduledProvision should page through pending orders to the end`() {
        every { orderRepository.findPending(3, any()) } answers {
            val page = secondArg<PageRequest>()
            if (page.pageNumber < 3) {
                SliceImpl(
                    listOf(
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}1", csvContent = "opmedId,name,legacyUsername,legacyPassword"),
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}2", csvContent = "opmedId,name,legacyUsername,legacyPassword"),
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}3", csvContent = "opmedId,name,legacyUsername,legacyPassword"),
                    )
                )
            } else {
               SliceImpl(emptyList())
            }
        }

        sut.scheduledProvision()

        verifySequence {
            orderRepository.findPending(3, PageRequest.of(0, 3))
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.findPending(3, PageRequest.of(1, 3))
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.findPending(3, PageRequest.of(2, 3))
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.findPending(3, PageRequest.of(3, 3))
        }
    }

    @Test
    fun `scheduleProvision should safely handle exceptions thrown while fetching pending orders`() {
        every { orderRepository.findPending(any(), any()) } throws Exception("test")

        sut.scheduledProvision()

        verifySequence {
            orderRepository.findPending(3, PageRequest.of(0, 3))
        }
    }

    @Test
    fun `scheduleProvision should continue to other orders if one throws an exception`() {
        every { orderRepository.findPending(3, any()) } answers {
            val page = secondArg<PageRequest>()
            if (page.pageNumber < 1) {
                SliceImpl(
                    listOf(
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}1", csvContent = ""),
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}2", csvContent = ""),
                        CsvMultiCustomerProvisioningOrder(id = "${page.pageNumber}3", csvContent = ""),
                    )
                )
            } else {
                SliceImpl(emptyList())
            }
        }

        sut.scheduledProvision()

        verifySequence {
            orderRepository.findPending(3, PageRequest.of(0, 3))
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.save(any())
            orderRepository.findPending(3, PageRequest.of(1, 3))
        }
    }

    @Test
    fun `buildResourceName should limit the customer name portion to 30 characters`() {
        val customer = Customer(id = "12345", status = CustomerStatus.PROVISIONING, name = "Murdock Pediatrics Services dba Taylor Pediatrics PLLC TX")

        assertThat(sut.buildResourceName(customer)).isEqualTo("DEV 12345 Murdock Pediatrics Services db")
        assertThat(sut.buildResourceName(customer, suffix = " - PMX")).isEqualTo("DEV 12345 Murdock Pediatrics Services db - PMX")
    }
//TODO: Rewrite this test after implementing deprovision

//    @Test
//    fun `deprovision should report on successfully deleted billing groups`() {
//        val customer = Customer(
//            id = "someId",
//            status = CustomerStatus.ENABLED,
//            name = "Some Name",
//            telnyxConfig = Customer.TelnyxConfig(
//                billingGroupId = UUID.randomUUID().toString(),
//                outboundVoiceProfileId = "test-voice-profile",
//                messagingProfileId = UUID.randomUUID().toString(),
//                callControlConnectionId = "test-call-control",
//                phoneNumbers = mutableSetOf(
//                    Customer.TelnyxConfig.PhoneNumber(
//                        id = UUID.randomUUID().toString(),
//                        phoneNumber = "+15553330987",
//                        orderId = "",
//                        orderStatus = PhoneNumberOrderStatus.SUCCESS,
//                        defaultVoice = false,
//                        defaultMessaging = false
//                    )
//                )
//            )
//        )
//        val phoneNumberTrueId = "1234598761234"
//        every { customerService.getById(any()) } returns customer
//        every { adminService.deleteCallControlConnection(customer.telnyxConfig.callControlConnectionId!!) } returns CallControlApplicationRequest.CallControlApplication(
//            customer.telnyxConfig.callControlConnectionId!!
//        )
//        every {numberConfigurationsApi.listPhoneNumbers().filterBillingGroupId(customer.telnyxConfig.billingGroupId).execute() } returns ListPhoneNumbersResponse().data(
//            listOf(
//                mockk {
//                    every { id } returns phoneNumberTrueId
//                    every { billingGroupId } returns customer.telnyxConfig.billingGroupId
//                }
//            )
//        )
//        every { numberConfigurationsApi.deletePhoneNumber(phoneNumberTrueId) } returns PhoneNumberResponse1()
//        every { messagingProfilesApi.deleteMessagingProfile(UUID.fromString(customer.telnyxConfig.messagingProfileId!!)) } returns MessagingProfileResponse()
//        every { outboundVoiceProfilesApi.deleteOutboundVoiceProfile(customer.telnyxConfig.outboundVoiceProfileId) } returns OutboundVoiceProfileResponse()
//        every { adminService.deleteBillingGroup(UUID.fromString(customer.telnyxConfig.billingGroupId)) } returns BillingGroupResponse.BillingGroupResult(
//            OffsetDateTime.now(),
//            OffsetDateTime.now(),
//            customer.telnyxConfig.billingGroupId!!,
//            "test",
//            "test-org",
//            "test-record-type",
//            OffsetDateTime.now()
//        )
//
//        val deprovisionResult = sut.deprovision("d77750d8-1358-4978-a221-5dea8084797c")
//
//        assertThat(deprovisionResult.success).isTrue
//
//        verifySequence {
//            numberConfigurationsApi.listPhoneNumbers().filterBillingGroupId(any())
//            numberConfigurationsApi.deletePhoneNumber(any())
//            adminService.deleteCallControlConnection(any())
//            messagingProfilesApi.deleteMessagingProfile(any())
//            outboundVoiceProfilesApi.deleteOutboundVoiceProfile(any())
//            adminService.deleteBillingGroup(any())
//        }
//    }
}