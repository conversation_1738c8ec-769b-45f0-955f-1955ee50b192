package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.EngagementMessageHandler
import com.connexin.pmx.server.services.EngagementMessageResult
import com.connexin.pmx.server.services.EngagementResponseHandler
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import io.mockk.verifySequence
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

@ExtendWith(MockKExtension::class)
class EngagementStateMachineImplTests {
    private val customer = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateId = "test"
            )
        )
    )

    private val customerWithReminders = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateId = "test"
            ),
            EngagementRule(
                id = "2",
                workflow = EngagementWorkflow.REMINDER,
                templateId = "test"
            ),
            EngagementRule(
                id = "3",
                workflow = EngagementWorkflow.CANCELLATION,
                templateId = "test"
            )
        )
    )

    private val customerWithAllWorkflows = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateId = "test"
            ),
            EngagementRule(
                id = "2",
                workflow = EngagementWorkflow.REMINDER,
                templateId = "test"
            ),
            EngagementRule(
                id = "3",
                workflow = EngagementWorkflow.BOOKING,
                templateId = "test"
            ),
            EngagementRule(
                id = "4",
                workflow = EngagementWorkflow.CANCELLATION,
                templateId = "test"
            ),
            EngagementRule(
                id = "5",
                workflow = EngagementWorkflow.CHECKIN,
                templateId = "test"
            )
        )
    )

    private val customerWithRemindersOnly = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "2",
                workflow = EngagementWorkflow.REMINDER,
                templateId = "test"
            ),
            EngagementRule(
                id = "3",
                workflow = EngagementWorkflow.CANCELLATION,
                templateId = "test"
            )
        )
    )

    private val customerWithBookingOnly = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "2",
                workflow = EngagementWorkflow.BOOKING,
                templateId = "test"
            ),
            EngagementRule(
                id = "3",
                workflow = EngagementWorkflow.CANCELLATION,
                templateId = "test"
            )
        )
    )

    @RelaxedMockK
    lateinit var responseHandler: EngagementResponseHandler

    @RelaxedMockK
    lateinit var messageHandler: EngagementMessageHandler

    @InjectMockKs
    lateinit var sut: EngagementStateMachineImpl

    @BeforeEach
    fun setup() {
        every { responseHandler.recordConfirmation(any(), any(), any(), any(), any()) } answers {
            val engagement = firstArg<Engagement>()
            val result = secondArg<ConfirmationStatus>()
            val respondents = thirdArg<Set<ContactResource>>()
            val isFinal = arg<Boolean>(3)
            val messageId = arg<String?>(4)
            ConfirmationResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                isFinal = isFinal,
                respondents = respondents,
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                result = result,
                messageId = messageId
            )
        }
        every { responseHandler.recordComplete(any()) } answers {
            val engagement = firstArg<Engagement>()
            CompleteResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                respondents = emptySet(),
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet()
            )
        }
        every { responseHandler.recordError(any(), *anyVararg()) } answers {
            val engagement = firstArg<Engagement>()
            ErrorResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                respondents = emptySet(),
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                errors = emptyList()
            )
        }
        every { responseHandler.recordContactUnreachable(any(), any(), any()) } answers {
            val engagement = firstArg<Engagement>()
            val workflow = secondArg<EngagementWorkflow>()
            val contactErrors = lastArg<EngagementMessageResult.ContactErrors>()
            MessageResponse(
                engagementId = engagement.id!!,
                customerId = engagement.customerId,
                appointments = engagement.resources.filterIsInstance(AppointmentResource::class.java).toSet(),
                respondents = engagement.resources.filterIsInstance(ContactResource::class.java)
                    .filter { it.id.equals(contactErrors.id, ignoreCase = true) }.toSet(),
                status = MessageStatus.FAILED,
                workflow = workflow,
                errors = contactErrors.errors
            )
        }
    }

    @Test
    fun `should send booking notification immediately and then transition to confirm state`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(7, ChronoUnit.DAYS),
            cust = customerWithAllWorkflows
        )
        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithAllWorkflows), context)

        // INITIAL -> BOOK -> CONFIRM and set checkpoint to respective date
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.bookingAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(0)

        verifySequence {
            messageHandler.send(
                any(),
                eq(customerWithAllWorkflows.engagementRules.find { it.workflow == EngagementWorkflow.BOOKING }!!),
                eq(customerWithAllWorkflows),
                any(),
                any(),
                rules = any()
            )
        }
    }

    @Test
    fun `should send booking notification and then transition to AWAIT_COMPLETED if confirmations or reminders are not enabled`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(7, ChronoUnit.DAYS),
            cust = customerWithBookingOnly
        )
        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithBookingOnly), context)

        // INITIAL -> BOOK and set checkpoint to now
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.bookingAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start)
    }

    @Test
    fun `handleSendConfirmation should try to notify three times then transition to declined after the forth`() {
        val engagement = createEngagement(
            initialState = EngagementStatus.CONFIRM
        )
        val context = createContext()
        val event = CheckpointEvent(
            engagement, customer
        )

        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        // send message and set checkpoint to 3 days prior
        var actual = sut.handleSendConfirmation(engagement, event, context.copy(now = engagement.eventDate.minus(5, ChronoUnit.DAYS)))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        // send message and set checkpoint to 1 day prior
        actual = sut.handleSendConfirmation(actual, event, context.copy(now = engagement.eventDate.minus(3, ChronoUnit.DAYS)))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(2)

        // send message and set checkpoint to day of
        actual = sut.handleSendConfirmation(actual, event, context.copy(now = engagement.eventDate.minus(1, ChronoUnit.DAYS)))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(3)

        // don't send message and transition to declined, reason no answer
        actual = sut.handleSendConfirmation(actual, event, context)
        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.confirmationAttempts).isEqualTo(3)

        verifySequence {
            messageHandler.send(any(), any(), any(), any(), any(), rules = any())
            messageHandler.send(any(), any(), any(), any(), any(), rules = any())
            messageHandler.send(any(), any(), any(), any(), any(), rules = any())
            responseHandler.recordConfirmation(any(), ConfirmationStatus.NO_RESPONSE, any(), true, null)
        }
    }


    @Test
    fun `handleSendReminder should try to notify twice and then transition to AWAIT_COMPLETED`() {
        val engagement = createEngagement(
            initialState = EngagementStatus.REMIND
        )
        val context = createContext(
            rules = mapOf(EngagementWorkflow.REMINDER to customer.engagementRules.elementAt(0))
        )
        val event = CheckpointEvent(
            engagement, customer
        )

        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        // send message and set checkpoint to 3 days prior
        var actual = sut.handleSendReminder(engagement, event, context)
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.reminderAttempts).isEqualTo(1)

        // send message and set checkpoint to 1 day prior then transition to AWAIT_COMPLETED
        actual = sut.handleSendReminder(actual, event, context)
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.reminderAttempts).isEqualTo(2)



        verifySequence {
            messageHandler.send(any(), any(), any(), any(), any(), rules = any())
            messageHandler.send(any(), any(), any(), any(), any(), rules = any())
        }
    }

    @Test
    fun `sendEvent should record an error response no rules apply`() {
        val engagement = createEngagement()

        val actual = sut.sendEvent(CheckpointEvent(engagement, customer.copy(engagementRules = mutableSetOf())))

        assertThat(actual.status).isEqualTo(EngagementStatus.ERROR)

        verifySequence {
            responseHandler.recordError(any(), *anyVararg())
        }
    }

    @Test
    fun `sendEvent should record a confirmation and transition to AWAIT_COMPLETED if no reminders configured`() {
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM)

        val actual = sut.sendEvent(
            ConfirmationResponseEvent(
                event = EngagementEvent.CONFIRMED,
                engagement = engagement,
                customer = customer,
                result = ConfirmationStatus.CONFIRMED,
                respondents = setOf(
                    ContactResource(
                        id = "test",
                        familyName = "Test",
                        givenName = "Test",
                        contactMethod = ContactMethod.SMS,
                        phone = "+14405551234",
                        language = Language.ENGLISH,
                        email = ""
                    )
                ),
                messageId = "test"
            )
        )

        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.nextCheckpoint).isEqualTo(actual.eventDate)

        verifySequence {
            responseHandler.recordConfirmation(any(), ConfirmationStatus.CONFIRMED, any(), false, "test")
        }
    }

    @Test
    fun `sendEvent should record a confirmation and transition to REMIND if reminders configured`() {
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM)

        val actual = sut.sendEvent(
            ConfirmationResponseEvent(
                event = EngagementEvent.CONFIRMED,
                engagement = engagement,
                customer = customerWithReminders,
                result = ConfirmationStatus.CONFIRMED,
                respondents = setOf(
                    ContactResource(
                        id = "test",
                        familyName = "Test",
                        givenName = "Test",
                        contactMethod = ContactMethod.SMS,
                        phone = "+14405551234",
                        language = Language.ENGLISH,
                        email = ""
                    )
                ),
                messageId = "test"
            )
        )

        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.nextCheckpoint).isEqualTo(
            actual.eventDate.minus(3, ChronoUnit.DAYS).plusSeconds(1)
        )

        verifySequence {
            responseHandler.recordConfirmation(any(), ConfirmationStatus.CONFIRMED, any(), false, "test")
        }
    }

    @Test
    fun `sendEvent should record DECLINED and stop if cancellations are not configured`() {
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM)

        val actual = sut.sendEvent(
            ConfirmationResponseEvent(
                event = EngagementEvent.DECLINED,
                engagement = engagement,
                customer = customer,
                result = ConfirmationStatus.DECLINED,
                respondents = setOf(
                    ContactResource(
                        id = "test",
                        familyName = "Test",
                        givenName = "Test",
                        contactMethod = ContactMethod.SMS,
                        phone = "+14405551234",
                        language = Language.ENGLISH,
                        email = ""
                    )
                ),
                messageId = "Test"
            )
        )

        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)

        verifySequence {
            responseHandler.recordConfirmation(any(), ConfirmationStatus.DECLINED, any(), true, "Test")
        }
    }

    @Test
    fun `sendEvent should record DECLINED and leave engagement open if cancellations are configured`() {
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM)

        val actual = sut.sendEvent(
            ConfirmationResponseEvent(
                event = EngagementEvent.DECLINED,
                engagement = engagement,
                customer = customerWithReminders,
                result = ConfirmationStatus.DECLINED,
                respondents = setOf(
                    ContactResource(
                        id = "test",
                        familyName = "Test",
                        givenName = "Test",
                        contactMethod = ContactMethod.SMS,
                        phone = "+14405551234",
                        language = Language.ENGLISH,
                        email = ""
                    )
                ),
                messageId = "Test"
            )
        )

        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)

        verifySequence {
            responseHandler.recordConfirmation(any(), ConfirmationStatus.DECLINED, any(), false, "Test")
        }
    }

    @Test
    fun `handleSendMessage should record contact unreachable responses`() {
        val engagement = createEngagement()
        val context = createContext()

        every { messageHandler.send(engagement, any(), context.customer, rules = any()) } answers {
            EngagementMessageResult(
                success = false,
                contactErrors = listOf(
                    EngagementMessageResult.ContactErrors(
                        id = "1",
                        errors = listOf(
                            ErrorDto(
                                path = "",
                                message = "test",
                                errorCode = Errors.INVALID_EMAIL_OR_PHONE.code
                            )
                        )
                    )
                )
            )
        }

        sut.handleSendMessage(engagement, EngagementWorkflow.CONFIRMATION, context)

        verifySequence {
            messageHandler.send(engagement, any(), context.customer, any(), any(), any(), rules = any())
            responseHandler.recordContactUnreachable(engagement, EngagementWorkflow.CONFIRMATION, any())
        }

    }

    @Test
    fun `should transition through confirmation and reminder process`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(10, ChronoUnit.DAYS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to 5 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CONFIRM, send message and set checkpoint to 3 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        // respondent confirms 3 days before
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(3, ChronoUnit.DAYS)))

        // CONFIRM -> CONFIRMED -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // reminder #1
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.reminderAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // reminder #2 then REMIND -> AWAIT_COMPLETED
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.reminderAttempts).isEqualTo(2)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // AWAIT_COMPLETE -> COMPLETED at eventDate
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    @Test
    fun `should cancel after confirming`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(10, ChronoUnit.DAYS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to 5 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CONFIRM, send message and set checkpoint to 3 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        // respondent confirms 3 days before
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(3, ChronoUnit.DAYS)))

        // CONFIRM -> CONFIRMED -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // reminder #1
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.reminderAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.DECLINED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.DECLINED
        ), context.copy(now = start.minus(1, ChronoUnit.DAYS)))

        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.DECLINED)
    }

    @Test
    fun `should cancel while AWAIT_COMPLETED and waiting to transition to COMPLETED`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(10, ChronoUnit.DAYS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to 5 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CONFIRM, send message and set checkpoint to 3 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        // respondent confirms 3 days before
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(3, ChronoUnit.DAYS)))

        // CONFIRM -> CONFIRMED -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // reminder #1
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.reminderAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.DECLINED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.DECLINED
        ), context.copy(now = start.minus(1, ChronoUnit.DAYS)))

        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.DECLINED)
        assertThat(actual.contactDeclined).isTrue
    }

    @Test
    fun `should transition through confirmation and reminder process in under 24 hours`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and checkpoint now (to immediately send the confirmation)
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // respondent confirms 1 hour before cutoff (cutoff is 12 hours prior)
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(13, ChronoUnit.HOURS)))

        // CONFIRM -> CONFIRMED -> REMIND -> REMINDED -> AWAIT_COMPLETED (do not send reminder if confirmed <24 hours from appt)
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.reminderAttempts).isEqualTo(0)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // AWAIT_COMPLETE -> COMPLETED at engagement event date
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    @Test
    fun `should transition through reminder process in under 24 hours`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithRemindersOnly), context)

        // INITIAL -> CONFIRM -> REMIND -> REMINDED -> AWAIT_COMPLETED immediately send the confirmation
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.reminderAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithRemindersOnly), context.copy(now = actual.nextCheckpoint))

        // AWAIT_COMPLETED -> COMPLETED
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    @Test
    fun `should transition from CONFIRM to DECLINED after 3 unanswered attempts`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(10, ChronoUnit.DAYS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to 5 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CONFIRM, send message and set checkpoint to 3 days prior
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // confirmation #2
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(2)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // confirmation #3
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(3)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> DECLINED
        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))
    }

    @Test
    fun `should immediately send confirmation if within 24 hours and cancel if no response by cutoff`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM send message and set checkpoint to cancellation threshold
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))
        assertThat(actual.confirmationAttempts).isEqualTo(1)

        actual = sut.sendEvent(CheckpointEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> DECLINED
        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.confirmationAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))
    }

    @Test
    fun `should transition to REMIND if engagement is already confirmed`() {
        val engagement = createEngagement(
            confirmationStatus = ConfirmationStatus.CONFIRMED
        )
        val context = createContext(
            now = Instant.now(),
            cust = customerWithReminders
        )

        val actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
    }

    @Test
    fun `should transition from CONFIRM on system confirmation`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM, eventDate = start)
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customer
        )

        val actual = sut.sendEvent(ConfirmationResponseEvent.confirmed(engagement, customer), context)

        // CONFIRM -> CONFIRMED -> REMIND -> REMINDERS_SENT -> AWAIT_COMPLETED
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.confirmationAttempts).isEqualTo(0)
        assertThat(actual.contactDeclined).isFalse
    }

    @Test
    fun `initial should skip REMINDER if confirmationStatus is DECLINED`() {
        val engagement = createEngagement(
            confirmationStatus = ConfirmationStatus.DECLINED
        )
        val context = createContext(
            now = Instant.now(),
            cust = customerWithRemindersOnly
        )

        val actual = sut.sendEvent(CheckpointEvent(engagement, customerWithRemindersOnly), context)

        // INITIAL -> ERROR
        assertThat(actual.status).isEqualTo(EngagementStatus.ERROR)
    }

    @Test
    fun `should handle cancellation event while confirming`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to now
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))

        actual = sut.sendEvent(CancellationEvent(actual, customerWithReminders), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CANCEL -> CANCELLED
        assertThat(actual.status).isEqualTo(EngagementStatus.CANCELLED)
        assertThat(actual.contactDeclined).isFalse

        verify {
            messageHandler.send(any(), customerWithReminders.engagementRules.last(), customerWithReminders, any(), any(), rules = any())
        }
    }

    @Test
    fun `should handle cancellation event from declined state`() {
        val engagement = createEngagement(initialState = EngagementStatus.CONFIRM)

        var actual = sut.sendEvent(
            ConfirmationResponseEvent(
                EngagementEvent.DECLINED,
                engagement,
                customerWithReminders,
                ConfirmationStatus.DECLINED
            )
        )

        // CONFIRM -> DECLINED
        assertThat(actual.status).isEqualTo(EngagementStatus.DECLINED)
        assertThat(actual.contactDeclined).isTrue

        actual = sut.sendEvent(CancellationEvent(actual, customerWithReminders))

        // DECLINED -> CANCELLED
        assertThat(actual.status).isEqualTo(EngagementStatus.CANCELLED)

        verify {
            messageHandler.send(any(), customerWithReminders.engagementRules.last(), customerWithReminders, any(), any(), rules = any())
        }
    }

    @Test
    fun `should handle cancellation event while awaiting completed`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(23, ChronoUnit.HOURS),
            cust = customerWithReminders
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithReminders), context)

        // INITIAL -> CONFIRM and set checkpoint to now
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(12, ChronoUnit.HOURS))

        actual = sut.sendEvent(ConfirmationResponseEvent(
            engagement = actual,
            customer = customerWithReminders,
            result = ConfirmationStatus.CONFIRMED,
            event = EngagementEvent.CONFIRMED
        ), context)

        // CONFIRM -> AWAITING_COMPLETED
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)

        actual = sut.sendEvent(CancellationEvent(actual, customerWithReminders), context)

        // AWAITING_COMPLETED -> CANCELLED
        assertThat(actual.status).isEqualTo(EngagementStatus.CANCELLED)
        assertThat(actual.contactDeclined).isFalse

        verify {
            messageHandler.send(any(), customerWithReminders.engagementRules.last(), customerWithReminders, any(), any(), rules = any())
        }
    }

    @Test
    fun `should handle cancellation event while reminding`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(25, ChronoUnit.HOURS),
            cust = customerWithRemindersOnly
        )

        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithRemindersOnly), context)

        // INITIAL -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(24, ChronoUnit.HOURS).plusSeconds(1))

        actual = sut.sendEvent(CancellationEvent(actual, customerWithRemindersOnly), context.copy(now = actual.nextCheckpoint))

        // CONFIRM -> CANCEL -> CANCELLED
        assertThat(actual.status).isEqualTo(EngagementStatus.CANCELLED)
        assertThat(actual.contactDeclined).isFalse

        verify {
            messageHandler.send(any(), customerWithRemindersOnly.engagementRules.last(), customerWithRemindersOnly, any(), any(), rules = any())
        }
    }

    @Test
    fun `should handle check in workflow 7 days out happy path`() {
        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(7, ChronoUnit.DAYS),
            cust = customerWithAllWorkflows
        )

        // initial should immediately transition to confirm
        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithAllWorkflows), context)
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.bookingAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        // confirm should send confirm message and transition to check in, immediately sending a check in message
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // user confirms 30 seconds after receiving confirmation
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithAllWorkflows,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(5, ChronoUnit.DAYS).plusSeconds(31)))

        // transitions from CONFIRM -> CONFIRMED -> CHECK_IN, sends check in message immediately
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // user checks in 1 hour after receiving the first appointment check in message
        actual = sut.sendEvent(CheckInResponseEvent(
            engagement = actual,
            customer = customerWithAllWorkflows,
            appointments = engagement.resources.filterIsInstance<AppointmentResource>().filter { it.id == "1" }.toSet()
        ), context.copy(now = start.minus(5, ChronoUnit.DAYS).plus(1, ChronoUnit.HOURS)))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.CHECKED_IN, "2" to CheckInStatus.NOT_CHECKED_IN))
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // and then they immediately check in the other, completing check-in
        actual = sut.sendEvent(CheckInResponseEvent(
            engagement = actual,
            customer = customerWithAllWorkflows,
            appointments = engagement.resources.filterIsInstance<AppointmentResource>().filter { it.id == "2" }.toSet()
        ), context.copy(now = start.minus(5, ChronoUnit.DAYS).plus(1, ChronoUnit.HOURS).plus(10, ChronoUnit.MINUTES)))

        // transitions from CHECK_IN -> CHECKED_IN -> REMIND
        assertThat(actual.status).isEqualTo(EngagementStatus.REMIND)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.CHECKED_IN, "2" to CheckInStatus.CHECKED_IN))
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // flow from remind onward omitted for brevity
    }

    @Test
    fun `should handle check in workflow 7 days out but user does not check in until last message`() {
        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(7, ChronoUnit.DAYS),
            cust = customerWithAllWorkflows
        )

        // initial should immediately transition to book and then confirm
        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithAllWorkflows), context)
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.bookingAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))

        // confirm should send confirm message and transition to check in, immediately sending a check in message
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // user confirms 30 seconds after receiving confirmation
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithAllWorkflows,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(5, ChronoUnit.DAYS).plusSeconds(31)))

        // transitions from CONFIRM -> CONFIRMED -> CHECK_IN, sends check in message immediately
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // no response 3 days out, so another check in message is sent
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.NOT_CHECKED_IN, "2" to CheckInStatus.NOT_CHECKED_IN))
        assertThat(actual.checkInAttempts).isEqualTo(2)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        // no response 1 day out, so last check in message is sent
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.NOT_CHECKED_IN, "2" to CheckInStatus.NOT_CHECKED_IN))
        assertThat(actual.checkInAttempts).isEqualTo(3)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        // user responds to the first one 30 seconds after receiving the final check in message.
        actual = sut.sendEvent(CheckInResponseEvent(
            engagement = actual,
            customer = customerWithAllWorkflows,
            appointments = actual.resources.filterIsInstance<AppointmentResource>().filter { it.id == "1" }.toSet()
        ), context.copy(now = start.minus(1, ChronoUnit.DAYS).plus(31, ChronoUnit.SECONDS)))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsEntry("1", CheckInStatus.CHECKED_IN)

       // user checks in the last one. note that it transitions through reminding and ends at await completed
        actual = sut.sendEvent(CheckInResponseEvent(
            engagement = actual,
            customer = customerWithAllWorkflows,
            appointments = actual.resources.filterIsInstance<AppointmentResource>().filter { it.id == "2" }.toSet()
        ), context.copy(now = start.minus(1, ChronoUnit.DAYS).plus(5, ChronoUnit.MINUTES)))
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.CHECKED_IN, "2" to CheckInStatus.CHECKED_IN))
        assertThat(actual.nextCheckpoint).isEqualTo(start)
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)

        // last checkpoint should wake up at the appointment start time and mark it as completed
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    @Test
    fun `should handle check in workflow 7 days out but user never checks in`() {
        every { messageHandler.send(any(), any(), any(), any(), any(), rules = any()) } answers {
            EngagementMessageResult(success = true)
        }

        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(7, ChronoUnit.DAYS),
            cust = customerWithAllWorkflows
        )

        // initial should immediately transition to book
        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithAllWorkflows), context)
        assertThat(actual.bookingAttempts).isEqualTo(1)
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(5, ChronoUnit.DAYS).plusSeconds(1))
        assertThat(actual.confirmationAttempts).isEqualTo(0)

        // confirm should send confirm message and transition to check in, immediately sending a check in message
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CONFIRM)
        assertThat(actual.confirmationAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // user confirms 30 seconds after receiving confirmation
        actual = sut.sendEvent(ConfirmationResponseEvent(
            event = EngagementEvent.CONFIRMED,
            engagement = actual,
            customer = customerWithAllWorkflows,
            result = ConfirmationStatus.CONFIRMED
        ), context.copy(now = start.minus(5, ChronoUnit.DAYS).plusSeconds(31)))

        // transitions from CONFIRM -> CONFIRMED -> CHECK_IN, sends check in message immediately
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(3, ChronoUnit.DAYS).plusSeconds(1))

        // no response 3 days out, so another check in message is sent
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.NOT_CHECKED_IN, "2" to CheckInStatus.NOT_CHECKED_IN))
        assertThat(actual.checkInAttempts).isEqualTo(2)
        assertThat(actual.nextCheckpoint).isEqualTo(start.minus(1, ChronoUnit.DAYS).plusSeconds(1))

        // no response 1 day out, so last check in message is sent
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.CHECK_IN)
        assertThat(getMappedAppointmentCheckInStatuses(actual)).containsAllEntriesOf(mapOf("1" to CheckInStatus.NOT_CHECKED_IN, "2" to CheckInStatus.NOT_CHECKED_IN))
        assertThat(actual.checkInAttempts).isEqualTo(3)
        assertThat(actual.nextCheckpoint).isEqualTo(start)

        // user never responds, so go to await_completed
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)

        // completed
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    @Test
    fun `should handle check in workflow on same day`() {
        val start = ZonedDateTime.of(2023, 3, 24, 9, 0, 0, 0, ZoneId.of("US/Eastern")).toInstant()
        val engagement = createEngagement(
            eventDate = start
        )
        val context = createContext(
            now = start.minus(3, ChronoUnit.HOURS),
            cust = customerWithAllWorkflows
        )

        // when starting at initial, fast forward to check in if "now" is the same day as the event as well as
        // Detecting that check-in is too close to start date and transition to REMIND -> AWAIT_COMPLETED
        var actual = sut.sendEvent(CheckpointEvent(engagement, customerWithAllWorkflows), context)
        assertThat(actual.status).isEqualTo(EngagementStatus.AWAIT_COMPLETED)
        assertThat(actual.checkInAttempts).isEqualTo(1)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(actual.nextCheckpoint).isEqualTo(actual.eventDate)
        assertThat(getMappedAppointmentCheckInStatuses(engagement)).containsAllEntriesOf(mapOf(
            "1" to CheckInStatus.NOT_CHECKED_IN,
            "2" to CheckInStatus.NOT_CHECKED_IN
        ))

        // one last checkpoint will close it out
        actual = sut.sendEvent(CheckpointEvent(actual, customerWithAllWorkflows), context.copy(now = actual.nextCheckpoint))
        assertThat(actual.status).isEqualTo(EngagementStatus.COMPLETED)
    }

    private fun createEngagement(
        eventDate: Instant = Instant.now().plus(10, ChronoUnit.DAYS),
        initialState: EngagementStatus = EngagementStatus.INITIAL,
        confirmationStatus: ConfirmationStatus = ConfirmationStatus.UNCONFIRMED
    ): Engagement {
        return Engagement(
            id = "test",
            customerId = "test",
            status = initialState,
            confirmationStatus = confirmationStatus,
            eventDate = eventDate,
            nextCheckpoint = Instant.now(),
            resources = mutableSetOf(
                AppointmentResource(
                    id = "1",
                    appointmentType = "test",
                    patient = "test",
                    staff = "test",
                    practice = "test",
                    startTime = eventDate,
                    location = "test",
                    reason = "test"
                ),
                AppointmentResource(
                    id = "2",
                    appointmentType = "test",
                    patient = "test2",
                    staff = "test",
                    practice = "test",
                    startTime = eventDate,
                    location = "test",
                    reason = "test"
                ),
                LocationResource(
                    id = "test",
                    name = "test",
                    zoneId = "US/Eastern",
                    zipCode = "44108",
                    practiceId = "test"
                ),
                ContactResource(
                    id = "contact1",
                    familyName = "Contact",
                    givenName = "First",
                    phone = "+14405551234",
                    email = null,
                    contactMethod = ContactMethod.SMS,
                    language = Language.ENGLISH,
                ),
                ContactResource(
                    id = "contact2",
                    familyName = "Contact",
                    givenName = "Second",
                    phone = "+14405551234",
                    email = null,
                    contactMethod = ContactMethod.VOICE,
                    language = Language.ENGLISH,
                ),
                ContactResource(
                    id = "contact3",
                    familyName = "Contact",
                    givenName = "Third",
                    email = "<EMAIL>",
                    phone = null,
                    contactMethod = ContactMethod.EMAIL,
                    language = Language.ENGLISH,
                )
            )
        )
    }

    private fun createContext(
        rules: Map<EngagementWorkflow, EngagementRule> = mapOf(
            EngagementWorkflow.CONFIRMATION to customer.engagementRules.elementAt(
                0
            )
        ),
        cust: Customer = customer,
        now: Instant? = null
    ): EngagementContext {
        return EngagementContext(
            customer = cust,
            rules = rules,
            now = now ?: Instant.now()
        )
    }

    @Test
    fun `unconfirmed appointment should go to check-in when check-in is enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)

        val engagement = createEngagement(eventDate = eventDate, confirmationStatus = ConfirmationStatus.UNCONFIRMED)
        val context = createContext(
            rules = mapOf(
                EngagementWorkflow.CHECKIN to EngagementRule(
                    id = "checkin-rule",
                    workflow = EngagementWorkflow.CHECKIN,
                    templateId = "test"
                ),
                EngagementWorkflow.REMINDER to EngagementRule(
                    id = "reminder-rule",
                    workflow = EngagementWorkflow.REMINDER,
                    templateId = "test"
                )
            ),
            now = now
        )

        // When
        val result = sut.handleCheckInitial(engagement, TransitionEvent(EngagementEvent.CHECKPOINT), context)

        // Then
        assertThat(result.status).isEqualTo(EngagementStatus.CHECK_IN)
    }

    @Test
    fun `unconfirmed appointment should go to reminder when check-in is not enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)

        val engagement = createEngagement(eventDate = eventDate, confirmationStatus = ConfirmationStatus.UNCONFIRMED)
        val context = createContext(
            rules = mapOf(
                EngagementWorkflow.REMINDER to EngagementRule(
                    id = "reminder-rule",
                    workflow = EngagementWorkflow.REMINDER,
                    templateId = "test"
                )
            ),
            now = now
        )

        // When
        val result = sut.handleCheckInitial(engagement, TransitionEvent(EngagementEvent.CHECKPOINT), context)

        // Then
        assertThat(result.status).isEqualTo(EngagementStatus.REMIND)
    }

    @Test
    fun `confirmed appointment should still go to check-in when check-in is enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)

        val engagement = createEngagement(eventDate = eventDate, confirmationStatus = ConfirmationStatus.CONFIRMED)
        val context = createContext(
            rules = mapOf(
                EngagementWorkflow.CHECKIN to EngagementRule(
                    id = "checkin-rule",
                    workflow = EngagementWorkflow.CHECKIN,
                    templateId = "test"
                ),
                EngagementWorkflow.REMINDER to EngagementRule(
                    id = "reminder-rule",
                    workflow = EngagementWorkflow.REMINDER,
                    templateId = "test"
                )
            ),
            now = now
        )

        // When
        val result = sut.handleCheckInitial(engagement, TransitionEvent(EngagementEvent.CHECKPOINT), context)

        // Then
        assertThat(result.status).isEqualTo(EngagementStatus.CHECK_IN)
    }

    @Test
    fun `unconfirmed appointment after booking should go to check-in when check-in is enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)

        val engagement = createEngagement(eventDate = eventDate, confirmationStatus = ConfirmationStatus.UNCONFIRMED)
        val context = createContext(
            rules = mapOf(
                EngagementWorkflow.CHECKIN to EngagementRule(
                    id = "checkin-rule",
                    workflow = EngagementWorkflow.CHECKIN,
                    templateId = "test"
                ),
                EngagementWorkflow.REMINDER to EngagementRule(
                    id = "reminder-rule",
                    workflow = EngagementWorkflow.REMINDER,
                    templateId = "test"
                )
            ),
            now = now
        )

        // When
        val result = sut.handleBookingSent(engagement, TransitionEvent(EngagementEvent.BOOKING_SENT), context)

        // Then
        assertThat(result.status).isEqualTo(EngagementStatus.CHECK_IN)
    }

    companion object {
        private fun getMappedAppointmentCheckInStatuses(engagement: Engagement): Map<String, CheckInStatus> {
            return engagement.resources
                .filterIsInstance<AppointmentResource>()
                .associateBy({it.id}, {it.checkInStatus})
        }
    }
}