package com.connexin.pmx.server

import com.connexin.pmx.server.utils.CacheKeys
import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Ticker
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.cache.CacheManager
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.support.SimpleCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset
import java.util.concurrent.TimeUnit

@TestConfiguration
@Profile("test")
class TestConfig {

    @Primary
    @Bean
    fun clock(): Clock {
        return Clock.fixed(Instant.parse("2021-12-31T12:31:00Z"), ZoneOffset.UTC)
    }

    @Bean
    fun cacheManager(ticker: Ticker): CacheManager {
        val webhooksCache = CaffeineCache(
            CacheKeys.webhooks, Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .maximumSize(10)
                .ticker(ticker)
                .build()
        )
        val manager = SimpleCacheManager()
        manager.setCaches(listOf(webhooksCache))
        return manager
    }

    @Bean
    fun ticker(): Ticker {
        return Ticker.systemTicker()
    }
}