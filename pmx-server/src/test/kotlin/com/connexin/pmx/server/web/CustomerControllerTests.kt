package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.dtos.PatchAdminCustomer
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.models.dtos.SelfManagedCustomer
import com.connexin.pmx.server.services.CustomerService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Duration
import java.time.Instant
import java.time.LocalTime

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(CustomerController::class)
@WithMockUser(roles = ["user"])
class CustomerControllerTests {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var customerService: CustomerService

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        customer = Customer(
            id = "1234",
            name = "Test",
            status = CustomerStatus.ENABLED,
            engagementRules = mutableSetOf(
                EngagementRule(
                    id = "1",
                    enabled = true,
                    workflow = EngagementWorkflow.CONFIRMATION,
                    templateOverrides = mutableMapOf(),
                    templateIds = mutableMapOf()
                )
            )
        )

        every { customerService.getById(customer.id!!, true) } returns customer
        every { customerService.getById("1", true) } returns null
    }

    @Test
    fun `getEngagementRules should return 200 with list of rules`() {
        mockMvc.perform(
            get(RULES_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(customer.engagementRules)))
    }

    @Test
    fun `createEngagementRule should return 404 if customer not found`() {
        mockMvc.perform(
            post(RULES_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        EngagementRule(
                            id = "1",
                            enabled = true,
                            workflow = EngagementWorkflow.CONFIRMATION,
                            templateOverrides = mutableMapOf(),
                            templateIds = mutableMapOf()
                        )
                    )
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `createEngagementRule should return 409 rule with ID already exists`() {
        every { customerService.createEngagementRule(customer, any()) } returns Response.failure(status = HttpStatus.CONFLICT)
        mockMvc.perform(
            post(RULES_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        EngagementRule(
                            id = "1",
                            enabled = true,
                            workflow = EngagementWorkflow.CONFIRMATION,
                            templateOverrides = mutableMapOf(),
                            templateIds = mutableMapOf()
                        )
                    )
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.CONFLICT.value()))
    }

    @Test
    fun `createEngagementRule should create rule`() {
        val expected = EngagementRule(
            id = "2",
            enabled = true,
            workflow = EngagementWorkflow.CONFIRMATION,
            templateOverrides = mutableMapOf(),
            templateIds = mutableMapOf()
        )
        every { customerService.createEngagementRule(customer, any()) } answers {
            Response.success(secondArg())
        }
        mockMvc.perform(
            post(RULES_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(expected)
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }

    @Test
    fun `saveEngagementRule should return 404 if customer not found`() {
        mockMvc.perform(
            put(RULE_URL)
                .accept(MediaType.APPLICATION_JSON)
                .header(X_OPMED, "1")
                .content(
                    mapper.writeValueAsString(
                        EngagementRule(
                            id = "1",
                            enabled = true,
                            workflow = EngagementWorkflow.CONFIRMATION,
                            templateOverrides = mutableMapOf(),
                            templateIds = mutableMapOf()
                        )
                    )
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `saveEngagementRule should return 400 if engagement rule is poorly formed`() {
        every { customerService.saveEngagementRule(customer, "1", any()) } returns Response.failure(status = HttpStatus.BAD_REQUEST)
        mockMvc.perform(
            put(RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        EngagementRule(
                            id = "1",
                            enabled = true,
                            workflow = EngagementWorkflow.CONFIRMATION,
                            templateOverrides = mutableMapOf(),
                            templateIds = mutableMapOf()
                        )
                    )
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.BAD_REQUEST.value()))
    }

    @Test
    fun `saveEngagementRule should return 200 if engagement rule is saved`() {
        val expected = EngagementRule(
            id = "1",
            enabled = true,
            workflow = EngagementWorkflow.CONFIRMATION,
            templateOverrides = mutableMapOf(),
            templateIds = mutableMapOf()
        )
        every { customerService.saveEngagementRule(customer, "1", any()) } answers {
            Response.success(thirdArg())
        }
        mockMvc.perform(
            put(RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(expected)
                )
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }

    @Test
    fun `getEngagementRule should return 404 if customer not found`() {
        mockMvc.perform(
            get(RULE_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getEngagementRule should return 404 if engagement rule not found`() {

        mockMvc.perform(
            get(BAD_RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `getEngagementRule should return 200`() {
        mockMvc.perform(
            get(RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(customer.engagementRules.first())))
    }

    @Test
    fun `deleteEngagementRule should return 404 if customer not found`() {
        mockMvc.perform(
            delete(RULE_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `deleteEngagementRule should return 404 if engagement rule not found`() {
        every { customerService.deleteEngagementRule(customer, "2") } returns Response.failure(status = HttpStatus.NOT_FOUND)
        mockMvc.perform(
            delete(BAD_RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `deleteEngagementRule should return 204`() {
        every { customerService.deleteEngagementRule(customer, "1") } returns Response.success(customer.engagementRules.first())
        mockMvc.perform(
            delete(RULE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
        )
            .andExpect(status().`is`(HttpStatus.NO_CONTENT.value()))
    }

    @Test
    fun `patch should update customer and return 200`() {
        val expected = Customer(
            id = "1234",
            name = "test - edit",
            createdAt = Instant.now(),
            updatedAt = Instant.now(),
            status = CustomerStatus.DISABLED,
            cancellationDeadline = Duration.ofSeconds(5555),
            deliveryStartTime = LocalTime.of(6, 0, 0),
            deliveryEndTime = LocalTime.of(12, 0, 0),
            deliveryDays = mutableSetOf(DeliveryDay.MONDAY),
            appointmentTimeDisplayOffset = Duration.ofSeconds(900)
        )
        every { customerService.patch(customer, any<PatchAdminCustomer>(), any()) } returns expected
        mockMvc.perform(
            patch(CUSTOMER_URL)
                .accept(MediaType.APPLICATION_JSON)
                .header(X_OPMED, customer.id)
                .contentType("application/json-patch+json")
                .content("[ " +
                        "{\"op\": \"replace\", \"path\": \"/cancellationDeadline\", \"value\": \"5555.0\" }," +
                        "{\"op\": \"replace\", \"path\": \"/deliveryDays\", \"value\": [\"MONDAY\"] }," +
                        "{\"op\": \"replace\", \"path\": \"/deliveryStartTime\", \"value\": \"06:00\" }," +
                        "{\"op\": \"replace\", \"path\": \"/deliveryEndTime\", \"value\": \"12:00\" }," +
                        "{\"op\": \"replace\", \"path\": \"/appointmentTimeDisplayOffset\", \"value\": \"900.0\" }" +
                        "]")
        )
            .andExpect(status().isOk)
            .andExpect(content().json(mapper.writeValueAsString(SelfManagedCustomer.from(expected))))
    }


    companion object {
        const val RULES_URL = "/api/v2/customer/engagement-rules"
        const val RULE_URL = "/api/v2/customer/engagement-rules/1"
        const val BAD_RULE_URL = "/api/v2/customer/engagement-rules/2"
        const val CUSTOMER_URL = "/api/v2/customer"
    }
}