package com.connexin.pmx.server.data

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.VTDocument
import com.connexin.pmx.server.models.bli.VTMessage
import com.connexin.pmx.server.models.bli.VTMessages
import com.connexin.pmx.server.utils.InstantUtil
import java.time.Instant
import java.time.temporal.ChronoUnit

class VTTestData() {
    companion object {
        val vtTestMessage = """
            UserName=96267&UserPassword=8993&XMLPost=
            <Orders>
            <Order Type="VT">
            <Phone>17105455226</Phone>
            <CallerID/>
            <StopTime>21:00</StopTime>
            <RestartTime>09:00</RestartTime>
            <Documents>
            <Document>
            <DocumentType>Live</DocumentType>
            <DocumentID/>
            <DocumentName>live.txt</DocumentName>
            <DocumentBinary>VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</DocumentBinary>
            </Document>
            <Document>
            <DocumentType>Message</DocumentType>
            <DocumentID/>
            <DocumentName>message.txt</DocumentName>
            <DocumentBinary>********************************************************************************************************************************************************************************************************************************************************************************************************************************************************</DocumentBinary>
            </Document>
            </Documents>
            <HotOne>STOP</HotOne>
            <HotTwo/>
            <HotThree/>
            <HotFour/>
            <HotFive/>
            <HotSix/>
            <HotSeven/>
            <HotEight/>
            <HotNine/>
            <HotZero/>
            <HotStar/>
            <HotPound/>
            </Order></Orders>&PostWay=sync"""
    }
}

fun testVTMessages(): VTMessages {

    val sampleMessage = testVTMessage1()
    val vtMessages = VTMessages(sampleMessage)
    return vtMessages

}

fun testVTMessage1(): VTMessage {

    val sampleDocuments: ArrayList<VTDocument> = ArrayList<VTDocument>()
    sampleDocuments.add(testVTDocument1())
    sampleDocuments.add(testVTDocument2())

    val testVTMessage = VTMessage (
        type = "VT",
        phoneNumber = "2125578877",
        callerID = "2184547979",
        stopTime = "21:00",
        restartTime = "09:00",
        vtDocuments = sampleDocuments,
        hotOne = "STOP",
        hotTwo = "TEST2",
        hotThree = "TEST3",
        hotFour = "TEST4",
        hotFive = "TEST5",
        hotSix = "TEST6",
        hotSeven = "TEST7",
        hotEight = "TEST8",
        hotNine = "TEST9",
        hotZero = "TEST0",
        hotStar = "TESTSTAR",
        hotPound = "TESTPOUND")

    return testVTMessage
}

fun testVTDocument1(): VTDocument {

    val testDocument = VTDocument (
         documentID = "11",
         documentType = "Live",
         documentName = "live.txt",
         documentBinary = "VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh")

    return testDocument
}

fun testVTDocument2(): VTDocument {

    val testDocument = VTDocument (
        documentID = "22",
        documentType = "Message",
        documentName = "message.txt",
        documentBinary = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

    return testDocument
}

// test pmx message for testing VT Reports
fun testPMXMessageForVoiceReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "100",
        customerId = "10",
        type = MessageType.VOICE,
        status = MessageStatus.DELIVERED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 1,
        createdAt = InstantUtil.toInstant("10/20/2020 09:15:17 AM"),
        updatedAt = InstantUtil.toInstant("10/20/2020 09:17:05 AM"),
        completedAt = InstantUtil.toInstant("10/20/2020 09:19:23 AM"),
        to = "***********",
        from = null,
        subject = null,
        message = "VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh",
        altMessage = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        remoteId = "59akd871-864d-4d32-8f1d-7a07d2ef449a",
        errors = null,
        replyTo = "***********",
        responseData = "1",
        confirmationStatus = ConfirmationStatus.CONFIRMED,
        voiceDeliveryMethod = VoiceDeliveryMethod.VOICE_MAIL,
        emailRecipients = null  // used for email broadcast only
    )

    return testMessage
}

/*
======================================================================================
XML from OP:
      '<Orders><Order Type="' + BLI_MSG_TYPE_PHONECALL + '">' +
      '<Phone>' + CleanPhoneNumber(FieldByName('CONTACT_VALUE').AsString) + '</Phone>' +
      '<CallerID>' + CallerID + '</CallerID>' +
      '<StopTime>'+ stopTime + '</StopTime>' +
      '<RestartTime>'+ restartTime + '</RestartTime>' +
      '<Documents>' +
        '<Document>' +
          '<DocumentType>Live</DocumentType>' +
          '<DocumentID/>' +
          '<DocumentName>live.txt</DocumentName>' +
          '<DocumentBinary>' + String(DIMime.MimeEncodeStringNoCRLF(AnsiString(FieldByName('MESSAGE_DATA').AsString))) + '</DocumentBinary>' +
        '</Document>' +
        '<Document>' +
          '<DocumentType>Message</DocumentType>' +
          '<DocumentID/>' +
          '<DocumentName>message.txt</DocumentName>' +
          '<DocumentBinary>' + String(DIMime.MimeEncodeStringNoCRLF(AnsiString(FieldByName('MESSAGE_DATA_VM').AsString))) + '</DocumentBinary>' +
        '</Document>' +
      '</Documents>' +
      '<HotOne>STOP</HotOne>' +
      '<HotTwo>' + HotTwo + '</HotTwo>' +
      '<HotThree/><HotFour/><HotFive/><HotSix/><HotSeven/>' +
      '<HotEight/><HotNine/><HotZero/><HotStar/><HotPound/>' +
    '</Order></Orders>';
======================================================================================
Sample xml:
<Orders>
<Order Type="VT">
    <Phone>16115822027</Phone>
    <CallerID/>
    <StopTime>21:00</StopTime>
    <RestartTime>09:00</RestartTime>
    <Documents>
        <Document>
            <DocumentType>Live</DocumentType>
            <DocumentID/>
            <DocumentName>live.txt</DocumentName>
            <DocumentBinary>VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</DocumentBinary>
        </Document>
        <Document>
            <DocumentType>Message</DocumentType>
            <DocumentID/>
            <DocumentName>message.txt</DocumentName>
            <DocumentBinary>********************************************************************************************************************************************************************************************************************************************************************************************************************************************************</DocumentBinary>
        </Document>
    </Documents>
    <HotOne>STOP</HotOne>
    <HotTwo/>
    <HotThree/>
    <HotFour/>
    <HotFive/>
    <HotSix/>
    <HotSeven/>
    <HotEight/>
    <HotNine/>
    <HotZero/>
    <HotStar/>
    <HotPound/>
</Order>
</Orders>
======================================================================================
<PostAPIResponse>
	<SaveTransactionalOrderResult>
		<OrderID>73087671</OrderID>
		<transactionID>8233e3f4-1d7d-ec11-ac6c-000c29335097</transactionID>
	</SaveTransactionalOrderResult>
</PostAPIResponse>

<PostAPIResponse>
	<SaveTransactionalOrderResult>
		<Exception>Get the EB Detail Report Error. Details Info:
System.Net.WebException: The remote server returned an error: (500) Internal Server Error.
   at System.Net.HttpWebRequest.GetResponse()
   at PostAPI.DataClass.Report.GetEBDetailReportByOrderID(DB db, User user, Int32 OrderID, String reportKey)
        </Exception>
	</SaveTransactionalOrderResult>
</PostAPIResponse>

For broadcast emails, the transactionID is null and the OrderID is filled in.
For all other messages, the transactionID is filled in and the OrderID is null.
TransID - can be orderid or transactionid: expects 'BLI timeout' if timed out

*/
/*
  status = MessageStatus.SENT, //CaseUtils.toCamelCase(MessageStatus.SENT, true, ' '),    //"Sent",

  OP expects one of the following in the 'status' field:
  TRANS_STATUS_FAILED =         'Attempt, failed';
  TRANS_STATUS_CONFIRMED =      'Confirmed';
  TRANS_STATUS_SELF_CONFIRMED = 'Self-confirmed';
  TRANS_STATUS_SENT =           'Sent';
  TRANS_STATUS_LEFT_MSG =       'Left message';
  TRANS_STATUS_VOICEMAIL =      'Voice Mail';
  TRANS_STATUS_LIVE =           'Live';
  TRANS_STATUS_AWAITING =       'Awaiting confirmation';
  TRANS_STATUS_ATTEMPT =        'Attempting delivery';
  TRANS_STATUS_NO_REPORT_24 =   'No delivery report after 24 hours.';
  TRANS_STATUS_NO_REPORT_48 =   'No delivery report after 48 hours.';
  TRANS_STATUS_NOT_SENT_48 =    'Message did not leave queue after 48 hours.';
  TRANS_STATUS_TIMEOUT =        'Delivered w/o confirmation that message was sent due to vendor timeout error.';

*/
/*
Delivery Methods that OP expects:
'Attempt, failed'
'Confirmed'
'Self-confirmed'
'Sent'
'Left message'
'Voice Mail'
'Live'
'Awaiting confirmation'
'Attempting delivery'
'No delivery report after 24 hours.'
'No delivery report after 48 hours.'
'Message did not leave queue after 48 hours.'
'Delivered w/o confirmation that message was sent due to vendor timeout error.'
=====================================================================
XML Sample of VT Report:
<Report>
  <VT>
    <UNQID>58bed871-864d-4d32-8f1d-7a07d2ef449a</UNQID>
    <JobID>638086</JobID>
    <PhoneNumber>12679602762</PhoneNumber>
    <Duration>0</Duration>
    <Rate/>
    <Cost/>
    <Status>Sent</Status>
    <Error/>
    <DeliveryMethod>Live</DeliveryMethod>
    <DeliveryMethod>Voice Mail</DeliveryMethod>
    <KeyPress/>
    <Timestamp>5/25/2011 2:07:00 PM</Timestamp>
  </VT>
</Report>
*/