package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.services.EngagementMessageHandler
import com.connexin.pmx.server.services.EngagementResponseHandler
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringJUnitExtension
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals

@ExtendWith(SpringJUnitExtension::class)
class EngagementStateMachineUnconfirmedAppointmentTest {

    private lateinit var responseHandler: EngagementResponseHandler
    private lateinit var messageHandler: EngagementMessageHandler
    private lateinit var stateMachine: EngagementStateMachineImpl

    @BeforeEach
    fun setup() {
        responseHandler = mockk(relaxed = true)
        messageHandler = mockk(relaxed = true)
        stateMachine = EngagementStateMachineImpl(responseHandler, messageHandler)
    }

    @Test
    fun `unconfirmed appointment should go to check-in when check-in is enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)
        
        val engagement = createTestEngagement(eventDate, ConfirmationStatus.UNCONFIRMED)
        val customer = createTestCustomer()
        val context = createTestContext(customer, now, withCheckIn = true)

        // When
        val result = stateMachine.handleCheckInitial(engagement, TransitionEvent(EngagementEvent.CHECKPOINT), context)

        // Then
        assertEquals(EngagementStatus.CHECK_IN, result.status)
    }

    @Test
    fun `unconfirmed appointment should go to reminder when check-in is not enabled`() {
        // Given
        val now = Instant.now()
        val eventDate = now.plus(2, ChronoUnit.DAYS)
        
        val engagement = createTestEngagement(eventDate, ConfirmationStatus.UNCONFIRMED)
        val customer = createTestCustomer()
        val context = createTestContext(customer, now, withCheckIn = false)

        // When
        val result = stateMachine.handleCheckInitial(engagement, TransitionEvent(EngagementEvent.CHECKPOINT), context)

        // Then
        assertEquals(EngagementStatus.REMIND, result.status)
    }

    private fun createTestEngagement(eventDate: Instant, confirmationStatus: ConfirmationStatus): Engagement {
        return Engagement(
            id = "test-engagement",
            customerId = "test-customer",
            eventDate = eventDate,
            confirmationStatus = confirmationStatus,
            status = EngagementStatus.INITIAL,
            resources = mutableSetOf(
                LocationResource(id = "loc1", zoneId = "America/New_York"),
                AppointmentResource(id = "appt1", patient = "patient1", startTime = eventDate),
                ContactResource(id = "contact1", contactMethod = ContactMethod.EMAIL)
            )
        )
    }

    private fun createTestCustomer(): Customer {
        return Customer(
            id = "test-customer",
            status = CustomerStatus.ENABLED,
            name = "Test Customer",
            cancellationDeadline = Duration.ofHours(12)
        )
    }

    private fun createTestContext(customer: Customer, now: Instant, withCheckIn: Boolean): EngagementContext {
        val rules = mutableMapOf<EngagementWorkflow, EngagementRule>()
        
        rules[EngagementWorkflow.REMINDER] = EngagementRule(
            id = "reminder-rule",
            workflow = EngagementWorkflow.REMINDER
        )
        
        if (withCheckIn) {
            rules[EngagementWorkflow.CHECKIN] = EngagementRule(
                id = "checkin-rule",
                workflow = EngagementWorkflow.CHECKIN
            )
        }

        return EngagementContext(
            rules = rules,
            customer = customer,
            now = now
        )
    }
}
