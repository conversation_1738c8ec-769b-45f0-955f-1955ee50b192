package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.dtos.DeprovisionResult
import com.connexin.pmx.server.services.CustomerProvisioningService
import com.connexin.pmx.server.services.CustomerService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("test")
@SpringBootTest
@Import(TestConfig::class)
class CustomerV2ControllerTests {
    private lateinit var customerService: CustomerService
    private lateinit var customerProvisioningService: CustomerProvisioningService
    private lateinit var customerV2Controller: CustomerV2Controller

    @BeforeEach
    fun setUp() {
        customerService = mockk()
        customerProvisioningService = mockk()
        customerV2Controller = CustomerV2Controller(customerService, customerProvisioningService)
    }

    @Test
    fun deleteCustomerSuccess() {
        val customerId = "123"
        val billingGroupId = "billingGroup123"
        val outboundVoiceProfileId = "outboundVoiceProfile123"
        val customer = mockk<Customer>()
        val deprovisionResult = DeprovisionResult(success = true, errors = emptyList())

        every { customerService.getById(customerId) } returns customer
        every { customer.name } returns "Test Customer"
        every { customerProvisioningService.deprovisionV2(customerId, billingGroupId, outboundVoiceProfileId) } returns deprovisionResult
        every { customerService.deleteById(customerId) } returns Unit

        val response = customerV2Controller.deleteCustomer(customerId, billingGroupId, outboundVoiceProfileId)

        assertEquals(HttpStatus.NO_CONTENT, response.statusCode)
        verify { customerService.getById(customerId) }
        verify { customerProvisioningService.deprovisionV2(customerId, billingGroupId, outboundVoiceProfileId) }
        verify { customerService.deleteById(customerId) }
    }

    @Test
    fun deleteCustomerNotFound() {
        val customerId = "123"
        val billingGroupId = "billingGroup123"
        val outboundVoiceProfileId = "outboundVoiceProfile123"

        every { customerService.getById(customerId) } returns null

        val response = customerV2Controller.deleteCustomer(customerId, billingGroupId, outboundVoiceProfileId)

        assertEquals(HttpStatus.NOT_FOUND, response.statusCode)
        verify { customerService.getById(customerId) }
    }

}