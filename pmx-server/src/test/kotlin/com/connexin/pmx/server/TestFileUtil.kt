package com.connexin.pmx.server

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.cfg.CoercionAction
import com.fasterxml.jackson.databind.cfg.CoercionInputShape
import com.fasterxml.jackson.databind.type.LogicalType
import com.telnyx.sdk.Configuration

class TestFileUtil {
    companion object {
        var mapper: ObjectMapper = Configuration.getDefaultApiClient().json.mapper

        fun getAsString(path: String): String {
            return this::class.java.getResource(path).readText()
        }

        fun getAsTree(path: String): JsonNode {
            val json = getAsString(path)

            return mapper.readTree(json)
        }

        fun <T> get(path: String, clazz: Class<T>): T {
            val json = getAsString(path)

            return mapper.readValue(json, clazz)
        }
    }
}