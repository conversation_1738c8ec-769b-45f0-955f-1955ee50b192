package com.connexin.pmx.server.services

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.data.*
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.ETMessages
import com.connexin.pmx.server.models.bli.MTMessages
import com.connexin.pmx.server.models.bli.VTMessages
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("test")
@SpringBootTest
@Import(TestConfig::class)
class BLIMappingServiceTest {

    @Autowired
    private lateinit var sut: BLIMappingService

    companion object {
        val messageText = "This is - - - - -  Pediatrics calling to remind you that <PERSON><PERSON> has an appointment with DemoDoc on Friday January 14 at 1:00 PM. If you cannot make this appointment, please call the office at 000-000-0000 during regular business hours to reschedule. Thank you!"
        val voiceMessage = "This is  - - - - - Pediatrics calling to remind you that Clue has an appointment with DemoDoc on Friday January 14 at 1:00 PM. Press 1 now to confirm, or press 2 to be connected to someone who can help you reschedule. Thank you!"
    }

    // messages tests

    @Test
    fun `mapToCreateMessageRequest for voice message`() {
        val actual = sut.mapToCreateMessageRequest(testVTMessages(), "1")

        val phoneNumber = setOf("2125578877")

        assertThat(actual).isNotNull
        assertThat(actual.customerId).isEqualTo("1")
        assertThat(actual.type).isEqualTo(MessageType.VOICE)
        assertThat(actual.to).isEqualTo(phoneNumber)
        assertThat(actual.message).isEqualTo(voiceMessage)     // live voice message
        assertThat(actual.altMessage).isEqualTo(messageText)   // voice mail message
        assertThat(actual.replyTo).isEqualTo("2184547979")
        assertThat(actual.sendFrom).isEqualTo("09:00-05:00")
        assertThat(actual.sendUntil).isEqualTo("21:00-05:00")
    }

    @Test
    fun `mapToCreateMessageRequest for invalid voice message`() {
        val actual = sut.mapToCreateMessageRequest(
            VTMessages(
            testVTMessage1().copy(
                phoneNumber = "",
                callerID = ""
            )
        ), "1")

        assertThat(actual).isNotNull
        assertThat(actual.to).isEmpty()
        assertThat(actual.replyTo).isNull()
    }

    @Test
    fun `mapToCreateMessageRequest for email message`() {
        val actual = sut.mapToCreateMessageRequest(testETMessages(), "1")

        val email = setOf("<EMAIL>")

        assertThat(actual).isNotNull
        assertThat(actual.customerId).isEqualTo("1")
        assertThat(actual.type).isEqualTo(MessageType.EMAIL)
        assertThat(actual.to).isEqualTo(email)
        assertThat(actual.message).isEqualTo(messageText)
        assertThat(actual.subject).isEqualTo("Appointment Reminder")
        assertThat(actual.replyTo).isEqualTo("<EMAIL>")
    }

    @Test
    fun `mapToCreateMessageRequest for invalid email message`() {
        val actual = sut.mapToCreateMessageRequest(
            ETMessages(
            testETMessage1().copy(
                emailTo = "",
                emailReplyTo = ""
            )
        ), "1")

        assertThat(actual).isNotNull
        assertThat(actual.to).isEmpty()
        assertThat(actual.replyTo).isNull()
    }

    @Test
    fun `mapToCreateMessageRequest for email broadcast message`() {
        val actual = sut.mapToCreateMessageRequest(testEBMessages(), "1")

        val emails = setOf("<EMAIL>", "<EMAIL>", "<EMAIL>")

        assertThat(actual).isNotNull
        assertThat(actual.customerId).isEqualTo("1")
        assertThat(actual.type).isEqualTo(MessageType.EMAIL_BROADCAST)
        assertThat(actual.to).isEqualTo(emails)
        assertThat(actual.message).isEqualTo(messageText)
        assertThat(actual.subject).isEqualTo("Appointment Reminder")
        assertThat(actual.replyTo).isEqualTo("<EMAIL>")
    }

    @Test
    fun `mapToCreateMessageRequest for text message`() {
        val actual = sut.mapToCreateMessageRequest(testMTMessages(), "1")

        val cellPhoneNumber = setOf("17105455226")

        assertThat(actual).isNotNull
        assertThat(actual.customerId).isEqualTo("1")
        assertThat(actual.type).isEqualTo(MessageType.SMS)
        assertThat(actual.to).isEqualTo(cellPhoneNumber)
        assertThat(actual.message).isEqualTo("Appointment reminder for Friday January 14, 2022 at 1:00 PM")
        assertThat(actual.sendFrom).isEqualTo("09:00-05:00")
        assertThat(actual.sendUntil).isEqualTo("17:00-05:00")
    }

    @Test
    fun `mapToCreateMessageRequest for invalid text message`() {
        val actual = sut.mapToCreateMessageRequest(
            MTMessages(
            testMTMessage().copy(
                cellPhoneNumber = ""
            )
        ), "1")

        assertThat(actual).isNotNull
        assertThat(actual.to).isEmpty()
    }

    // report tests

    @Test
    fun mapToVoiceMessageReport() {
        val actual = sut.mapToVoiceMessageReport(testPMXMessageForVoiceReport())

        assertThat(actual).isNotNull
        assertThat(actual.vtReport.uniqueId).isEqualTo("100")
        assertThat(actual.vtReport.jobID).isEqualTo("59akd871-864d-4d32-8f1d-7a07d2ef449a")
        assertThat(actual.vtReport.phoneNumber).isEqualTo("12125157878")
        assertThat(actual.vtReport.duration).isNull()
        assertThat(actual.vtReport.rate).isNull()
        assertThat(actual.vtReport.cost).isNull()
        assertThat(actual.vtReport.status).isEqualTo(sut.getMessageStatus(MessageStatus.DELIVERED))
        assertThat(actual.vtReport.error).isNull()
        assertThat(actual.vtReport.deliveryMethod).isEqualTo(sut.getVoiceDeliveryMethod(VoiceDeliveryMethod.VOICE_MAIL))
        assertThat(actual.vtReport.keyPress).isEqualTo("1")
        assertThat(actual.vtReport.timestamp).isEqualTo("10/20/2020 09:19:23 AM")
    }

    @Test
    fun mapToEmailReport() {
        val actual = sut.mapToEmailReport(testPMXMessageForEmailReport())

        assertThat(actual).isNotNull
        assertThat(actual.etReport.formId).isEqualTo("200")
        assertThat(actual.etReport.uniqueId).isEqualTo("200")
        assertThat(actual.etReport.orderId).isEqualTo("5228871-864d-4d32-8f1d-7a07d2ef449a")
        assertThat(actual.etReport.project).isNull()
        assertThat(actual.etReport.emailAddress).isEqualTo("<EMAIL>")
        assertThat(actual.etReport.openCount).isNull()
        assertThat(actual.etReport.lastOpened).isNull()
        assertThat(actual.etReport.jobStatus).isEqualTo(sut.getMessageStatus(MessageStatus.DELIVERED))
        assertThat(actual.etReport.result).isNull()
        assertThat(actual.etReport.error).isNull()
        assertThat(actual.etReport.timestamp).isEqualTo("09/17/2020 03:12:15 PM")
    }

    @Test
    fun `mapToEmailReport for status = FAILED should show unknown error if none is provided`() {
        val actual = sut.mapToEmailReport(testPMXMessageForEmailReport().copy(
            status = MessageStatus.FAILED
        ))

        assertThat(actual).isNotNull
        assertThat(actual.etReport.jobStatus).isEqualTo("FAILED")
        assertThat(actual.etReport.error).isEqualTo("Unknown error")
    }

    @Test
    fun `mapToEmailReport for status = FAILED should show formatted error`() {
        val actual = sut.mapToEmailReport(testPMXMessageForEmailReport().copy(
            status = MessageStatus.FAILED,
            emailDeliveryFailureReason = EmailDeliveryFailureReason.SPAM_DETECTED,
            errors = "{ \"reason\": \"test\" }"
        ))

        assertThat(actual).isNotNull
        assertThat(actual.etReport.jobStatus).isEqualTo("FAILED")
        assertThat(actual.etReport.error).isEqualTo("title: SPAM_DETECTED\ndetail: test")
    }

    @Test
    fun mapToEmailBroadcastReport() {
        val actual = sut.mapToEmailBroadcastReport(testPMXMessageWithForEBReport())

        assertThat(actual).isNotNull

        actual.ebReport.forEach {
            assertThat(it.orderId).isEqualTo("300")
            assertThat(it.project).isEqualTo("Broadcast")
            assertThat(it.openCount).isNull()
            assertThat(it.lastOpened).isNull()
            assertThat(it.result).isNull()
            assertThat(it.error).isNull()
            assertThat(it.timestamp).isEqualTo("09/15/2019 02:15:10 PM")
            assertThat(it.name).isNull()
        }

        val messageStatus = sut.getMessageStatus(MessageStatus.DELIVERED)

        assertThat(actual.ebReport.first().email).isEqualTo("<EMAIL>")
        assertThat(actual.ebReport.first().emailAddress).isEqualTo("<EMAIL>")
        assertThat(actual.ebReport.first().jobStatus).isEqualTo(messageStatus)

        assertThat(actual.ebReport[1].email).isEqualTo("<EMAIL>")
        assertThat(actual.ebReport[1].emailAddress).isEqualTo("<EMAIL>")
        assertThat(actual.ebReport[1].jobStatus).isEqualTo(messageStatus)
    }

    @Test
    fun `mapToEmailBroadcastReport for status = FAILED should show unknown error if none is provided`() {
        val testMsg = testPMXMessageWithForEBReport()
        val actual = sut.mapToEmailBroadcastReport(testMsg.copy(
            status = MessageStatus.FAILED,
            emailRecipients = testMsg.emailRecipients?.map { it.copy(status = MessageStatus.FAILED) }
        ))

        assertThat(actual).isNotNull
        assertThat(actual.ebReport.first().jobStatus).isEqualTo("FAILED")
        assertThat(actual.ebReport.first().error).isEqualTo("Unknown error")
        assertThat(actual.ebReport.last().jobStatus).isEqualTo("FAILED")
        assertThat(actual.ebReport.last().error).isEqualTo("Unknown error")
    }

    @Test
    fun `mapToEmailBroadcastReport for status = FAILED should show formatted error`() {
        val testMsg = testPMXMessageWithForEBReport()
        val actual = sut.mapToEmailBroadcastReport(testMsg.copy(
            status = MessageStatus.FAILED,
            emailRecipients = testMsg.emailRecipients?.map { it.copy(status = MessageStatus.FAILED, reason = EmailDeliveryFailureReason.SPAM_DETECTED, errors = "{ \"reason\": \"test\" }") }
        ))

        assertThat(actual).isNotNull
        assertThat(actual.ebReport.first().jobStatus).isEqualTo("FAILED")
        assertThat(actual.ebReport.first().error).isEqualTo("title: SPAM_DETECTED\ndetail: test")
        assertThat(actual.ebReport.last().jobStatus).isEqualTo("FAILED")
        assertThat(actual.ebReport.last().error).isEqualTo("title: SPAM_DETECTED\ndetail: test")
    }

    @Test
    fun mapToTextMessageReport() {
        val actual = sut.mapToTextMessageReport(testPMXMessageForTextReport())

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.uniqueId).isEqualTo("400")
        assertThat(actual.mtReport.orderID).isEqualTo("995Ad877-864d-4d32-8f1d-7a07d2ef449a")
        assertThat(actual.mtReport.project).isNull()
        assertThat(actual.mtReport.cellPhoneNumber).isEqualTo("12725137978")
        assertThat(actual.mtReport.jobStatus).isEqualTo(sut.getMessageStatus(MessageStatus.DELIVERED))
        assertThat(actual.mtReport.result).isNull()
        assertThat(actual.mtReport.error).isNull()
    }

    @Test
    fun `mapToCreateMessageRequest for valid voice message with StopTime past midnight`() {
        val actual = sut.mapToCreateMessageRequest(
            VTMessages(
            testVTMessage1().copy(
                stopTime = "25:00"
            )
        ), "1")

        assertThat(actual).isNotNull
        assertThat(actual.sendUntil).isEqualTo("01:00-05:00")
    }

    @Test
    fun `mapToCreateMessageRequest for text message with StopTime past midnight`() {
        val actual = sut.mapToCreateMessageRequest(
            MTMessages(
            testMTMessage().copy(
                stopTime = "25:00"
            )
        ), "1")

        assertThat(actual).isNotNull
        assertThat(actual.sendUntil).isEqualTo("01:00-05:00")
    }

    @Test
    fun `mapToTextMessageReport for status = SENT`() {
        val actual = sut.mapToTextMessageReport(
            testPMXMessageForTextReport().copy(
                status = MessageStatus.SENT
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.jobStatus).isEqualTo("Sent")
    }

    @Test
    fun `mapToTextMessageReport for status = FAILED should show unknown error if none is provided`() {
        val actual = sut.mapToTextMessageReport(
            testPMXMessageForTextReport().copy(
                status = MessageStatus.FAILED
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.jobStatus).isEqualTo("FAILED")
        assertThat(actual.mtReport.error).isEqualTo("Unknown error")
    }

    @Test
    fun `mapToTextMessageReport for status = FAILED should formatted error`() {
        val actual = sut.mapToTextMessageReport(
            testPMXMessageForTextReport().copy(
                status = MessageStatus.FAILED,
                errors = "[ { \"title\": \"test\", \"code\": \"999\", \"detail\": \"test detail\" } ]"
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.jobStatus).isEqualTo("FAILED")
        assertThat(actual.mtReport.error).isEqualTo("title: test\ncode: 999\ndetail: test detail")
    }

    @Test
    fun `mapToTextMessageReport for status = FAILED should formatted error with errors property`() {
        val actual = sut.mapToTextMessageReport(
            testPMXMessageForTextReport().copy(
                status = MessageStatus.FAILED,
                errors = "{ \"errors\": [ { \"title\": \"test\", \"code\": \"999\", \"detail\": \"test detail\" } ] }"
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.jobStatus).isEqualTo("FAILED")
        assertThat(actual.mtReport.error).isEqualTo("title: test\ncode: 999\ndetail: test detail")
    }

    @Test
    fun `mapToTextMessageReport for status = DELIVERED`() {
        val actual = sut.mapToTextMessageReport(testPMXMessageForTextReport())

        assertThat(actual).isNotNull
        assertThat(actual.mtReport.jobStatus).isEqualTo("Sent")
    }

    @Test
    fun `mapToVoiceMessageReport for status = SENT`() {
        val actual = sut.mapToVoiceMessageReport(
            testPMXMessageForVoiceReport().copy(
                status = MessageStatus.SENT
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.vtReport.status).isEqualTo("Sent")
    }

    @Test
    fun `mapToVoiceMessageReport for status = FAILED should show unknown error if none is provided`() {
        val actual = sut.mapToVoiceMessageReport(
            testPMXMessageForVoiceReport().copy(
                status = MessageStatus.FAILED
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.vtReport.status).isEqualTo("FAILED")
        assertThat(actual.vtReport.error).isEqualTo("Unknown error")
    }

    @Test
    fun `mapToVoiceMessageReport for status = FAILED should show formatted errors`() {
        val actual = sut.mapToVoiceMessageReport(
            testPMXMessageForVoiceReport().copy(
                status = MessageStatus.FAILED,
                errors = "[ { \"title\": \"test\", \"code\": \"999\", \"detail\": \"test detail\" } ]"
            )
        )

        assertThat(actual).isNotNull
        assertThat(actual.vtReport.status).isEqualTo("FAILED")
        assertThat(actual.vtReport.error).isEqualTo("title: test\ncode: 999\ndetail: test detail")
    }

    @Test
    fun `mapToVoiceMessageReport for status = DELIVERED`() {
        val actual = sut.mapToVoiceMessageReport(testPMXMessageForVoiceReport())

        assertThat(actual).isNotNull
        assertThat(actual.vtReport.status).isEqualTo("Sent")
    }
}