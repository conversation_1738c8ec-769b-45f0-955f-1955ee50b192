package com.connexin.pmx.server.data

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.MTMessage
import com.connexin.pmx.server.models.bli.MTMessages
import com.connexin.pmx.server.utils.InstantUtil
import java.time.Instant
import java.time.temporal.ChronoUnit


class MTTestData() {
    companion object {
        val mtTestMessage = """
            UserName=96267&UserPassword=8993&XMLPost=
            <Orders>
            <Order Type="MT">
            <ShortCode>47879</ShortCode>
            <CellPhoneNumber>17105455226</CellPhoneNumber>
            <Date>2021-02-22</Date>
            <Time>20:00</Time>
            <StopTime>17:00</StopTime>
            <RestartTime>09:00</RestartTime>
            <OverType>truncate</OverType>
            <MessageFile/><MessageID/>
            <Message>Appointment reminder for Friday January 14, 2022 at 1:00 PM</Message>
            </Order></Orders>&PostWay=sync"""
    }
}

fun testMTMessages(): MTMessages {

    val sampleMessage = testMTMessage()
    val mtMessages = MTMessages(sampleMessage)
    return mtMessages

}

fun testMTMessage(): MTMessage {

    val testOrder = MTMessage (
        type = "MT",
        shortCode = "47879",
        cellPhoneNumber = "17105455226",
        date = "2021-02-22",
        time = "20:00",
        stopTime = "17:00",
        restartTime = "09:00",
        overType = "truncate",
        messageFile = "",
        messageID = "",
        message = "Appointment reminder for Friday January 14, 2022 at 1:00 PM")

    return testOrder

}

// test pmx message for testing MT Reports
fun testPMXMessageForTextReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "400",
        customerId = "10",
        type = MessageType.SMS,
        status = MessageStatus.DELIVERED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 1,
        createdAt = InstantUtil.toInstant("07/10/2020 11:05:10 AM"),
        updatedAt = InstantUtil.toInstant("07/10/2020 11:07:19 AM"),
        completedAt = InstantUtil.toInstant("07/10/2020 11:10:25 AM"),
        to = "12725137978",
        from = "12835135978",
        subject = null,   // used for email only
        message = "Appointment reminder for Tues Aug 25th, 2020 at 9:30AM.",
        altMessage = null,   // used for voice messages only
        remoteId = "995Ad877-864d-4d32-8f1d-7a07d2ef449a",
        errors = null,
        replyTo = null,
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,     // used for voice messages only
        emailRecipients = null    // used for email broadcast only
    )

    return testMessage
}

// test pmx message for testing MT Reports
fun testFailedPMXMessageForTextReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "401",
        customerId = "10",
        type = MessageType.SMS,
        status = MessageStatus.FAILED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 1,
        createdAt = InstantUtil.toInstant("07/10/2020 11:05:10 AM"),
        updatedAt = InstantUtil.toInstant("07/10/2020 11:07:19 AM"),
        completedAt = InstantUtil.toInstant("07/10/2020 11:10:25 AM"),
        to = "12725137978",
        from = "12835135978",
        subject = null,   // used for email only
        message = "Appointment reminder for Tues Aug 25th, 2020 at 9:30AM.",
        altMessage = null,   // used for voice messages only
        remoteId = "995Ad877-864d-4d32-8f1d-7a07d2ef449a",
        errors = "[\n" +
                "    {\n" +
                "      \"code\":\"10007\",\n" +
                "      \"title\":\"Unexpected error\",\n" +
                "      \"detail\":\"An unexpected error occurred.\"\n" +
                "    }\n" +
                "  ]",
        replyTo = null,
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,     // used for voice messages only
        emailRecipients = null    // used for email broadcast only
    )

    return testMessage
}

// test pmx message for testing MT Reports
fun testFailedPMXMessageFromLogsForTextReport(): PmxMessage {

    val testMessage = PmxMessage(
        id = "402",
        customerId = "10",
        type = MessageType.SMS,
        status = MessageStatus.FAILED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 1,
        createdAt = InstantUtil.toInstant("07/10/2020 11:05:10 AM"),
        updatedAt = InstantUtil.toInstant("07/10/2020 11:07:19 AM"),
        completedAt = InstantUtil.toInstant("07/10/2020 11:10:25 AM"),
        to = "12725137978",
        from = "12835135978",
        subject = null,   // used for email only
        message = "Appointment reminder for Tues Aug 25th, 2020 at 9:30AM.",
        altMessage = null,   // used for voice messages only
        remoteId = "995Ad877-864d-4d32-8f1d-7a07d2ef449a",
        errors = "[ {\n" +
                    "\"code\" : \"40001\",\n" +
                    "\"detail\" : \"The destination number is either a landline or a non-routable wireless number.\",\n" +
                    "\"meta\" : {\n" +
                        "\"url\" : \"https://developers.telnyx.com/docs/overview/errors/40001\"\n" +
                    "},\n" +
                    "\"title\" : \"Not routable\"\n" +
                "} ]",
        replyTo = null,
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,     // used for voice messages only
        emailRecipients = null    // used for email broadcast only
    )

    return testMessage
}

/*
======================================================================================
XML from OP:
      '<Orders><Order Type="' + BLI_MSG_TYPE_TEXTMSG + '">' +
      '<ShortCode>' + DEFAULT_TEXTMSG_CALLBACK_NUM + '</ShortCode>' +    //fp As per BLI instructions introduced 11.1
      '<CellPhoneNumber>' + CleanPhoneNumber(FieldByName('CONTACT_VALUE').AsString) + '</CellPhoneNumber>' +
      '<Date>' + FormatDateTime('yyyy-mm-dd', Date) + '</Date>' +
      '<Time>' + FormatDateTime('hh:nn', GetAdjustedCurrentDateTime()) + '</Time>' +
      '<StopTime>' + stopTime + '</StopTime>' +
      '<RestartTime>' + restartTime + '</RestartTime>' +
      '<OverType>truncate</OverType>' +
      '<MessageFile/><MessageID/>' +
      '<Message>' + CleanTextMessage(FieldByName('MESSAGE_DATA').AsString) + '</Message>' +
    '</Order></Orders>';
======================================================================================
Sample xml:
<Orders>
	<Order Type=\"MT\">
		<ShortCode>95664</ShortCode>
		<CellPhoneNumber>12159063307</CellPhoneNumber>
		<Date>2022-01-24</Date>
		<Time>13:36</Time>
		<StopTime>21:00</StopTime>
		<RestartTime>09:00</RestartTime>
		<OverType>truncate</OverType>
		<MessageFile/>
		<MessageID/>
		<Message>Pediatric appt: Testccvv, Jan 25 @ 12:30 PM. Confirm:</Message>
	</Order>
</Orders>
*/

/*
XML Samples of MT Reports:

<PostAPIResponse>
  <SaveTransactionalOrderResult>
    <Exception>The MT Job which Unqid is 5b743568-d1c6-49bb-8325-0209f888430f does not exist.</Exception>
  </SaveTransactionalOrderResult>
</PostAPIResponse>

<Report>
	<MT>
		<OrderID>1685170</OrderID>
		<UNQID>708ccf6f-2eca-4edb-bfd8-c48371c12798</UNQID>
		<Project>Transactional Mobile,1,2013</Project>
		<CellPhoneNumber>12152851345</CellPhoneNumber>
		<Jobstatus>Sent</Jobstatus>
		<Result></Result>
		<Error></Error>
	</MT>
</Report>
*/