package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.repositories.ResponseRepository
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class ResponseServiceImplTest {
    @MockK
    lateinit var responseRepository: ResponseRepository

    private lateinit var sut: ResponseServiceImpl

    @BeforeEach
    fun setup() {
        sut = ResponseServiceImpl(responseRepository)
    }

    @Test
    fun `create declined response should apply dedupeDeclinedResponse logic`() {
        val response = ConfirmationResponse(
                id = "1",
                engagementId = "engagement_id",
                customerId = "customer_id",
                isFinal = true,
                appointments = emptySet(),
                respondents = emptySet(),
                result = ConfirmationStatus.DECLINED
        )

        every { responseRepository.findDeclinedResponse(response) } returns response
        every { responseRepository.save(any()) } returns response


        sut.create(response)


        verify(exactly = 1) {
            responseRepository.findDeclinedResponse(response)
        }
        verify(exactly = 0) {
            responseRepository.save(any())
        }
    }

    @Test
    fun `create a non-declined response should not apply dedupeDeclinedResponse logic`() {
        val response = ConfirmationResponse(
                engagementId = "engagement_id",
                customerId = "customer_id",
                isFinal = true,
                appointments = emptySet(),
                respondents = emptySet(),
                result = ConfirmationStatus.CONFIRMED
        )

        every { responseRepository.findDeclinedResponse(response) } returns response
        every { responseRepository.save(any()) } returns response


        sut.create(response)


        verify(exactly = 0) {
            responseRepository.findDeclinedResponse(response)
        }
        verify(exactly = 1) {
            responseRepository.save(any())
        }
    }

}