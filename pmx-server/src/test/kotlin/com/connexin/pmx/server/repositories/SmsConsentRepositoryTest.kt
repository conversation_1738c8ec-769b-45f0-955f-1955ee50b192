package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.SmsConsent
import com.connexin.pmx.server.models.ConsentStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.repository.findByIdOrNull
import org.springframework.test.context.ActiveProfiles
import java.time.Instant

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class SmsConsentRepositoryTest {

    @Autowired
    lateinit var template: MongoTemplate

    @Autowired
    lateinit var sut: SmsConsentRepository

    @AfterEach
    fun cleanup() {
        template.remove(SmsConsent::class.java)
    }

    @Test
    fun `CRUD operations`() {
        val created = sut.save(
            SmsConsent(id = "test", status = ConsentStatus.OPTED_IN, relatedMessageId = "1")
        )

        assertThat(created.createdAt).isEqualTo(created.updatedAt).isNotNull

        val retrieved = sut.findByIdOrNull(created.id)

        assertThat(retrieved)
            .usingRecursiveComparison()
            .ignoringFieldsOfTypes(Instant::class.java)
            .isEqualTo(created)

        sut.deleteById(created.id)

        assertThat(sut.findByIdOrNull(created.id)).isNull()
    }

    @Test
    fun findByRelatedMessageId() {
        val created = sut.save(SmsConsent(id = "test", relatedMessageId = "1"))

        val actual = sut.findByRelatedMessageId("1")

        assertThat(actual.isPresent).isTrue
        assertThat(actual.get().id).isEqualTo("test")
    }
}