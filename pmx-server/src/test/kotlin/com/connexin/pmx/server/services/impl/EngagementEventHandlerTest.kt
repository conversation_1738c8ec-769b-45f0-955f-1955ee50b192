package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.dtos.EngagementEventDto
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant

@ExtendWith(MockKExtension::class)
class EngagementEventHandlerTest {
    @InjectMockKs
    lateinit var sut: EngagementEventHandlerImpl

    @MockK
    lateinit var customerService: CustomerService

    @MockK
    lateinit var engagementService: EngagementService

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        customer = Customer(
            id = "1",
            name = "Test",
            status = CustomerStatus.ENABLED
        )

        every { customerService.getById(any()) } returns null
        every { customerService.getById(customer.id!!) } returns customer

        every { engagementService.getByAppointmentId("1", any()) } returns null
    }

    @Test
    fun `handle should return early if customer, appointment or engagement could not be found`() {
        var engagementEventRequest = EngagementEventDto("nope", EngagementEvent.CHECKED_IN)
        sut.handle(engagementEventRequest, "0")

        verify(exactly = 0) {
            engagementService.sendEvent(any())
        }
        engagementEventRequest = EngagementEventDto("1", EngagementEvent.CHECKED_IN)

        sut.handle(engagementEventRequest, "0")
        verify(exactly = 0) {
            engagementService.sendEvent(any())
        }

        engagementEventRequest = EngagementEventDto("1", EngagementEvent.CHECKED_IN)

        sut.handle(engagementEventRequest, "2")
        verify(exactly = 0) {
            engagementService.sendEvent(any())
        }

        engagementEventRequest = EngagementEventDto("1", EngagementEvent.CHECKED_IN)

        sut.handle(engagementEventRequest, "3")
        verify(exactly = 0) {
            engagementService.sendEvent(any())
        }
    }


    @Test
    fun `handle should send check in event`() {
        val engagement = buildEngagement()
        val engagementEventRequest = EngagementEventDto("2", EngagementEvent.CHECKED_IN)

        sut.handle(engagementEventRequest, "1")

        verify(exactly = 1) {
            engagementService.sendEvent(match<CheckInResponseEvent> { it.engagement == engagement && it.respondents.isNotEmpty() })
        }
    }

    private fun buildEngagement(): Engagement {
        val engagement = Engagement(
            id = "engagement1",
            customerId = customer.id!!,
            status = EngagementStatus.CHECK_IN,
            nextCheckpoint = Instant.now(),
            eventDate = Instant.now(),
            resources = mutableSetOf(
                AppointmentResource(
                    id = "2",
                    startTime = Instant.now(),
                    appointmentType = "apptType1",
                    practice = "practice1",
                    location = "location1",
                    staff = "staff1",
                    reason = "test",
                    patient = "patient1"
                ),
                ContactResource(
                    id = "person1",
                    familyName = "test",
                    givenName = "contact",
                    email = "<EMAIL>",
                    contactMethod = ContactMethod.EMAIL,
                    phone = null,
                    language = Language.ENGLISH
                ),
                ContactResource(
                    id = "person2",
                    familyName = "test",
                    givenName = "contact2",
                    email = "<EMAIL>",
                    contactMethod = ContactMethod.SMS,
                    phone = "**********",
                    language = Language.ENGLISH
                )
            )
        )
        every { engagementService.getByAppointmentId("1", "2") } returns engagement
        every { engagementService.sendEvent(match<CheckInResponseEvent> { it.engagement == engagement && it.customer == customer }) } returns engagement

        return engagement
    }
}