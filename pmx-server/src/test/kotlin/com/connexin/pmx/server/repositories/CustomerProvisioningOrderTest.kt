package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.CsvMultiCustomerProvisioningOrder
import com.connexin.pmx.server.models.CustomerProvisioningOrder
import com.connexin.pmx.server.models.NewCustomerProvisioningOrder
import com.connexin.pmx.server.models.ProvisioningStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageRequest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class CustomerProvisioningOrderTest {
    @Autowired
    lateinit var sut: CustomerProvisioningOrderRepository

    @Autowired
    private lateinit var template: MongoTemplate

    @BeforeEach
    fun setup() {
        sut.save(NewCustomerProvisioningOrder(
            id = "good1A",
            customerId = "1"
        ))

        sut.save(CsvMultiCustomerProvisioningOrder(
            id = "good1B",
            csvContent = "test 1"
        ))

        sut.save(NewCustomerProvisioningOrder(
            id = "bad1B",
            customerId = "2",
            status = ProvisioningStatus.COMPLETED
        ))

        sut.save(CsvMultiCustomerProvisioningOrder(
            id = "bad2B",
            csvContent = "test 2",
            status = ProvisioningStatus.COMPLETED
        ))

        sut.save(NewCustomerProvisioningOrder(
            id = "bad3A",
            customerId = "3",
            status = ProvisioningStatus.FAILED
        ))

        sut.save(CsvMultiCustomerProvisioningOrder(
            id = "bad3B",
            csvContent = "test 2",
            status = ProvisioningStatus.FAILED
        ))

        sut.save(NewCustomerProvisioningOrder(
            id = "bad4A",
            customerId = "2",
            status = ProvisioningStatus.IN_PROGRESS,
            attempts = 10
        ))

        sut.save(CsvMultiCustomerProvisioningOrder(
            id = "bad4B",
            csvContent = "test 2",
            status = ProvisioningStatus.IN_PROGRESS,
            attempts = 10
        ))
    }

    @AfterEach
    fun teardown() {
        template.dropCollection(CustomerProvisioningOrder::class.java)
    }

    @Test
    fun `findPending should only return valid orders`() {
        val actual = sut.findPending(5, PageRequest.of(0, 10))

        assertThat(actual.numberOfElements).isEqualTo(2)
        assertThat(actual.content.map { it.id }).containsExactly("good1A", "good1B")
    }
}