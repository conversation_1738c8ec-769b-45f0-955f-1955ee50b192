package com.connexin.pmx.server.models

import org.assertj.core.api.Assertions.assertThatNoException
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.valiktor.ConstraintViolationException
import java.util.stream.Stream

class ContactResourceTests {
    @ParameterizedTest
    @MethodSource("validContacts")
    fun `validate should pass`(method: ContactMethod, phone: String?, email: String?) {
        val contact = ContactResource(
            id = "1",
            familyName = "test",
            givenName = "test",
            contactMethod = method,
            phone = phone,
            email = email,
            language = Language.ENGLISH
        )

        assertThatNoException().isThrownBy {
            contact.validate()
        }
    }

    @ParameterizedTest
    @MethodSource("invalidContacts")
    fun `validate should fail`(method: ContactMethod, phone: String?, email: String?) {
        val contact = ContactResource(
            id = "1",
            familyName = "test",
            givenName = "test",
            contactMethod = method,
            phone = phone,
            email = email,
            language = Language.ENGLISH
        )

        assertThatThrownBy { contact.validate() }
            .isInstanceOf(ConstraintViolationException::class.java)
    }

    companion object {
        @JvmStatic
        fun validContacts(): Stream<Arguments> = Stream.of(
            Arguments.of(ContactMethod.SMS, "+14405551234", null),
            Arguments.of(ContactMethod.VOICE, "+14405551234", null),
            Arguments.of(ContactMethod.EMAIL, null, "<EMAIL>"),
        )

        @JvmStatic
        fun invalidContacts(): Stream<Arguments> = Stream.of(
            Arguments.of(ContactMethod.NONE, "+14405551234", "<EMAIL>"),
            Arguments.of(ContactMethod.VOICE, null, "<EMAIL>"),
            Arguments.of(ContactMethod.VOICE, "", "<EMAIL>"),
            Arguments.of(ContactMethod.VOICE, "    ", "<EMAIL>"),
            Arguments.of(ContactMethod.VOICE, "not a phone number", "<EMAIL>"),
            Arguments.of(ContactMethod.SMS, null, "<EMAIL>"),
            Arguments.of(ContactMethod.SMS, "", "<EMAIL>"),
            Arguments.of(ContactMethod.SMS, "    ", "<EMAIL>"),
            Arguments.of(ContactMethod.SMS, "not a phone number", "<EMAIL>"),
            Arguments.of(ContactMethod.EMAIL, "+14405551234", null),
            Arguments.of(ContactMethod.EMAIL, "+14405551234", ""),
            Arguments.of(ContactMethod.EMAIL, "+14405551234", "     "),
            Arguments.of(ContactMethod.EMAIL, "+14405551234", "not an email address"),
        )
    }
}