package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.LegacyConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.data.*
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.CreateMessageResponse
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.UUIDSource
import com.ninjasquad.springmockk.MockkBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant
import java.time.OffsetTime

@Import(value = [SecurityConfig::class, LegacyConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(BLIController::class)
class BLIControllerTests {

    companion object {
        private val log = LoggerFactory.getLogger(BLIControllerTests::class.java)
        private const val url = "/PostAPI"
        private const val username = "96267"
        private const val password = "8993"
        private const val message =
            "This is - - - - -  Pediatrics calling to remind you that Clue has an appointment with DemoDoc on Friday January 14 at 1:00 PM. If you cannot make this appointment, please call the office at 000-000-0000 during regular business hours to reschedule. Thank you!"
        private const val voiceMessage =
            "This is  - - - - - Pediatrics calling to remind you that Clue has an appointment with DemoDoc on Friday January 14 at 1:00 PM. Press 1 now to confirm, or press 2 to be connected to someone who can help you reschedule. Thank you!"
    }

    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean
    private lateinit var customerService: CustomerService

    @MockkBean
    private lateinit var pmxMessageService: PmxMessageService

    @MockkBean
    private lateinit var uuidSource: UUIDSource

    @BeforeEach
    fun setup() {
        every { customerService.authenticate(username, password) } returns Customer(
            id = "1",
            status = CustomerStatus.ENABLED,
            name = "Test Pediatrics"
        )
        every { customerService.authenticate("bad", "bad") } returns null
        every { uuidSource.randomUUIDString() } returns "4de0c386-c5b6-4ef5-9753-829dce188760"
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    private fun sendMessageAct(messageType: MessageType, expectedResponseBody: String) {

        var viewName = ""
        var logMessage = ""
        var postContent = ""

        when (messageType) {
            MessageType.VOICE -> {
                viewName = "VTNew.aspx"
                postContent = VTTestData.vtTestMessage
                logMessage = "testCreateVTMessage: [Test VT Message= $postContent]"
            }
            MessageType.EMAIL -> {
                viewName = "ETNew.aspx"
                postContent = ETTestData.etTestMessage
                logMessage = "testCreateETMessage: [Test ET Message= $postContent]"
            }
            MessageType.EMAIL_BROADCAST -> {
                viewName = "EBNew.aspx"
                postContent = EBTestData.ebTestMessage
                logMessage = "testCreateEBMessage: [Test EB Message= $postContent]"
            }
            MessageType.SMS -> {
                viewName = "MTNew.aspx"
                postContent = MTTestData.mtTestMessage
                logMessage = "testCreateVTMessage: [Test MT Message= $postContent]"
            }
        }

        log.info(logMessage)

        mockMvc.perform(
            post("$url/$viewName")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content(postContent)
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(content().xml(expectedResponseBody))
    }

    @Test
    fun `createVoiceMessage success`() {

        val expectedRequest = CreateMessageRequest(
            customerId = "1",
            type = MessageType.VOICE,
            to = setOf("17105455226"),
            message = voiceMessage,
            altMessage = message,
            sendFrom = OffsetTime.parse("09:00-05:00"),
            sendUntil = OffsetTime.parse("21:00-05:00")
        )

        every { pmxMessageService.create(any()) } returns CreateMessageResponse(
            success = true,
            message = PmxMessage(
                id = "12345",
                customerId = "1",
                type = MessageType.VOICE,
                status = MessageStatus.QUEUED,
                message = expectedRequest.message,
                sendAfter = Instant.now()
            )
        )

        val expectedResponse = """
             <PostAPIResponse>
               <SaveTransactionalOrderResult>
                  <OrderID/>
                  <transactionID>12345</transactionID>
                  <Error/>
                  <Exception/>
               </SaveTransactionalOrderResult>
             </PostAPIResponse>
        """.trimIndent()

        sendMessageAct(MessageType.VOICE, expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.create(expectedRequest)
        }
    }

    @ParameterizedTest(name = "{0}New.aspx")
    @ValueSource(strings = ["VT", "ET", "EB", "MT"])
    fun `POST message returns 401 response for invalid credentials`(messageType: String) {
        mockMvc.perform(
            post("$url/${messageType}New.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content("UserName=bad&UserPassword=bad&XMLPost=&PostWay=Sync")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(
                content().xml(
                    """
                <PostAPIResponse>
                    <SaveTransactionalOrderResult>
                        <OrderID/>
                        <transactionID/>
                        <Error>401</Error>
                        <Exception>Unable to authenticate request.</Exception>
                    </SaveTransactionalOrderResult>
                </PostAPIResponse>""".trimIndent()
                )
            )
    }

    @Test
    fun `POST email broadcast fails validation`() {
        every { pmxMessageService.create(any()) } returns CreateMessageResponse(success = false, errors = listOf(ErrorDto("to", "Must have valid email addresses", 1)))

        mockMvc.perform(
            post("$url/EBNew.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content(EBTestData.ebTestMessage)
        )
            .andExpect(status().isBadRequest)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(content().xml("<PostAPIResponse><SaveTransactionalOrderResult><OrderID/><transactionID/><Error/><Exception>Must have valid email addresses</Exception></SaveTransactionalOrderResult></PostAPIResponse>"))
    }

    @ParameterizedTest(name = "{0}New.aspx")
    @ValueSource(strings = ["VT", "ET", "EB", "MT"])
    fun `POST message returns 400 response for blank xml body`(messageType: String) {
        mockMvc.perform(
            post("$url/${messageType}New.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content("UserName=$username&UserPassword=$password&XMLPost=&PostWay=Sync")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
    }

    @ParameterizedTest(name = "{0}New.aspx")
    @ValueSource(strings = ["VT", "ET", "EB", "MT"])
    fun `POST message returns 400 response for invalid xml body`(messageType: String) {
        mockMvc.perform(
            post("$url/${messageType}New.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content("UserName=$username&UserPassword=$password&XMLPost=INVALID_XML&PostWay=Sync")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
    }

    @ParameterizedTest(name = "{0}Report.aspx")
    @ValueSource(strings = ["VT", "ET", "MT", "EB"])
    fun `POST report returns 401 response for invalid credentials`(messageType: String) {
        mockMvc.perform(
            post("$url/${messageType}Report.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content("UserName=bad&UserPassword=bad&ReturnType=XML&Unqid=1&OrderID=1")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(
                content().xml(
                    """
                <PostAPIResponse>
                    <SaveTransactionalOrderResult>
                        <OrderID/>
                        <transactionID/>
                        <Error>401</Error>
                        <Exception>Unable to authenticate request.</Exception>
                    </SaveTransactionalOrderResult>
                </PostAPIResponse>""".trimIndent()
                )
            )
    }

    @ParameterizedTest(name = "{0}Report.aspx")
    @ValueSource(strings = ["VT", "ET", "MT", "EB"])
    fun `POST report returns 404 response for invalid message id`(messageType: String) {

        every { pmxMessageService.getById("1") } returns null

        mockMvc.perform(
            post("$url/${messageType}Report.aspx")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content("UserName=$username&UserPassword=$password&ReturnType=XML&Unqid=1&OrderID=1")
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(
                content().xml(
                    """
                <PostAPIResponse>
                    <SaveTransactionalOrderResult>
                        <OrderID/>
                        <transactionID/>
                        <Error>404</Error>
                        <Exception>Job with id 1 not found.</Exception>
                    </SaveTransactionalOrderResult>
                </PostAPIResponse>""".trimIndent()
                )
            )
    }

    @Test
    fun testSendEmailMessage() {

        val expectedRequest = CreateMessageRequest(
            customerId = "1",
            type = MessageType.EMAIL,
            to = setOf("<EMAIL>"),
            message = message,
            subject = "Appointment Reminder",
            replyTo = "<EMAIL>"
        )

        every { pmxMessageService.create(any()) } returns CreateMessageResponse(
            success = true,
            message = PmxMessage(
                id = "73087671",
                customerId = "1",
                type = MessageType.EMAIL,
                status = MessageStatus.QUEUED,
                message = expectedRequest.message,
                sendAfter = Instant.now()
            )
        )

        val expectedResponse = """            
             <PostAPIResponse>
               <SaveTransactionalOrderResult>
                  <OrderID/>
                  <transactionID>73087671</transactionID>
                  <Error/>
                  <Exception/>
               </SaveTransactionalOrderResult>
             </PostAPIResponse>
        """.trimIndent()

        sendMessageAct(MessageType.EMAIL, expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.create(expectedRequest)
        }
    }

    @Test
    fun testSendEmailBroadcastMessage() {

        val expectedRequest = CreateMessageRequest(
            customerId = "1",
            type = MessageType.EMAIL_BROADCAST,
            to = setOf(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ),
            message = message,
            subject = "Appointment Reminder",
            replyTo = "<EMAIL>",
        )

        every { pmxMessageService.create(any()) } returns CreateMessageResponse(
            success = true,
            message = PmxMessage(
                id = "98087675",
                customerId = "1",
                type = MessageType.EMAIL_BROADCAST,
                status = MessageStatus.QUEUED,
                message = expectedRequest.message,
                sendAfter = Instant.now()
            )
        )

        val expectedResponse = """            
             <PostAPIResponse>
               <SaveTransactionalOrderResult>
                  <OrderID/>
                  <transactionID>98087675</transactionID>
                  <Error/>
                  <Exception/>
               </SaveTransactionalOrderResult>
             </PostAPIResponse>
        """.trimIndent()

        sendMessageAct(MessageType.EMAIL_BROADCAST, expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.create(expectedRequest)
        }
    }

    @Test
    fun testSendTextMessage() {

        val expectedRequest = CreateMessageRequest(
            customerId = "1",
            type = MessageType.SMS,
            to = setOf("17105455226"),
            message = "Appointment reminder for Friday January 14, 2022 at 1:00 PM",
            sendFrom = OffsetTime.parse("09:00-05:00"),
            sendUntil = OffsetTime.parse("17:00-05:00")
        )

        every { pmxMessageService.create(any()) } returns CreateMessageResponse(
            success = true,
            message = PmxMessage(
                id = "55027275",
                customerId = "1",
                type = MessageType.EMAIL_BROADCAST,
                status = MessageStatus.QUEUED,
                message = expectedRequest.message,
                sendAfter = Instant.now()
            )
        )

        val expectedResponse = """            
             <PostAPIResponse>
               <SaveTransactionalOrderResult>
                  <OrderID/>
                  <transactionID>55027275</transactionID>
                  <Error/>
                  <Exception/>
               </SaveTransactionalOrderResult>
             </PostAPIResponse>
        """.trimIndent()

        sendMessageAct(MessageType.SMS, expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.create(expectedRequest)
        }
    }

    private fun retrieveReport(messageType: MessageType, uniqueId: String, expectedResponseBody: String) {

        val viewName = when (messageType) {
            MessageType.VOICE -> "VTReport.aspx"
            MessageType.EMAIL -> "ETReport.aspx"
            MessageType.EMAIL_BROADCAST -> "EBReport.aspx"
            MessageType.SMS -> "MTReport.aspx"
        }

        log.info("$viewName - Retrieve message report for id $uniqueId")

        var idName = "Unqid"
        if (messageType == MessageType.EMAIL_BROADCAST)
            idName = "OrderID"

        val postContent = "UserName=$username&UserPassword=$password&ReturnType=XML&$idName=$uniqueId"

        mockMvc.perform(
            post("$url/$viewName")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .content(postContent)
        )
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.TEXT_XML_VALUE))
            .andExpect(content().xml(expectedResponseBody))
    }

    @Test
    fun testRetrieveVoiceMessageReport() {

        every { pmxMessageService.getById("100") } returns testPMXMessageForVoiceReport()

        val expectedResponse = """
             <Report>
               <VT>
                 <UNQID>100</UNQID>
                 <JobID>59akd871-864d-4d32-8f1d-7a07d2ef449a</JobID>
                 <PhoneNumber>12125157878</PhoneNumber>
                 <Duration/>
                 <Rate/>
                 <Cost/>
                 <Status>Sent</Status>
                 <Error/>              
                 <DeliveryMethod>Voice Mail</DeliveryMethod>
                 <KeyPress>1</KeyPress>
                 <Timestamp>10/20/2020 09:19:23 AM</Timestamp>
               </VT>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.VOICE, "100", expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.getById("100")
        }

    }

    @Test
    fun testRetrieveEmailMessageReport() {
        every { pmxMessageService.getById("200") } returns testPMXMessageForEmailReport()

        val expectedResponse = """
             <Report>
             	<ET>
             		<formid>200</formid>
             		<unqid>200</unqid>
             		<orderid>5228871-864d-4d32-8f1d-7a07d2ef449a</orderid>
             		<Project></Project>
             		<EmailAddress><EMAIL></EmailAddress>
             		<OpenCount></OpenCount>
             		<LastOpened></LastOpened>
             		<jobstatus>Sent</jobstatus>
             		<result></result>
             		<error></error>
             		<timestamp>09/17/2020 03:12:15 PM</timestamp>
             	</ET>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.EMAIL, "200", expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.getById("200")
        }

    }

    @Test
    fun testRetrieveBouncedEmailMessageReport() {
        every { pmxMessageService.getById("201") } returns testBouncedPMXMessageForEmailReport()

        val expectedResponse = """
             <Report>
             	<ET>
             		<formid>201</formid>
             		<unqid>201</unqid>
             		<orderid>5228871-864d-4d32-8f1d-7a07d2ef449a</orderid>
             		<Project></Project>
             		<EmailAddress><EMAIL></EmailAddress>
             		<OpenCount></OpenCount>
             		<LastOpened></LastOpened>
             		<jobstatus>FAILED</jobstatus>
             		<result></result>
             		<error>title: HARD_BOUNCE
             detail: Permanent/OnAccountSuppressionList</error>
             		<timestamp>09/17/2020 03:12:15 PM</timestamp>
             	</ET>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.EMAIL, "201", expectedResponse)

    }

    @Test
    fun testRetrieveDelayedEmailMessageReport() {
        every { pmxMessageService.getById("202") } returns testDelayedPMXMessageForEmailReport()

        val expectedResponse = """
             <Report>
             	<ET>
             		<formid>202</formid>
             		<unqid>202</unqid>
             		<orderid>5228871-864d-4d32-8f1d-7a07d2ef449a</orderid>
             		<Project></Project>
             		<EmailAddress><EMAIL></EmailAddress>
             		<OpenCount></OpenCount>
             		<LastOpened></LastOpened>
             		<jobstatus>FAILED</jobstatus>
             		<result></result>
             		<error>title: COMMUNICATION_FAILURE
             detail: TransientCommunicationFailure</error>
             		<timestamp>09/17/2020 03:12:15 PM</timestamp>
             	</ET>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.EMAIL, "202", expectedResponse)

    }

    @Test
    fun testRetrieveRejectedEmailMessageReport() {
        every { pmxMessageService.getById("203") } returns testRejectedPMXMessageForEmailReport()

        val expectedResponse = """
             <Report>
             	<ET>
             		<formid>203</formid>
             		<unqid>203</unqid>
             		<orderid>5228871-864d-4d32-8f1d-7a07d2ef449a</orderid>
             		<Project></Project>
             		<EmailAddress><EMAIL></EmailAddress>
             		<OpenCount></OpenCount>
             		<LastOpened></LastOpened>
             		<jobstatus>FAILED</jobstatus>
             		<result></result>
             		<error>title: CONTENT_REJECTED
             detail: Email rejected</error>
             		<timestamp>09/17/2020 03:12:15 PM</timestamp>
             	</ET>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.EMAIL, "203", expectedResponse)

    }

    @Test
    @Disabled
    fun testCreateEBReport() {
        every { pmxMessageService.getById("300") } returns testPMXMessageWithForEBReport()

        val expectedResponse = """
            <Report>
                <EB>
                     <OrderId>5711918</OrderId>
                     <Project>Broadcast</Project>
                     <EmailAddress><EMAIL></EmailAddress>
                     <OpenCount></OpenCount>
                     <LastOpened></LastOpened>
                     <JobStatus>Sent</JobStatus>
                     <Result></Result>
                     <Error></Error>
                     <Timestamp>09/15/2019 02:15:10 PM</Timestamp>
                     <Email><EMAIL></Email>
                     <Name></Name>
                </EB>
                <EB>
                     <OrderId>5711918</OrderId>
                     <Project>Broadcast</Project>
                     <EmailAddress><EMAIL></EmailAddress>
                     <OpenCount></OpenCount>
                     <LastOpened></LastOpened>
                     <JobStatus>Sent</JobStatus>
                     <Result></Result>
                     <Error></Error>
                     <Timestamp>09/15/2019 02:15:10 PM</Timestamp>
                     <Email><EMAIL></Email>
                     <Name></Name>
                </EB>                               
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.VOICE, "300", expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.getById("300")
        }

    }

    @Test
    fun testRetrieveTextMessageReport() {
        every { pmxMessageService.getById("400") } returns testPMXMessageForTextReport()

        val expectedResponse = """
             <Report>
             	<MT>
             		<OrderID>995Ad877-864d-4d32-8f1d-7a07d2ef449a</OrderID>
             		<UNQID>400</UNQID>
             		<Project></Project>
             		<CellPhoneNumber>12725137978</CellPhoneNumber>
             		<Jobstatus>Sent</Jobstatus>
             		<Result></Result>
             		<Error></Error>
             	</MT>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.SMS, "400", expectedResponse)

        verifySequence {
            customerService.authenticate(username, password)
            pmxMessageService.getById("400")
        }

    }

    @Test
    fun testRetrieveFailedTextMessageReport() {
        every { pmxMessageService.getById("401") } returns testFailedPMXMessageForTextReport()

        val expectedResponse = """
             <Report>
             	<MT>
             		<OrderID>995Ad877-864d-4d32-8f1d-7a07d2ef449a</OrderID>
             		<UNQID>401</UNQID>
             		<Project></Project>
             		<CellPhoneNumber>12725137978</CellPhoneNumber>
             		<Jobstatus>FAILED</Jobstatus>
             		<Result></Result>
             		<Error>title: Unexpected error
             code: 10007
             detail: An unexpected error occurred.</Error>
             	</MT>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.SMS, "401", expectedResponse)

    }

    @Test
    fun testRetrieveFailedTextMessageFromLogsReport() {
        every { pmxMessageService.getById("402") } returns testFailedPMXMessageFromLogsForTextReport()

        val expectedResponse = """
             <Report>
             	<MT>
             		<OrderID>995Ad877-864d-4d32-8f1d-7a07d2ef449a</OrderID>
             		<UNQID>402</UNQID>
             		<Project></Project>
             		<CellPhoneNumber>12725137978</CellPhoneNumber>
             		<Jobstatus>FAILED</Jobstatus>
             		<Result></Result>
             		<Error>title: Not routable
             code: 40001
             detail: The destination number is either a landline or a non-routable wireless number.</Error>
             	</MT>
             </Report>
        """.trimIndent()

        retrieveReport(MessageType.SMS, "402", expectedResponse)

    }
}