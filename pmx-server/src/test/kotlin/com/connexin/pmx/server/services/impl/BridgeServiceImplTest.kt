package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.*
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import kong.unirest.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Value
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.util.ReflectionTestUtils

@TestPropertySource(locations = ["classpath:application-test.yml"])
@ActiveProfiles("test")
@ExtendWith(MockKExtension::class, SpringExtension:: class)
class BridgeServiceImplTest {
    @MockK
    lateinit var unirestInstance: UnirestInstance

    @MockK
    lateinit var httpRequestWithBody: HttpRequestWithBody

    @MockK
    lateinit var requestBodyEntity: RequestBodyEntity

    @Value("\${op.bridge.url}")
    private lateinit var bridgeUrl: String

    @Value("\${op.bridge.paths.patient-checkin-generate-url-path}")
    private lateinit var patientCheckinGenerateLinkPath: String

    private lateinit var sut: BridgeServiceImpl

    @BeforeEach
    fun setup() {
        sut = BridgeServiceImpl(unirestInstance)
        ReflectionTestUtils.setField(sut, "bridgeUrl", bridgeUrl)
        ReflectionTestUtils.setField(sut, "patientGenerateCheckinPath", patientCheckinGenerateLinkPath)
    }

    @Test
    fun `Generate bridge checkin unsuccessful response`() {
        val opmedId = "123"
        val patientLocalId = "12"
        val appointmentLocalId = "23"
        val contactLocalId = "45"

        every { unirestInstance.post(any()) } returns httpRequestWithBody
        every { httpRequestWithBody.headers(
            match { map ->
                map[Constants.X_OPMED] == opmedId
            }
        )} returns httpRequestWithBody
        every { httpRequestWithBody.body(
            match { body: GenerateCheckInUrlRequestDto ->
                body.patientLocalId == patientLocalId && body.appointmentLocalId == appointmentLocalId && body.contactLocalId == contactLocalId
            }
        ) } returns requestBodyEntity
        every { requestBodyEntity.asString() } returns FailedResponse(Exception("Test failure"))

        val exception = assertThrows<Exception> {
            sut.generatePatientCheckinUrl(appointmentLocalId, patientLocalId, contactLocalId, opmedId)
        }

        assertThat(exception.message).contains("Could not generate check-in link url")
    }

    @Test
    fun `Generate bridge checkin successful response`() {
        val opmedId = "123"
        val patientLocalId = "12"
        val appointmentLocalId = "23"
        val contactLocalId = "45"

        val response = "checkinUrl"
        every { unirestInstance.post(any()) } returns httpRequestWithBody
        every { httpRequestWithBody.headers(
            match { map ->
                map[Constants.X_OPMED] == opmedId
            }
        )} returns httpRequestWithBody
        every { httpRequestWithBody.body(
            match { body: GenerateCheckInUrlRequestDto ->
                body.patientLocalId == patientLocalId && body.appointmentLocalId == appointmentLocalId && body.contactLocalId == contactLocalId
            }
        ) } returns requestBodyEntity
        every { requestBodyEntity.asString() } returns BasicResponse(MockRawResponse(response, Headers(), 200, "OK", Config()), response)

        val actualResponse = sut.generatePatientCheckinUrl(appointmentLocalId, patientLocalId, contactLocalId, opmedId)

        assertThat(actualResponse).isEqualTo(response)
    }
}