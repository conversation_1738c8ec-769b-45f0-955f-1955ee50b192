package com.connexin.pmx.server.services.impl

import com.amazonaws.services.simpleemailv2.AmazonSimpleEmailServiceV2
import com.amazonaws.services.simpleemailv2.model.*
import com.connexin.pmx.server.models.BulkEmailMessage
import com.connexin.pmx.server.models.EmailMessage
import com.connexin.pmx.server.services.RateLimiters
import io.github.resilience4j.ratelimiter.RateLimiterRegistry
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verifyOrder
import org.assertj.core.api.Assertions.assertThat
import org.json.JSONObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Duration

@ExtendWith(MockKExtension::class)
internal class SesEmailServiceImplTest {

    @MockK
    private lateinit var emailService: AmazonSimpleEmailServiceV2

    private lateinit var sut: SesEmailServiceImpl

    @BeforeEach
    fun setup() {
        sut = SesEmailServiceImpl(
            emailService, "<EMAIL>", "pmx", RateLimiters(RateLimiterRegistry.ofDefaults())
        )
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `sendBulkEmail should include unsubscribe link`() {
        val createTemplateRequest = CreateEmailTemplateRequest()
            .withTemplateName("test-template")
            .withTemplateContent(
                EmailTemplateContent()
                    .withSubject("test subject")
                    .withHtml(
                        "<p>Dear {{Name}}</p>" +
                                "<p>This is a test!</p>" +
                                "<p>If you would prefer not to receive further messages from this sender, " +
                                "please <a href=\"{{UnsubscribeLink}}\">click here</a> and confirm your request.</p>"
                    )
            )
        val deleteTemplateRequest = DeleteEmailTemplateRequest().withTemplateName("test-template")
        val sendBulkEmailRequest = SendBulkEmailRequest()
            .withConfigurationSetName("pmx")
            .withFromEmailAddress("<EMAIL>")
            .withDefaultContent(
                BulkEmailContent()
                    .withTemplate(
                        Template()
                            .withTemplateName("test-template")
                            .withTemplateData("{\"Name\":\"Patient\"}")
                    )
            )
            .withBulkEmailEntries(
                BulkEmailEntry()
                    .withDestination(Destination().withToAddresses("<EMAIL>"))
                    .withReplacementEmailContent(
                        ReplacementEmailContent()
                            .withReplacementTemplate(
                                ReplacementTemplate()
                                    .withReplacementTemplateData(
                                        JSONObject(
                                            mapOf(
                                                "Name" to "Patient 1",
                                                "UnsubscribeLink" to "http://op.test/unsubscribe?id=1&email=<EMAIL>"
                                            )
                                        ).toString()
                                    )
                            )
                    ),
                BulkEmailEntry()
                    .withDestination(Destination().withToAddresses("<EMAIL>"))
                    .withReplacementEmailContent(
                        ReplacementEmailContent()
                            .withReplacementTemplate(
                                ReplacementTemplate()
                                    .withReplacementTemplateData(
                                        JSONObject(
                                            mapOf(
                                                "Name" to "Patient 2",
                                                "UnsubscribeLink" to "http://op.test/unsubscribe?id=1&email=<EMAIL>"
                                            )
                                        ).toString()
                                    )
                            )
                    )
            )
        every { emailService.getAccount(any()) } returns GetAccountResult().withSendQuota(
            SendQuota().withMax24HourSend(
                10.0
            ).withMaxSendRate(5.0)
        )
        every { emailService.createEmailTemplate(createTemplateRequest) } returns CreateEmailTemplateResult()
        every { emailService.deleteEmailTemplate(deleteTemplateRequest) } returns DeleteEmailTemplateResult()
        every { emailService.sendBulkEmail(sendBulkEmailRequest) } returns SendBulkEmailResult().withBulkEmailEntryResults(
            BulkEmailEntryResult().withMessageId("1").withStatus(BulkEmailStatus.SUCCESS),
            BulkEmailEntryResult().withMessageId("2").withStatus(BulkEmailStatus.SUCCESS)
        )

        sut.sendBulkEmail(
            BulkEmailMessage(
                template = BulkEmailMessage.Template(
                    name = "test-template",
                    subject = "test subject",
                    htmlContent = "<p>Dear {{Name}}</p>" +
                            "<p>This is a test!</p>",
                    defaultReplacementValues = JSONObject(mapOf("Name" to "Patient"))
                ),
                entries = listOf(
                    BulkEmailMessage.Entry(
                        toAddress = "<EMAIL>",
                        unsubscribeLink = "http://op.test/unsubscribe?id=1&email=<EMAIL>",
                        replacementValues = JSONObject(mapOf("Name" to "Patient 1"))
                    ),
                    BulkEmailMessage.Entry(
                        toAddress = "<EMAIL>",
                        unsubscribeLink = "http://op.test/unsubscribe?id=1&email=<EMAIL>",
                        replacementValues = JSONObject(mapOf("Name" to "Patient 2"))
                    )
                )
            )
        )

        verifyOrder {
            emailService.createEmailTemplate(createTemplateRequest)
            emailService.sendBulkEmail(sendBulkEmailRequest)
            emailService.deleteEmailTemplate(deleteTemplateRequest)
        }
    }

    @Test
    fun `sendEmail should fail once send rate limit is reached`() {
        every { emailService.getAccount(any()) } answers {
            GetAccountResult().withSendQuota(SendQuota().withMax24HourSend(10.0).withMaxSendRate(5.0))
        }
        every { emailService.sendEmail(any()) } answers {
//            Thread.sleep(50) // a response delay of 50m
            SendEmailResult().withMessageId("SUCCESS")
        }

        val emailMessage = EmailMessage(
            subject = "test",
            body = "test",
            replyToAddress = "<EMAIL>",
            toAddresses = mutableSetOf("<EMAIL>")
        )

        // first 5 calls should succeed
        for (i in 1..5) {
            val result = sut.sendEmail(emailMessage)
            assertThat(result.success).isTrue
        }

        // 6th call exceeds our per second limit
        val actual = sut.sendEmail(emailMessage)
        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(actual.retryDelay).isEqualTo(Duration.ofSeconds(1))
    }

    @Test
    fun `sendEmail should fail once daily limit is reached`() {
        every { emailService.getAccount(any()) } answers {
            GetAccountResult().withSendQuota(SendQuota().withMax24HourSend(10.0).withMaxSendRate(5.0))
        }
        every { emailService.sendEmail(any()) } answers {
            SendEmailResult().withMessageId("SUCCESS")
        }

        val emailMessage = EmailMessage(
            subject = "test",
            body = "test",
            replyToAddress = "<EMAIL>",
            toAddresses = mutableSetOf("<EMAIL>")
        )

        for (i in 1..5) {
            val result = sut.sendEmail(emailMessage)
            assertThat(result.success).isTrue
        }
        Thread.sleep(1000)

        for (i in 1..5) {
            val result = sut.sendEmail(emailMessage)
            assertThat(result.success).isTrue
        }
        Thread.sleep(1000)

        // 11th call exceeds our daily limit
        val actual = sut.sendEmail(emailMessage)
        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(actual.retryDelay).isEqualTo(Duration.ofHours(1))
    }

    @Test
    fun `sendEmail should apply rate limit changes`() {
        every { emailService.getAccount(any()) } returns
                GetAccountResult().withSendQuota(SendQuota().withMax24HourSend(10.0).withMaxSendRate(2.0)) andThen
                GetAccountResult().withSendQuota(SendQuota().withMax24HourSend(10.0).withMaxSendRate(1.0))
        every { emailService.sendEmail(any()) } answers {
            SendEmailResult().withMessageId("SUCCESS")
        }

        val emailMessage = EmailMessage(
            subject = "test",
            body = "test",
            replyToAddress = "<EMAIL>",
            toAddresses = mutableSetOf("<EMAIL>")
        )

        // after the first call the limit will drop from 2 to 1, but new limit won't apply until refresh period elapses
        assertThat(sut.sendEmail(emailMessage).success).isTrue
        assertThat(sut.sendEmail(emailMessage).success).isTrue
        // third call exceeds the previous per second limit of 2
        assertThat(sut.sendEmail(emailMessage).success).isFalse
        Thread.sleep(1000) // allow refresh period to complete

        // limit change goes into effect
        assertThat(sut.sendEmail(emailMessage).success).isTrue
        // second call exceeds the new per second limit of 1
        assertThat(sut.sendEmail(emailMessage).success).isFalse
    }

    @Test
    fun `sendBulkEmail should send in batch sizes equal to the allowed send rate`() {
        // arrange
        every { emailService.getAccount(any()) } returns
                GetAccountResult().withSendQuota(SendQuota().withMax24HourSend(100.0).withMaxSendRate(5.0))
        every { emailService.createEmailTemplate(any()) } returns CreateEmailTemplateResult()
        every { emailService.deleteEmailTemplate(any()) } returns DeleteEmailTemplateResult()
        every { emailService.sendBulkEmail(any()) } returns
                // 1
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (0..4).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("SUCCESS") }
                ) andThen
                // 2
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (5..9).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("SUCCESS") }
                ) andThen
                // 3
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (10..14).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("SUCCESS") }
                ) andThen
                // 4
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (15..19).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("SUCCESS") }
                ) andThen
                // 5
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (20..24).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("SUCCESS") }
                ) andThen
                // 6
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (25..29).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("ACCOUNT_THROTTLED") }
                ) andThen
                // 7
                SendBulkEmailResult().withBulkEmailEntryResults(
                    (30..34).map { BulkEmailEntryResult().withMessageId(it.toString()).withStatus("ACCOUNT_DAILY_QUOTA_EXCEEDED") }
                )

        var currentBatch = -1

        // act
        val actual = sut.sendBulkEmail(BulkEmailMessage(
            template = BulkEmailMessage.Template(name = "test", subject = "test", htmlContent = "test"),
            entries = (0..34).map { BulkEmailMessage.Entry(toAddress = "patient+$<EMAIL>") }
        )) { batchEntryResults ->
            currentBatch++
            batchEntryResults.forEachIndexed { index, entryResult ->
                // assert entries
                val id = index + (5 * currentBatch)
                assertThat(entryResult.messageId).isEqualTo(id.toString())
                assertThat(entryResult.toAddress).isEqualTo("patient+$<EMAIL>")
                assertThat(entryResult.success).isEqualTo(currentBatch < 5)
                assertThat(entryResult.retry).isEqualTo(currentBatch >= 5)
                assertThat(entryResult.retryDelay).isEqualTo(when (currentBatch) {
                    5 -> Duration.ofSeconds(1)
                    6 -> Duration.ofHours(1)
                    else -> null
                })
            }
        }

        // assert result
        assertThat(actual.success).isFalse
        assertThat(actual.entryResults).hasSize(35)
    }
}