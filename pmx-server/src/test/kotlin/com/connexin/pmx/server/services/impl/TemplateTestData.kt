package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import java.time.*
import java.time.temporal.ChronoUnit

object TemplateTestData {
    val appointmentDateLocal: LocalDateTime = LocalDateTime.of(2023, 3, 16, 13, 45)
    val appointmentDate: Instant = Instant.from(
        OffsetDateTime.of(
            appointmentDateLocal, ZoneId.of("US/Eastern").rules.getOffset(
                appointmentDateLocal
            )
        )
    )

    val legacyConfirmationTemplate = Template(
        id = "confirmation_legacy",
        name = "Default Confirmation",
        workflow = EngagementWorkflow.CONFIRMATION,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            ),
            MessageType.VOICE to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            )
        )
    )
    val confirmationTemplate = Template(
        id = "confirmation",
        name = "Default Confirmation",
        workflow = EngagementWorkflow.CONFIRMATION,
        scenarios = mapOf(
            TemplateScenario.DEFAULT to mapOf(
                MessageType.SMS to mapOf(
                    Language.ENGLISH to Template.Segments(
                        main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                    ),
                    Language.SPANISH to Template.Segments(
                        main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                    )
                ),
                MessageType.VOICE to mapOf(
                    Language.ENGLISH to Template.Segments(
                        main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                    ),
                    Language.SPANISH to Template.Segments(
                        main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                    )
                ),
                MessageType.EMAIL to mapOf(
                    Language.ENGLISH to Template.Segments(
                        main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                    ),
                    Language.SPANISH to Template.Segments(
                        main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                    )
                )
            ),
            TemplateScenario.CONFIRMATION_DEADLINE_PASSED to mapOf(
                MessageType.SMS to mapOf(
                    Language.ENGLISH to Template.Segments(
                        main = "Confirmation window passed. Call <Location_Telephone>"
                    ),
                    Language.SPANISH to Template.Segments(
                        main = "El tiempo para confirmar su cita ha termindo. Llame a la practica <Location_Telephone>"
                    )
                )
            )
        )
    )
    val reminderTemplate = Template(
        id = "reminder",
        name = "Default Reminder",
        workflow = EngagementWorkflow.REMINDER,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            ),
            MessageType.VOICE to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>"
                )
            )
        )
    )
    val internalSurveyLinkTemplate = Template(
        id = "internal-survey",
        name = "Default Internal Survey",
        workflow = EngagementWorkflow.APPT_SURVEY_INTERNAL,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name>, has a upcoming appointment is on <Appointment_Day_Date> at <Appointment_Time> at <Appointment_Location>. Please take a moment to complete this survey: Copy or record pin code <Pin_Code> because it is required to access the survey. Click link below and enter your pin code. <Survey_Link> Text STOP to opt out."
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name>, tiene una próxima cita el <Appointment_Day_Date> a las <Appointment_Time> en <Appointment_Location>. Por favor, tómese un momento para completar esta encuesta: Copiar o grabar el código PIN <Pin_Code> porque es necesario para acceder a la encuesta. Haga clic en el enlace a continuación e ingrese su código PIN. <Survey_Link> Envíe un mensaje con la palabra STOP para cancelar la suscripción."
                )
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "Hello <Patient_First_Name>,\n" +
                            "\n" +
                            "Has an upcoming appointment with <Provider> is scheduled for <Appointment_Day_Date> at <Appointment_Time> at <Appointment_Location>, <Location_Address>.\n" +
                            "\n" +
                            "To help us streamline your visit and save you time, please take a moment to complete the survey assigned for this visit: Copy or record pin code <Pin_Code> because it is required to access the survey. Click link below and enter your pin code.  \n" +
                            "<Survey_Link>\n" +
                            "\n" +
                            "Need to reschedule? Please contact the office at <Location_Telephone>.\n" +
                            "\n" +
                            "We look forward to seeing you soon!"
                ),
                Language.SPANISH to Template.Segments(
                    main = "Hola <Patient_First_Name>,\n" +
                            "\n" +
                            "Tiene una próxima cita con <Provider> programada para el <Appointment_Day_Date> a las <Appointment_Time> en <Appointment_Location>, <Location_Address>.\n" +
                            "\n" +
                            "Para ayudarnos a agilizar su visita y ahorrar tiempo, por favor tómese un momento para completar la encuesta asignada para esta cita: Copiar o grabar el código PIN <Pin_Code> porque es necesario para acceder a la encuesta. Haga clic en el enlace a continuación e ingrese su código PIN.   \n" +
                            "<Survey_Link>\n" +
                            "\n" +
                            "¿Necesita reprogramar? Por favor comuníquese con la oficina al <Location_Telephone>.\n" +
                            "\n" +
                            "¡Esperamos verle pronto!"
                )
            )
        )
    )
    val externalSurveyTemplate = Template(
        id = "external-survey",
        name = "Default External Survey",
        workflow = EngagementWorkflow.APPT_SURVEY_EXTERNAL,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "<Patient_First_Name>, has a upcoming appointment is on <Appointment_Day_Date> at <Appointment_Time> at <Appointment_Location>. Please take a moment to complete this survey: Click the link below to complete the survey\n\n <Survey_Link> Text STOP to opt out."
                ),
                Language.SPANISH to Template.Segments(
                    main = "<Patient_First_Name>, tiene una próxima cita el <Appointment_Day_Date> a las <Appointment_Time> en <Appointment_Location>. Por favor, tómese un momento para completar esta encuesta: Haga clic en el enlace a continuación para completar la encuesta.\n\n <Survey_Link> Envíe un mensaje con la palabra STOP para cancelar la suscripción."
                )
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(
                    main = "Hello <Patient_First_Name>,\n" +
                            "\n" +
                            "Has an upcoming appointment with <Provider> is scheduled for <Appointment_Day_Date> at <Appointment_Time> at <Appointment_Location>, <Location_Address>.\n" +
                            "\n" +
                            "To help us streamline your visit and save you time, please take a moment to complete the survey assigned for this visit: Click the link below to complete the survey  \n" +
                            "<Survey_Link>\n" +
                            "\n" +
                            "Need to reschedule? Please contact the office at <Location_Telephone>.\n" +
                            "\n" +
                            "We look forward to seeing you soon!"
                ),
                Language.SPANISH to Template.Segments(
                    main = "Hola <Patient_First_Name>,\n" +
                            "\n" +
                            "Tiene una próxima cita con <Provider> programada para el <Appointment_Day_Date> a las <Appointment_Time> en <Appointment_Location>, <Location_Address>.\n" +
                            "\n" +
                            "Para ayudarnos a agilizar su visita y ahorrar tiempo, por favor tómese un momento para completar la encuesta asignada para esta cita: Haga clic en el enlace a continuación para completar la encuesta. \n" +
                            "<Survey_Link>\n" +
                            "\n" +
                            "¿Necesita reprogramar? Por favor comuníquese con la oficina al <Location_Telephone>.\n" +
                            "\n" +
                            "¡Esperamos verle pronto!"
                )
            )
        )
    )
    val cancellationTemplate = Template(
        id = "cancellation",
        name = "Default Cancellation",
        workflow = EngagementWorkflow.CANCELLATION,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            ),
            MessageType.VOICE to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            )
        )
    )
    val checkinTemplate = Template(
        id = "checkin",
        name = "Default Check-In",
        workflow = EngagementWorkflow.CHECKIN,
        variations = mapOf(
            MessageType.SMS to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            ),
            MessageType.VOICE to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            ),
            MessageType.EMAIL to mapOf(
                Language.ENGLISH to Template.Segments(main = "<Patient_First_Name> has an appt <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Please confirm or cancel HERE <Confirmation_Link>"),
                Language.SPANISH to Template.Segments(main = "<Patient_First_Name> tiene una cita <Appointment_Day_Date> @ <Appointment_Time>, <Practice_Name> <Appointment_Location>.  Por favor, confirme o cancele AQUÍ <Confirmation_Link>")
            )
        )
    )
    val crappyTemplate = Template(
        id = "crappy",
        name = "Crappy Check-In",
        workflow = EngagementWorkflow.CHECKIN,
        variations = emptyMap()
    )
    val customer = Customer(
        id = "test1",
        name = "Test",
        status = CustomerStatus.ENABLED,
        appointmentTimeDisplayOffset = Duration.ofMinutes(20),
        engagementRules = mutableSetOf(
            EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateIds = mapOf(TemplateScenario.DEFAULT to legacyConfirmationTemplate.id!!)
            ),
            EngagementRule(
                id = "2",
                workflow = EngagementWorkflow.REMINDER,
                templateIds = mapOf(TemplateScenario.DEFAULT to reminderTemplate.id!!)
            ),
            EngagementRule(
                id = "3",
                workflow = EngagementWorkflow.CANCELLATION,
                templateIds = mapOf(TemplateScenario.DEFAULT to cancellationTemplate.id!!)
            ),
            EngagementRule(
                id = "4",
                workflow = EngagementWorkflow.CHECKIN,
                templateIds = mapOf(TemplateScenario.DEFAULT to checkinTemplate.id!!)
            ),
            EngagementRule(
                id = "5",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateId = confirmationTemplate.id
            ),
            EngagementRule(
                id = "6",
                workflow = EngagementWorkflow.APPT_SURVEY_EXTERNAL,
                templateId = internalSurveyLinkTemplate.id
            ),
            EngagementRule(
                id = "7",
                workflow = EngagementWorkflow.APPT_SURVEY_INTERNAL,
                templateId = externalSurveyTemplate.id
            ),
        )
    )

    val engagementRulesMap = customer.engagementRules.associateBy { it.workflow }

    val engagement = Engagement(
        id = "test",
        customerId = customer.id!!,
        eventDate = appointmentDate,
        nextCheckpoint = appointmentDate,
        resources = mutableSetOf(
            PracticeResource(
                id = "practice1",
                name = "Practice One"
            ),
            LocationResource(
                id = "location1",
                name = "Location One",
                address = "123 Main St",
                practiceId = "practice1",
                phone = "************",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            StaffResource(
                id = "staff1",
                name = "Dr Staff 1"
            ),
            PatientResource(
                id = "patient1",
                givenName = "Patient 1",
                familyName = "Test"
            ),
            PatientResource(
                id = "patient2",
                givenName = "Patient 2",
                familyName = "Test"
            ),
            ContactResource(
                id = "contact1",
                givenName = "English Email Contact",
                familyName = "Test",
                contactMethod = ContactMethod.EMAIL,
                language = Language.ENGLISH,
                email = "<EMAIL>",
                phone = null
            ),
            ContactResource(
                id = "contact2",
                givenName = "Spanish Email Contact",
                familyName = "Test",
                contactMethod = ContactMethod.EMAIL,
                language = Language.ENGLISH,
                email = "<EMAIL>",
                phone = null
            ),
            ContactResource(
                id = "contact3",
                givenName = "English SMS Contact",
                familyName = "Test",
                contactMethod = ContactMethod.SMS,
                language = Language.ENGLISH,
                email = null,
                phone = "+14405550003"
            ),
            ContactResource(
                id = "contact4",
                givenName = "Spanish SMS Contact",
                familyName = "Test",
                contactMethod = ContactMethod.SMS,
                language = Language.SPANISH,
                email = null,
                phone = "+14405550004"
            ),
            ContactResource(
                id = "contact5",
                givenName = "English Voice Contact",
                familyName = "Test",
                contactMethod = ContactMethod.VOICE,
                language = Language.ENGLISH,
                email = null,
                phone = "+14405550005"
            ),
            ContactResource(
                id = "contact6",
                givenName = "Spanish Voice Contact",
                familyName = "Test",
                contactMethod = ContactMethod.VOICE,
                language = Language.SPANISH,
                email = null,
                phone = "+14405550006"
            ),
            AppointmentResource(
                id = "appointment1",
                startTime = appointmentDate,
                reason = "Test 1",
                location = "location1",
                staff = "staff1",
                patient = "patient1",
                practice = "practice1",
                appointmentType = "apptType1"
            ),
            AppointmentResource(
                id = "appointment2",
                startTime = appointmentDate.plus(30, ChronoUnit.MINUTES),
                reason = "Test 2",
                location = "location1",
                staff = "staff1",
                patient = "patient2",
                practice = "practice1",
                appointmentType = "apptType1"
            ),
        )
    )
}