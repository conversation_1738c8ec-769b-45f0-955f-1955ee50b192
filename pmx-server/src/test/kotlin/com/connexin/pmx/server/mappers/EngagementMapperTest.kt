package com.connexin.pmx.server.mappers

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.ErrorResponse
import com.connexin.pmx.server.models.dtos.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import java.time.Instant

@SpringBootTest
@Import(TestConfig::class)
@ActiveProfiles("test")
class EngagementMapperTest {

    @Autowired
    private lateinit var sut: EngagementMapper

    private val appointment = AppointmentResource(
        id = "appointment",
        startTime = Instant.now(),
        reason = "test",
        location = "test",
        staff = "test",
        patient = "test",
        appointmentType = "test",
        practice = "test"
    )

    private val contact = ContactResource(
        id = "contact",
        familyName = "test",
        givenName = "test",
        email = "test",
        phone = "test",
        contactMethod = ContactMethod.SMS,
        language = Language.ENGLISH
    )

    @Test
    fun `mapToDto should map ErrorResponse`() {
        val expected = ErrorResponse(
            id = "error",
            occurredAt = Instant.now(),
            customerId = "customer",
            engagementId = "engagement",
            respondents = setOf(contact),
            appointments = setOf(appointment),
            errors = listOf(
                ErrorDto(path = "test", message = "test", errorCode = 1)
            )
        )

        val actual = sut.mapToDto(expected)

        assertThat(actual)
            .isInstanceOf(ErrorResponseDto::class.java)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }

    @Test
    fun `mapToDto should map ConfirmationResponse`() {
        val expected = ConfirmationResponse(
            id = "confirmation",
            occurredAt = Instant.now(),
            customerId = "customer",
            engagementId = "engagement",
            respondents = setOf(contact),
            appointments = setOf(appointment),
            result = ConfirmationStatus.CONFIRMED,
            isFinal = true,
            messageId = "test"
        )

        val actual = sut.mapToDto(expected)

        assertThat(actual)
            .isInstanceOf(ConfirmationResponseDto::class.java)
            .usingRecursiveComparison()
            .ignoringFields("confirmationStatus")
            .isEqualTo(expected)
        assertThat(actual.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
    }

    @Test
    fun `mapToDto should map CompleteResponse`() {
        val expected = CompleteResponse(
            id = "complete",
            occurredAt = Instant.now(),
            customerId = "customer",
            engagementId = "engagement",
            respondents = setOf(contact),
            appointments = setOf(appointment),
        )

        val actual = sut.mapToDto(expected)

        assertThat(actual)
            .isInstanceOf(CompleteResponseDto::class.java)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }

    @Test
    fun `mapToDto should map MessageResponse`() {
        val expected = MessageResponse(
            id = "message",
            occurredAt = Instant.now(),
            customerId = "customer",
            engagementId = "engagement",
            respondents = setOf(contact),
            appointments = setOf(appointment),
            messageId = "message",
            workflow = EngagementWorkflow.CONFIRMATION,
            status = MessageStatus.FAILED,
            errors = listOf(ErrorDto("/test", message = "Test", Errors.INVALID_EMAIL_OR_PHONE.code)),
            message = "message body",
            altMessage = "alt message body",
            subject = "subject"
        )

        val actual = sut.mapToDto(expected)

        assertThat(actual)
            .isInstanceOf(MessageResponseDto::class.java)
            .usingRecursiveComparison()
            .isEqualTo(expected)
    }

    @Test
    fun `mapToDto should map CancellationResponse`() {
        val expected = CancellationResponse(
            id = "confirmation",
            occurredAt = Instant.now(),
            customerId = "customer",
            engagementId = "engagement",
            respondents = setOf(contact),
            appointments = setOf(appointment),
            result = CancellationStatus.CANCELLED
        )

        val actual = sut.mapToDto(expected)

        assertThat(actual)
            .isInstanceOf(CancellationResponseDto::class.java)
            .usingRecursiveComparison()
            .ignoringFields("cancellationStatus")
            .isEqualTo(expected)
        assertThat(actual.cancellationStatus).isEqualTo(CancellationStatus.CANCELLED)
    }
}