package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateOrEditTemplateRequest
import com.connexin.pmx.server.repositories.TemplateRepository
import com.connexin.pmx.server.services.UrlGenerator
import com.connexin.pmx.server.services.impl.TemplateTestData.appointmentDate
import com.connexin.pmx.server.services.impl.TemplateTestData.appointmentDateLocal
import com.connexin.pmx.server.services.impl.TemplateTestData.customer
import com.connexin.pmx.server.services.impl.TemplateTestData.engagement
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.impl.annotations.SpyK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verifySequence
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

@ExtendWith(MockKExtension::class)
class TemplateServiceImplTests {
    @RelaxedMockK
    lateinit var repository: TemplateRepository

    @SpyK
    var urlGenerator = UrlGenerator("http://localhost:8080", "http://localhost:8080")

    @InjectMockKs
    lateinit var sut: TemplateServiceImpl

    @BeforeEach
    fun setup() {
        every { urlGenerator.confirm(any()) } answers { callOriginal() }
        every { repository.getByIdOrNull(TemplateTestData.legacyConfirmationTemplate.id!!) } returns TemplateTestData.legacyConfirmationTemplate
        every { repository.getByIdOrNull(TemplateTestData.confirmationTemplate.id!!) } returns TemplateTestData.confirmationTemplate
        every { repository.getByIdOrNull(TemplateTestData.reminderTemplate.id!!) } returns TemplateTestData.reminderTemplate
        every { repository.getByIdOrNull(TemplateTestData.cancellationTemplate.id!!) } returns TemplateTestData.cancellationTemplate
        every { repository.getByIdOrNull(TemplateTestData.checkinTemplate.id!!) } returns TemplateTestData.checkinTemplate
        every { repository.getByIdOrNull(TemplateTestData.crappyTemplate.id!!) } returns TemplateTestData.crappyTemplate
    }

    @Test
    fun `update legacy should update and return successfully`() {
        val existing = Template(
            name = "old",
            workflow = EngagementWorkflow.CONFIRMATION,
            variations = emptyMap()
        )
        val expected = CreateOrEditTemplateRequest(
            name = "Test",
            workflow = EngagementWorkflow.CHECKIN,
            variations = mapOf(
                MessageType.SMS to mapOf(
                    Language.ENGLISH to Template.Segments(main = "test")
                )
            )
        )

        every { repository.findByIdOrNull("test") } returns existing
        every { repository.save(any()) } answers { firstArg() }

        val actual = sut.update("test", expected)

        assertThat(actual.success).isTrue
        assertThat(actual.get().name).isEqualTo(expected.name)
        assertThat(actual.get().workflow).isEqualTo(expected.workflow)
        assertThat(actual.get().variations).isEqualTo(expected.variations)
        assertThat(actual.get().scenarios).isEqualTo(mapOf(TemplateScenario.DEFAULT to expected.variations))
    }

    @Test
    fun `update should update and return successfully`() {
        val existing = Template(
            name = "old",
            workflow = EngagementWorkflow.CONFIRMATION,
            variations = emptyMap()
        )
        val expected = CreateOrEditTemplateRequest(
            name = "Test",
            workflow = EngagementWorkflow.CHECKIN,
            scenarios = mapOf(
                TemplateScenario.DEFAULT to mapOf(
                    MessageType.SMS to mapOf(
                        Language.ENGLISH to Template.Segments(main = "test")
                    )
                )
            )
        )

        every { repository.findByIdOrNull("test") } returns existing
        every { repository.save(any()) } answers { firstArg() }

        val actual = sut.update("test", expected)

        assertThat(actual.success).isTrue
        assertThat(actual.get().name).isEqualTo(expected.name)
        assertThat(actual.get().workflow).isEqualTo(expected.workflow)
        assertThat(actual.get().variations).isEqualTo(expected.variations)
        assertThat(actual.get().scenarios).isEqualTo(expected.scenarios)
    }

    @Test
    fun `update should fail upon validation errors`() {
        val existing = Template(
            name = "old",
            workflow = EngagementWorkflow.CONFIRMATION,
            variations = emptyMap()
        )
        val expected = CreateOrEditTemplateRequest(
            name = "",
            workflow = EngagementWorkflow.CHECKIN,
            variations = emptyMap()
        )

        every { repository.findByIdOrNull("test") } returns existing
        every { repository.save(any()) } answers { firstArg() }

        val actual = sut.update("test", expected)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).anyMatch { it.errorCode == Errors.VALIDATION_FAILED.code }
    }

    @Test
    fun `update should fail upon if original is not found`() {
        val expected = CreateOrEditTemplateRequest(
            name = "Test",
            workflow = EngagementWorkflow.CHECKIN,
            variations = mapOf(
                MessageType.SMS to mapOf(
                    Language.ENGLISH to Template.Segments(main = "test")
                )
            )
        )

        every { repository.findByIdOrNull("test") } returns null

        val actual = sut.update("test", expected)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).anyMatch { it.errorCode == Errors.NOT_FOUND.code }
    }

    @Test
    fun `create should create and return successfully`() {
        val expected = CreateOrEditTemplateRequest(
            name = "Test",
            workflow = EngagementWorkflow.CHECKIN,
            variations = mapOf(
                MessageType.SMS to mapOf(
                    Language.ENGLISH to Template.Segments(main = "test")
                )
            )
        )

        every { repository.save(any()) } answers { firstArg() }

        val actual = sut.create(expected)

        assertThat(actual.success).isTrue
        assertThat(actual.get().name).isEqualTo(expected.name)
        assertThat(actual.get().workflow).isEqualTo(expected.workflow)
        assertThat(actual.get().variations).isEqualTo(expected.variations)
    }

    @Test
    fun `create should fail upon validation errors`() {
        val expected = CreateOrEditTemplateRequest(
            name = "",
            workflow = EngagementWorkflow.CHECKIN,
            variations = emptyMap()
        )

        every { repository.save(any()) } answers { firstArg() }

        val actual = sut.create(expected)

        assertThat(actual.success).isFalse
        assertThat(actual.errors).anyMatch { it.errorCode == Errors.VALIDATION_FAILED.code }
    }

    @Test
    fun `delete should delete`() {
        every { repository.deleteById("test") } returns Unit

        sut.delete("test")

       verifySequence {
           sut.delete("test")
       }
    }

    @Test
    fun `getById should return null`() {
        every { repository.getByIdOrNull("test") } returns null

        val actual = sut.getById("test")

        assertThat(actual).isNull()
    }

    @Test
    fun `getById should return template`() {
        val expected = Template(
            name = "test",
            workflow = EngagementWorkflow.CONFIRMATION,
            variations = emptyMap()
        )
        every { repository.getByIdOrNull("test") } returns expected

        val actual = sut.getById("test")

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `find should return results`() {
        every { repository.findAll(any<Pageable>()) } returns mockk()

        val actual = sut.findAll(Pageable.unpaged())

        assertThat(actual).isNotNull
    }

    @Test
    fun `buildSubstitutions should generate expected substitution mappings`() {
        val dateTime = appointmentDate.atOffset(ZoneId.of("US/Eastern").rules.getOffset(appointmentDateLocal))
        val languages = setOf(Language.SPANISH, Language.ENGLISH)
        val actual = sut.buildSubstitutions(engagement, languages)

        assertThat(actual).containsKeys(Language.SPANISH, Language.ENGLISH)
        for (language in languages) {
            val locale = Locale(language.iso639alpha2, language.country)
            assertThat(actual[language]).isEqualTo(mapOf(
                SubstitutionTokens.CONFIRMATION_LINK.token to "http://localhost:8080/confirm?id=${engagement.id}&contact=\$CONTACTID",
                SubstitutionTokens.APPOINTMENT_REASON.token to "Test 1, Test 2",
                SubstitutionTokens.PATIENT_LAST_NAME.token to "Test, Test",
                SubstitutionTokens.PATIENT_FIRST_NAME.token to "Patient 1, Patient 2",
                SubstitutionTokens.PROVIDER.token to "Dr Staff 1",
                SubstitutionTokens.PRACTICE_NAME.token to "Practice One",
                SubstitutionTokens.LOCATION_ADDRESS.token to "123 Main St",
                SubstitutionTokens.LOCATION_TELEPHONE.token to "(*************",
                SubstitutionTokens.APPOINTMENT_LOCATION.token to "Location One",
                SubstitutionTokens.APPOINTMENT_DATE.token to dateTime.format(DateTimeFormatter.ofPattern("MMM d", locale)),
                SubstitutionTokens.APPOINTMENT_DATE_VM.token to dateTime.format(DateTimeFormatter.ofPattern("MMMM d", locale)),
                SubstitutionTokens.APPOINTMENT_TIME.token to dateTime.format(DateTimeFormatter.ofPattern("h:mm a", locale)),
                SubstitutionTokens.APPOINTMENT_DAY_DATE.token to dateTime.format(DateTimeFormatter.ofPattern("EEEE, MMM d", locale)),
                SubstitutionTokens.APPOINTMENT_DAY_DATE_VM.token to dateTime.format(DateTimeFormatter.ofPattern("EEEE, MMMM d", locale)),
                SubstitutionTokens.RECALL_REASON.token to "",
                SubstitutionTokens.CHECK_IN_LINK.token to "",
                SubstitutionTokens.PIN_CODE.token to "",
                SubstitutionTokens.SURVEY_LINK.token to ""
            ))
        }
    }

    @Test
    fun `buildSubstitutions should apply the appointmentTimeDisplayOffset`() {
        val dateTime = appointmentDate.atOffset(ZoneId.of("US/Eastern").rules.getOffset(appointmentDateLocal))
            .minusMinutes(customer.appointmentTimeDisplayOffset?.toMinutes() ?: 0)
        val languages = setOf(Language.SPANISH, Language.ENGLISH)
        val actual = sut.buildSubstitutions(engagement, languages, customer)

        for (language in languages) {
            val locale = Locale(language.iso639alpha2, language.country)
            val substitution = actual[language]
            val appointmentTime = substitution?.get(SubstitutionTokens.APPOINTMENT_TIME.token)
            assertThat(appointmentTime).isEqualTo(dateTime.format(DateTimeFormatter.ofPattern("h:mm a", locale)))
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["1", "2", "3", "4", "5"])
    fun `generateMessageSegments should use standard template to generate segments respecting language and method`(ruleId: String) {
        for (contact in engagement.resources.filterIsInstance<ContactResource>()) {
            val substitutions = sut.buildSubstitutions(engagement, setOf(contact.language))

            val actual = sut.generateMessageSegments(
                    customer.engagementRules.first { it.id == ruleId },
                    contact,
                    substitutions[contact.language]!!
                )

            // TODO validate expected text based on language and contact method

            assertThat(actual).isNotNull
        }
    }

    @Test
    fun `generateMessageSegments should use standard template to generate segments respecting scenario, language, and method`() {
        for (contact in engagement.resources.filterIsInstance<ContactResource>().filter { it.contactMethod == ContactMethod.SMS }) {
            val substitutions = sut.buildSubstitutions(engagement, setOf(contact.language))

            val actual = sut.generateMessageSegments(
                customer.engagementRules.first { it.id == "5" },
                contact,
                substitutions[contact.language]!!,
                TemplateScenario.CONFIRMATION_DEADLINE_PASSED
            )

            assertThat(actual).isNotNull

            if (contact.language == Language.SPANISH) {
                assertThat(actual!!.main).isEqualTo("El tiempo para confirmar su cita ha termindo. Llame a la practica (*************")
            } else {
                assertThat(actual!!.main).isEqualTo("Confirmation window passed. Call (*************")
            }
        }
    }
}
