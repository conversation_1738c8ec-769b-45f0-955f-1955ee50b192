package com.connexin.pmx.server.services

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class UrlGeneratorTests {
    @Test
    fun `should generate URLs respecting baseUrl with path`() {
        val sut = UrlGenerator("$BASE_URL/pmx", "$CONFIRM_URL/pmx")

        assertThat(sut.pmxVoiceWebhook()).hasPath("/pmx/webhooks/pmx/voice/incoming")
        assertThat(sut.pmxMessagingWebhook()).hasPath("/pmx/webhooks/pmx/messaging/status")
        assertThat(sut.pmxEmailWebhook()).hasPath("/pmx/webhooks/pmx/email/events")
        assertThat(sut.unsubscribe(mapOf("id" to "test")))
            .hasPath("/pmx/unsubscribe")
            .hasParameter("id", "test")
        assertThat(sut.confirm(mapOf("id" to "test"))).hasPath("/pmx/confirm")
            .hasParameter("id", "test")
        assertThat(sut.confirm(mapOf("id" to "test")).toString()).isEqualTo("$CONFIRM_URL/pmx/confirm?id=test")
    }

    @Test
    fun `should generate URLs respecting baseUrl without path`() {
        val sut = UrlGenerator(BASE_URL, CONFIRM_URL)

        assertThat(sut.pmxVoiceWebhook()).hasPath("/webhooks/pmx/voice/incoming")
        assertThat(sut.pmxMessagingWebhook()).hasPath("/webhooks/pmx/messaging/status")
        assertThat(sut.pmxEmailWebhook()).hasPath("/webhooks/pmx/email/events")
        assertThat(sut.unsubscribe(mapOf("id" to "test")))
            .hasPath("/unsubscribe")
            .hasParameter("id", "test")
        assertThat(sut.confirm(mapOf("id" to "test"))).hasPath("/confirm")
            .hasParameter("id", "test")
        assertThat(sut.confirm(mapOf("id" to "test")).toString()).isEqualTo("$CONFIRM_URL/confirm?id=test")
    }

    companion object {
        const val BASE_URL = "https://applications-dev.op.healthcare"
        const val CONFIRM_URL = "https://healthconfirmations.com"
    }
}