package com.connexin.pmx.server.client

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.dtos.*
import com.telnyx.sdk.model.OutboundCallRecording
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kong.unirest.HttpRequestWithBody
import kong.unirest.UnirestInstance
import kong.unirest.HttpResponse
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles


@ActiveProfiles("test")
@SpringBootTest
@Import(TestConfig::class)
class TelnyxClientTest {
    private lateinit var unirestInstance: UnirestInstance
    private lateinit var telnyxClient: TelnyxClient
    private val apiKey = "test"

    @BeforeEach
    fun setUp(){
        unirestInstance = mockk()
        telnyxClient = TelnyxClient(unirestInstance, apiKey)
    }

    @Test
    fun deleteBillingGroupSuccess() {
        val response = mockk<HttpResponse<BillingGroupResponse>>()
        val request = mockk<HttpRequestWithBody>()

        every { response.isSuccess } returns true
        every { response.body } returns BillingGroupResponse(data())
        every { request.header("Authorization", "Bearer test") } returns request
        every { request.routeParam(any(), any()) } returns request
        every { request.asObject(BillingGroupResponse::class.java) } returns response
        every { unirestInstance.delete(any<String>()) } returns request

        val result = telnyxClient.deleteBillingGroup("123")

        assertNotNull(result)
        verify { unirestInstance.delete(any<String>()) }
        verify { request.routeParam(any(), any()) }
        verify { request.header(any(), any()) }
        verify { request.asObject(BillingGroupResponse::class.java) }
    }
    @Test
    fun deleteOutboundVoiceProfileSuccess() {
        val response = mockk<HttpResponse<OutboundVoiceProfileResponse>>()
        every { response.isSuccess } returns true
        every { response.body } returns OutboundVoiceProfileResponse(dataOutBOund())
        every { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) } returns response

        val result = telnyxClient.deleteOutboundVoiceProfile("123")

        assertNotNull(result)
        verify { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) }
    }

    @Test
    fun deleteOutboundVoiceProfileUnauthorized() {
        val response = mockk<HttpResponse<OutboundVoiceProfileResponse>>()
        every { response.isSuccess } returns false
        every { response.status } returns 401
        every { response.statusText } returns "Unauthorized"
        every { response.body } returns null
        every { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) } returns response

        val result = telnyxClient.deleteOutboundVoiceProfile("123")

        assertNull(result)
        verify { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) }
    }

    @Test
    fun deleteOutboundVoiceProfileNotFound() {
        val response = mockk<HttpResponse<OutboundVoiceProfileResponse>>()
        every { response.isSuccess } returns false
        every { response.status } returns 404
        every { response.statusText } returns "Not Found"
        every { response.body } returns null
        every { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) } returns response

        val result = telnyxClient.deleteOutboundVoiceProfile("123")

        assertNull(result)
        verify { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) }
    }

    @Test
    fun deleteOutboundVoiceProfileBadRequest() {
        val response = mockk<HttpResponse<OutboundVoiceProfileResponse>>()
        every { response.isSuccess } returns false
        every { response.status } returns 422
        every { response.statusText } returns "Unprocessable Entity"
        every { response.body } returns null
        every { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) } returns response

        val result = telnyxClient.deleteOutboundVoiceProfile("123")

        assertNull(result)
        verify { unirestInstance.delete(any<String>()).routeParam(any(), any()).asObject(OutboundVoiceProfileResponse::class.java) }
    }
    @Test
    fun handleErrorResponseBillingGroupUnauthorized() {
        val jsonString = """
        {
            "errors": [
                {
                    "code": 401,
                    "title": "Unauthorized",
                    "detail": "Access is denied"
                }
            ]
        }
        """
        val response = mockk<HttpResponse<String>>()
        every { response.body } returns jsonString
        every { response.status } returns 401

        telnyxClient.handleErrorResponseBillingGroup(response)
        assertNotNull(response.body)

    }

    @Test
    fun handleErrorResponseBillingGroupNotFound() {
        val jsonString = """
        {
            "errors": [
                {
                    "code": 404,
                    "title": "Not Found",
                    "detail": "Resource not found"
                }
            ]
        }
        """
        val response = mockk<HttpResponse<String>>()
        every { response.body } returns jsonString
        every { response.status } returns 404

        telnyxClient.handleErrorResponseBillingGroup(response)

        assertNotNull(response.body)
    }

    @Test
    fun handleErrorResponseBillingGroupBadRequest() {
        val jsonString = """
        {
            "errors": [
                {
                    "code": 422,
                    "title": "Unprocessable Entity",
                    "detail": "Invalid request"
                }
            ]
        }
        """
        val response = mockk<HttpResponse<String>>()
        every { response.body } returns jsonString
        every { response.status } returns 422

        telnyxClient.handleErrorResponseBillingGroup(response)

        assertNotNull(response.body)

    }

    @Test
    fun handleErrorResponseBillingGroupUnknownError() {
        val jsonString = """
        {
            "errors": [
                {
                    "code": 500,
                    "title": "Internal Server Error",
                    "detail": "An unknown error occurred"
                }
            ]
        }
        """
        val response = mockk<HttpResponse<String>>()
        every { response.body } returns jsonString
        every { response.status } returns 500

        telnyxClient.handleErrorResponseBillingGroup(response)

        assertNotNull(response.body)

    }

    companion object{
        fun data(): BillingGroup{
            return BillingGroup(
                recordType = "billing_group",
                id = "sample_id",
                organizationId = "sample_org_id",
                name = "Sample Billing Group",
                createdAt = "2023-01-01T00:00:00Z",
                updatedAt = "2023-01-01T00:00:00Z",
                deletedAt = null
            )
        }
        fun dataOutBOund(): OutBoundVoiceProfile {
            return OutBoundVoiceProfile(
                id = "sample_id",
                recordType = "outbound_voice_profile",
                name = "Sample Outbound Voice Profile",
                connectionsCount = 10,
                trafficType = "voice",
                servicePlan = "basic",
                concurrentCallLimit = 5,
                enabled = true,
                tags = listOf("tag1", "tag2"),
                usagePaymentMethod = "prepaid",
                whitelistedDestinations = listOf("destination1", "destination2"),
                maxDestinationRate = 100,
                dailySpendLimit = "1000",
                dailySpendLimitEnabled = true,
                callRecording = OutboundCallRecording().apply {
                    callRecordingType = OutboundCallRecording.CallRecordingTypeEnum.ALL
                    callRecordingCallerPhoneNumbers = listOf("+19705555098")
                    callRecordingChannels = OutboundCallRecording.CallRecordingChannelsEnum.DUAL
                    callRecordingFormat = OutboundCallRecording.CallRecordingFormatEnum.MP3
                },
                billingGroupId = "billing_group_id",
                createdAt = "2023-01-01T00:00:00Z",
                updatedAt = "2023-01-01T00:00:00Z"
            )
        }
    }
}