package com.connexin.pmx.server.web

import org.mockserver.integration.ClientAndServer
import org.mockserver.integration.ClientAndServer.startClientAndServer
import org.mockserver.model.Header
import org.mockserver.model.HttpRequest
import org.mockserver.model.HttpResponse

object InternalMockServer {

    private var mockServer: ClientAndServer? = null

    fun startServer() {
        if (mockServer == null) {
            mockServer = startClientAndServer(9988)
        }
    }

    fun stopServer() {
        mockServer!!.stop()
        mockServer = null
    }

    fun mockDefaultRequest() {
        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("GET")
                .withPath("/api/default/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid default get response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("POST")
                .withPath("/api/default/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid default post response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("PATCH")
                .withPath("/api/default/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid default patch response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("DELETE")
                .withPath("/api/default/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid default delete response data }")
            )
    }

    fun mockBridgeRequest() {
        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("GET")
                .withPath("/api/bridge/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                    Header("Authorization", "Bearer mockBridgeAccessToken")
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid bridge get response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("POST")
                .withPath("/api/bridge/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                    Header("Authorization", "Bearer mockBridgeAccessToken")
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid bridge post response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("PATCH")
                .withPath("/api/bridge/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                    Header("Authorization", "Bearer mockBridgeAccessToken")
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid bridge patch response data }")
            )

        mockServer!!.`when`(
            HttpRequest.request()
                .withMethod("DELETE")
                .withPath("/api/bridge/test")
                .withHeaders(
                    Header("Content-Type", "application/json"),
                    Header("Authorization", "Bearer mockBridgeAccessToken")
                )
        )
            .respond(
                HttpResponse.response().withStatusCode(200).withBody("{ valid bridge delete response data }")
            )
    }

}