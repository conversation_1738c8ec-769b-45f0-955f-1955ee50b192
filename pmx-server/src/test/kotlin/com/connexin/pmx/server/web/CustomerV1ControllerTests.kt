package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.LegacyConfig
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.dtos.DeprovisionResult
import com.connexin.pmx.server.services.CustomerProvisioningService
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.TelnyxService
import com.ninjasquad.springmockk.MockkBean
import com.telnyx.sdk.ApiException
import io.mockk.clearAllMocks
import io.mockk.every
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.context.WebApplicationContext


@Import(value = [LegacyConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(CustomerV1Controller::class)
class CustomerV1ControllerTests {

    companion object {
        private val log = LoggerFactory.getLogger(CustomerV1ControllerTests::class.java)
        private const val endpoint = "/api/v1/customers"
        private const val opmedId = "1"
    }

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var context: WebApplicationContext

    @MockkBean
    private lateinit var customerService: CustomerService

    @MockkBean
    private lateinit var customerProvisioningService: CustomerProvisioningService

    @MockkBean
    private lateinit var adminService: TelnyxService

    @BeforeEach
    fun setup() {
        every { customerService.deleteById(any()) } returns Unit
        this.mockMvc = MockMvcBuilders
            .webAppContextSetup(context)
            .build()
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `Successful customer deletion`() {
        every { customerProvisioningService.deprovision(any()) } returns DeprovisionResult(success = true, mutableListOf())
        every { customerService.getById(opmedId) } returns
                Customer(
                    id = "1",
                    name = "Valued Customer",
                    legacyUsername = "valcust",
                    legacyPassword = "badpass",
                    status = CustomerStatus.ENABLED
                )

        mockMvc.perform(delete("$endpoint/$opmedId"))
            .andExpect(MockMvcResultMatchers.status().isNoContent)
    }

    @Test
    fun `Successful customer deletion after deprovision profile retrieval failure`() {
        every { customerProvisioningService.deprovision(any()) } returns DeprovisionResult(success = true, mutableListOf())
        every { adminService.getBillingGroup(any()) } throws ApiException()
        every { customerService.getById(opmedId) } returns
                Customer(
                    id = "1",
                    name = "Valued Customer",
                    legacyUsername = "valcust",
                    legacyPassword = "badpass",
                    status = CustomerStatus.ENABLED
                )

        mockMvc.perform(delete("$endpoint/$opmedId"))
            .andExpect(MockMvcResultMatchers.status().isNoContent)
    }

    @Test
    fun `Unsuccessful customer deletion`() {
        every { customerService.getById(opmedId) } returns null
        every { customerProvisioningService.deprovision(any()) } returns DeprovisionResult(success = true, mutableListOf())

        mockMvc.perform(delete("$endpoint/$opmedId"))
            .andExpect(MockMvcResultMatchers.status().isNotFound)
    }
}