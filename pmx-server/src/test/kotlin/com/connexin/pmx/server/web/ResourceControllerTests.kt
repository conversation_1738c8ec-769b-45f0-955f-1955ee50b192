package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.Constants.X_OPMED
import com.connexin.pmx.server.models.dtos.Contact
import com.connexin.pmx.server.models.dtos.Practice
import com.connexin.pmx.server.models.dtos.Response
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(ResourceController::class)
@WithMockUser
class ResourceControllerTests {

    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var mapper: ObjectMapper

    @MockkBean
    lateinit var customerService: CustomerService

    @MockkBean
    lateinit var engagementService: EngagementService

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        customer = Customer(
            id = "1234",
            name = "Test",
            status = CustomerStatus.ENABLED
        )

        every { customerService.getById(customer.id!!) } returns customer
        every { customerService.getById("1") } returns null
    }

    @Test
    fun `patchContact should return 404 if customer is not found`() {
        mockMvc.perform(
            patch(CONTACT_URL)
                .header(X_OPMED, "1")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(Contact(id = "1")))
        )
            .andExpect(status().`is`(HttpStatus.NOT_FOUND.value()))
    }

    @Test
    fun `patchContact should update Contact`() {
        val expected = Contact(
            id = "1",
            contactMethod = ContactMethod.SMS,
            language = Language.ENGLISH
        )
        every {
            engagementService.updateContact(any())
        } returns Response.success(
            ContactResource(
                id = "1",
                familyName = null,
                givenName = null,
                email = null,
                phone = null,
                contactMethod = ContactMethod.SMS,
                language = Language.ENGLISH
            )
        )

        mockMvc.perform(
            patch(CONTACT_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(expected))
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }

    @Test
    fun `patchPractice should update Practice`() {
        val expected = Practice(
            id = "1",
            name = "test"
        )
        every {
            engagementService.updatePractice(any())
        } returns Response.success(
            PracticeResource(
                id = "1",
                name = "test"
            )
        )

        mockMvc.perform(
            patch(PRACTICE_URL)
                .header(X_OPMED, customer.id)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(expected))
        )
            .andExpect(status().`is`(HttpStatus.OK.value()))
            .andExpect(content().json(mapper.writeValueAsString(expected)))
    }

    companion object {
        const val CONTACT_URL = "/api/v2/resource/contact"
        const val PRACTICE_URL = "/api/v2/resource/practice"
    }
}