package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.models.DeliveryDay
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import java.time.Duration
import java.time.LocalTime
import java.time.temporal.ChronoUnit

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class CustomerRepositoryTest {
    @Autowired
    lateinit var sut: CustomerRepository

    @Autowired
    lateinit var template: MongoTemplate

    @AfterEach
    fun teardown() {
        template.remove(Query.query(Criteria.where("id").`is`("1234")), Customer::class.java)
    }

    @Test
    fun `should create retrieve and delete a customer`() {
        val expected = Customer(
            id = "1234",
            name = "Test",
            status = CustomerStatus.ENABLED,
            deliveryDays = mutableSetOf(DeliveryDay.MONDAY, DeliveryDay.FRIDAY),
            deliveryStartTime = LocalTime.of(9, 0),
            deliveryEndTime = LocalTime.of(14, 45),
            cancellationDeadline = Duration.of(12, ChronoUnit.HOURS)
        )

        val actual = sut.findById(sut.save(expected).id!!)

        assertThat(actual.get())
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(expected)

        sut.delete(actual.get())

        assertThat(sut.findById(actual.get().id!!).isEmpty).isTrue
    }

    @Test
    fun `should not delete customer with non-existent id`() {
        val nonExistentId = "5678"
        sut.deleteById(nonExistentId)

        assertThat(sut.findById(nonExistentId).isEmpty).isTrue
    }
}