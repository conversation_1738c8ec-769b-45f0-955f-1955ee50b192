package com.connexin.pmx.server.utils

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

class PhoneNumberUtilTest {

    @ParameterizedTest(name = "given the number \"{0}\", canonicalize should return E164-formatted equivalent")
    @MethodSource("validUnformattedNumbers")
    fun `canonicalize should return E164-formatted equivalent`(original: String, expected: String) {
        val actual = PhoneNumberUtil.canonicalize(original)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `format should return national format`() {
        val actual = PhoneNumberUtil.format("+1**********")

        assertThat(actual).isEqualTo("(*************")
    }

    companion object {
        @JvmStatic
        fun validUnformattedNumbers(): Stream<Arguments> = Stream.of(
            Arguments.of("************", "+1**********"),
            Arguments.of("1-************", "+1**********"),
            Arguments.of("**********", "+1**********"),
            Arguments.of("1**********", "+1**********"),
            Arguments.of("************", "+1**********"),
            Arguments.of("1 ************", "+1**********"),
            Arguments.of("************", "+1**********"),
            Arguments.of("**************", "+1**********"),
            Arguments.of("(*************", "+1**********"),
            Arguments.of("****************", "+1**********"),
        )
    }
}