package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.PatchAdminCustomer
import com.connexin.pmx.server.models.dtos.CreateCustomerRequest
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.models.dtos.PatchSelfManagedCustomer
import com.connexin.pmx.server.repositories.CustomerRepository
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.TextNode
import com.github.fge.jsonpatch.JsonPatch
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import io.mockk.verifySequence
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.data.repository.findByIdOrNull
import org.springframework.http.HttpStatus
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.stream.Stream

@ExtendWith(MockKExtension::class)
class CustomerServiceImplTest {
    @MockK
    lateinit var customerRepository: CustomerRepository

    @RelaxedMockK
    lateinit var mapper: ObjectMapper

    private var passwordEncoder: BCryptPasswordEncoder = BCryptPasswordEncoder()

    private lateinit var sut: CustomerServiceImpl

    private lateinit var customer: Customer

    @BeforeEach
    fun setup() {
        sut = CustomerServiceImpl(customerRepository, passwordEncoder, mapper)

        customer = Customer(
            id = "1",
            organizationId = 1L,
            name = "Test",
            status = CustomerStatus.ENABLED,
            engagementRules = mutableSetOf(
                EngagementRule(
                    id = "1",
                    enabled = true,
                    workflow = EngagementWorkflow.CONFIRMATION,
                    templateOverrides = mutableMapOf(),
                    templateIds = mutableMapOf()
                ),
                EngagementRule(
                    id = "99",
                    enabled = true,
                    workflow = EngagementWorkflow.CANCELLATION,
                    templateOverrides = mutableMapOf(),
                    templateIds = mutableMapOf(),
                    allStaff = true,
                    allAppointmentTypes = true,
                    allPracticeLocations = true
                )
            )
        )
    }

    @Test
    fun `authenticate should return Customer with matching credentials`() {
        val expectedPassword = passwordEncoder.encode("test1234")
        every { customerRepository.findByLegacyUsername("test") } returns Optional.of(
            Customer(
                id = "1",
                organizationId = 1L,
                name = "Test",
                legacyUsername = "test",
                legacyPassword = expectedPassword,
                status = CustomerStatus.ENABLED
            )
        )

        val actual = sut.authenticate("test", "test1234")

        assertThat(actual).isNotNull
        assertThat(actual?.id).isEqualTo("1")
    }

    @Test
    fun `authenticate should return null if no Customer with username is found`() {
        every { customerRepository.findByLegacyUsername("test") } returns Optional.empty()

        val actual = sut.authenticate("test", "test1234")

        assertThat(actual).isNull()
    }

    @Test
    fun `authenticate should return null if Customer found but password does not match`() {
        val expectedPassword = passwordEncoder.encode("test1234")
        every { customerRepository.findByLegacyUsername("test") } returns Optional.of(
            Customer(
                id = "1",
                organizationId = 1L,
                name = "Test",
                legacyUsername = "test",
                legacyPassword = expectedPassword,
                status = CustomerStatus.ENABLED
            )
        )

        val actual = sut.authenticate("test", "badpass")

        assertThat(actual).isNull()
    }

    @Test
    fun `authenticate should return null if Customer found but is disabled`() {
        val expectedPassword = passwordEncoder.encode("test1234")
        every { customerRepository.findByLegacyUsername("test") } returns Optional.of(
            Customer(
                id = "1",
                organizationId = 1L,
                name = "Test",
                legacyUsername = "test",
                legacyPassword = expectedPassword,
                status = CustomerStatus.DISABLED
            )
        )

        val actual = sut.authenticate("test", "test1234")

        assertThat(actual).isNull()
    }

    @Test
    fun `authenticate should return null if Customer found but is provisioning`() {
        val expectedPassword = passwordEncoder.encode("test1234")
        every { customerRepository.findByLegacyUsername("test") } returns Optional.of(
            Customer(
                id = "1",
                organizationId = 1L,
                name = "Test",
                legacyUsername = "test",
                legacyPassword = expectedPassword,
                status = CustomerStatus.PROVISIONING
            )
        )

        val actual = sut.authenticate("test", "test1234")

        assertThat(actual).isNull()
    }

    @Test
    fun `save should save and return customer`() {
        val expected = Customer(
            id = "1",
            organizationId = 1L,
            name = "Test",
            status = CustomerStatus.ENABLED
        )
        every { customerRepository.save(expected) } returns expected

        val actual = sut.save(expected)

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `getById should return matching customer`() {
        val expected = Customer(
            id = "1",
            organizationId = 1L,
            name = "Test",
            status = CustomerStatus.ENABLED
        )
        every { customerRepository.findByIdOrNull("1") } returns expected

        val actual = sut.getById("1")

        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `create should succeed and save a customer with provisioning status`() {
        every { customerRepository.findByIdOrNull("1") } returns null
        every { customerRepository.findByLegacyUsername(any()) } returns Optional.empty()
        every { customerRepository.save(any()) } answers {
            val customer = firstArg<Customer>()

            customer
        }
        val request = CreateCustomerRequest(
            opmedId = "1",
            name = "Test",
            legacyUsername = "test",
            legacyPassword = "test1234"
        )
        val expected = Customer(
            id = request.opmedId,
            name = request.name,
            legacyUsername = request.legacyUsername,
            legacyPassword = passwordEncoder.encode(request.legacyPassword),
            status = CustomerStatus.PROVISIONING
        )

        val actual = sut.create(request)

        assertThat(actual.success).isTrue
        assertThat(actual.customer).isEqualTo(expected.copy(legacyPassword = actual.customer!!.legacyPassword))

        verifySequence {
            customerRepository.findByIdOrNull("1")
            customerRepository.findByLegacyUsername("test")
            customerRepository.save(expected.copy(legacyPassword = actual.customer!!.legacyPassword))
        }
    }

    @Test
    fun `create should fail validation`() {
        every { customerRepository.save(any()) } answers {
            val customer = firstArg<Customer>()
            customer
        }
        val request = CreateCustomerRequest(
            opmedId = "",
            name = "",
            legacyUsername = "",
            legacyPassword = ""
        )

        val actual = sut.create(request)

        assertThat(actual.success).isFalse
        assertThat(actual.customer).isNull()
        assertThat(actual.errors).containsExactly(
            ErrorDto(path = "opmedId", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "name", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "legacyUsername", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code),
            ErrorDto(path = "legacyPassword", message = "Must not be blank", errorCode = Errors.VALIDATION_FAILED.code),
        )

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `create should fail because customer already exists`() {
        val existing = Customer(
            id = "1",
            name = "Test",
            legacyUsername = "test",
            legacyPassword = "test",
            status = CustomerStatus.PROVISIONING
        )
        every { customerRepository.findByIdOrNull("1") } returns existing
        every { customerRepository.save(any()) } answers {
            val customer = firstArg<Customer>()
            customer
        }

        val actual = sut.create(
            CreateCustomerRequest(
                opmedId = "1",
                name = "Test",
                legacyUsername = "test",
                legacyPassword = "test"
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.customer).isNull()
        assertThat(actual.errors).containsExactly(
            ErrorDto(path = "opmedId", message = "Customer already exists", errorCode = Errors.DUPLICATE.code)
        )

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `create should fail because legacy username is not unique`() {
        val existing = Customer(
            id = "2",
            name = "Test",
            legacyUsername = "test",
            legacyPassword = "test",
            status = CustomerStatus.PROVISIONING
        )
        every { customerRepository.findByIdOrNull("1") } returns null
        every { customerRepository.findByLegacyUsername("test") } returns Optional.of(existing)
        every { customerRepository.save(any()) } answers {
            val customer = firstArg<Customer>()
            customer
        }

        val actual = sut.create(
            CreateCustomerRequest(
                opmedId = "1",
                name = "Test",
                legacyUsername = "test",
                legacyPassword = "test"
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.customer).isNull()
        assertThat(actual.errors).containsExactly(
            ErrorDto(path = "legacyUsername", message = "Must be unique", errorCode = Errors.DUPLICATE_USERNAME.code)
        )

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `createEngagementRule should fail if rule with ID already exists`() {

        val actual = sut.createEngagementRule(
            customer, EngagementRule(
                id = "1",
                enabled = true,
                workflow = EngagementWorkflow.CONFIRMATION,
                templateOverrides = mutableMapOf(),
                templateIds = mutableMapOf()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.CONFLICT)

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `createEngagementRule should fail if validation fails`() {
        every { sut.save(customer) } returns customer
        val actual = sut.createEngagementRule(
            customer, EngagementRule(
                id = "2",
                enabled = true,
                workflow = EngagementWorkflow.CONFIRMATION,
                templateOverrides = mutableMapOf(),
                templateIds = mutableMapOf()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.errors).allMatch { it.errorCode == Errors.VALIDATION_FAILED.code }

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `createEngagementRule should fail if rule overlaps another`() {
        every { sut.save(customer) } returns customer
        val actual = sut.createEngagementRule(
            customer, EngagementRule(
                id = "2",
                enabled = true,
                workflow = EngagementWorkflow.CANCELLATION,
                templateIds = mapOf(TemplateScenario.DEFAULT to "1"),
                allPracticeLocations = true,
                allAppointmentTypes = true,
                allStaff = true
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.CONFLICT)
        assertThat(actual.errors).allMatch { it.errorCode == Errors.OVERLAPPING_ENGAGEMENT_RULES.code }

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `createEngagementRule should succeed`() {
        every { sut.save(customer) } returns customer
        val actual = sut.createEngagementRule(
            customer, EngagementRule(
                id = "2",
                enabled = true,
                workflow = EngagementWorkflow.CONFIRMATION,
                templateOverrides = mapOf(
                    TemplateScenario.DEFAULT to mapOf(
                        MessageType.SMS to mapOf(
                            Language.ENGLISH to Template.Segments(main = "test")
                        )
                    )
                ),
                templateIds = mutableMapOf(TemplateScenario.DEFAULT to "1234")
            )
        )

        assertThat(actual.success).isTrue

        verifySequence {
            customerRepository.save(customer)
        }
    }

    @Test
    fun `deleteEngagementRule should fail if rule cannot be found`() {
        val actual = sut.deleteEngagementRule(customer, "23")

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.NOT_FOUND)

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `deleteEngagementRule should succeed`() {
        every { sut.save(customer) } returns customer

        val actual = sut.deleteEngagementRule(customer, "1")

        assertThat(actual.success).isTrue

        verify {
            customerRepository.save(customer)
        }
    }

    @Test
    fun `saveEngagementRule should fail if validation fails`() {
        val actual = sut.saveEngagementRule(
            customer, "1", EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateOverrides = mutableMapOf(),
                templateIds = mutableMapOf()
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.BAD_REQUEST)
        assertThat(actual.errors).allMatch { it.errorCode == Errors.VALIDATION_FAILED.code }

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `saveEngagementRule should fail the result would be overlapping rules`() {
        val actual = sut.saveEngagementRule(
            customer, "1", EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CANCELLATION,
                templateIds = mutableMapOf(TemplateScenario.DEFAULT to "1"),
                allPracticeLocations = true,
                allStaff = true,
                allAppointmentTypes = true
            )
        )

        assertThat(actual.success).isFalse
        assertThat(actual.status).isEqualTo(HttpStatus.CONFLICT)
        assertThat(actual.errors).allMatch { it.errorCode == Errors.OVERLAPPING_ENGAGEMENT_RULES.code }

        verify(exactly = 0) {
            customerRepository.save(any())
        }
    }

    @Test
    fun `saveEngagementRule should succeed`() {
        every { customerRepository.save(any()) } answers { firstArg() }

        val actual = sut.saveEngagementRule(
            customer, "1", EngagementRule(
                id = "1",
                workflow = EngagementWorkflow.CONFIRMATION,
                templateIds = mutableMapOf(TemplateScenario.DEFAULT to "1"),
                allPracticeLocations = true,
                allStaff = true,
                allAppointmentTypes = true
            )
        )

        assertThat(actual.success).isTrue

        verify(exactly = 1) {
            customerRepository.save(any())
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["1", "100"])
    fun `saveEngagementRule should succeed`(ruleId: String) {
        every { customerRepository.save(any()) } answers { firstArg() }
        val actual = sut.saveEngagementRule(
            customer, ruleId, EngagementRule(
                id = ruleId,
                workflow = EngagementWorkflow.CONFIRMATION,
                enabled = false,
                templateOverrides = mapOf(
                    TemplateScenario.DEFAULT to mapOf(
                        MessageType.SMS to mapOf(
                            Language.ENGLISH to Template.Segments(main = "test")
                        )
                    )
                ),
                templateIds = mutableMapOf(TemplateScenario.DEFAULT to "1234")
            )
        )

        assertThat(actual.success).isTrue

        verifySequence {
            customerRepository.save(customer)
        }
    }

    @Test
    fun `patch should update customer admin properties`() {
        every { mapper.convertValue(PatchAdminCustomer.from(customer), JsonNode::class.java) } returns TextNode("test")
        every { mapper.treeToValue(any(), PatchAdminCustomer::class.java) } returns PatchAdminCustomer.from(customer)
        every { customerRepository.save(any()) } answers { firstArg() }

        val actual = sut.patch(customer, PatchAdminCustomer.from(customer), JsonPatch(emptyList()))

        assertThat(actual).isEqualTo(customer)

        verifySequence {
            mapper.convertValue(PatchAdminCustomer.from(customer), JsonNode::class.java)
            mapper.treeToValue(any(), PatchAdminCustomer::class.java)
            customerRepository.save(customer)
        }
    }

    @Test
    fun `patch should update customer self-managed properties`() {
        every { mapper.convertValue(PatchSelfManagedCustomer.from(customer), JsonNode::class.java) } returns TextNode("test")
        every { mapper.treeToValue(any(), PatchSelfManagedCustomer::class.java) } returns PatchSelfManagedCustomer.from(customer)
        every { customerRepository.save(any()) } answers { firstArg() }

        val actual = sut.patch(customer, PatchSelfManagedCustomer.from(customer), JsonPatch(emptyList()))

        assertThat(actual).isEqualTo(customer)

        verifySequence {
            mapper.convertValue(PatchSelfManagedCustomer.from(customer), JsonNode::class.java)
            mapper.treeToValue(any(), PatchSelfManagedCustomer::class.java)
            customerRepository.save(customer)
        }
    }

    @ParameterizedTest
    @MethodSource("testEngagementRules")
    fun `isOverlapping should return correct value`(
        existing: EngagementRule,
        rule: EngagementRule,
        expected: Boolean
    ) {
        val actual = CustomerServiceImpl.isOverlapping(rule, setOf(existing))

        assertThat(actual).isEqualTo(expected)
    }

    companion object {
        private val now: Instant = Instant.now()

        private fun createRule(
            id: String,
            workflow: EngagementWorkflow = EngagementWorkflow.CONFIRMATION,
            start: Instant? = null,
            end: Instant? = null,
            appointmentTypes: Set<String>? = null,
            staff: Set<String>? = null,
            practiceLocations: Set<PracticeLocationRule>? = null
        ): EngagementRule {
            return EngagementRule(
                id = id,
                workflow = workflow,
                startDate = start,
                endDate = end,
                templateIds = emptyMap(),
                appointmentTypes = appointmentTypes,
                allAppointmentTypes = appointmentTypes.isNullOrEmpty(),
                staff = staff,
                allStaff = staff.isNullOrEmpty(),
                practiceLocations = practiceLocations,
                allPracticeLocations = practiceLocations.isNullOrEmpty()
            )
        }

        @JvmStatic
        fun testEngagementRules(): Stream<Arguments> = Stream.of(
            // is same rule
            Arguments.of(createRule(id = "1"), createRule(id = "1"), false),
            // rules don't overlap, different workflows
            Arguments.of(createRule(id = "1"), createRule(id = "2", workflow = EngagementWorkflow.REMINDER), false),
            // rules with no start and end date overlap
            Arguments.of(createRule(id = "1"), createRule(id = "2"), true),
            // rules have overlapping start dates
            Arguments.of(
                createRule(
                    id = "1",
                    start = now.minus(10, ChronoUnit.DAYS),
                    end = null
                ),
                createRule(
                    id = "2",
                    start = now,
                    end = null
                ),
                true
            ),
            // end and start dates overlap
            Arguments.of(
                createRule(
                    id = "1",
                    start = null,
                    end = now.plus(10, ChronoUnit.DAYS)
                ),
                createRule(
                    id = "2",
                    start = now
                ),
                true
            ),
            // start and end dates overlap
            Arguments.of(
                createRule(
                    id = "1",
                    start = now.minus(10, ChronoUnit.DAYS),
                    end = now.plus(10, ChronoUnit.DAYS)
                ),
                createRule(
                    id = "2",
                    start = now,
                    end = now.plus(15, ChronoUnit.DAYS)
                ),
                true
            ),
            // existing rule ends before rule starts
            Arguments.of(
                createRule(
                    id = "1",
                    start = now.minus(10, ChronoUnit.DAYS),
                    end = now.minus(1, ChronoUnit.DAYS)
                ),
                createRule(
                    id = "2",
                    start = now,
                    end = now.plus(15, ChronoUnit.DAYS)
                ),
                false
            ),
            // existing rule starts after rule ends
            Arguments.of(
                createRule(
                    id = "1",
                    start = now.plus(10, ChronoUnit.DAYS),
                    end = now.plus(20, ChronoUnit.DAYS)
                ),
                createRule(
                    id = "2",
                    start = now,
                    end = now.plus(9, ChronoUnit.DAYS)
                ),
                false
            ),
            // both rules apply to all appointment types
            Arguments.of(
                createRule(
                    id = "1",
                    appointmentTypes = emptySet()
                ),
                createRule(
                    id = "2",
                    appointmentTypes = emptySet()
                ),
                true
            ),
            // one applies to all appointment types
            Arguments.of(
                createRule(
                    id = "1",
                    appointmentTypes = emptySet()
                ),
                createRule(
                    id = "2",
                    appointmentTypes = setOf("1", "9")
                ),
                true
            ),
            // both rules share at least one appointment type
            Arguments.of(
                createRule(
                    id = "1",
                    appointmentTypes = setOf("1", "2", "3")
                ),
                createRule(
                    id = "2",
                    appointmentTypes = setOf("2", "4", "6")
                ),
                true
            ),
            // no shared appointment types
            Arguments.of(
                createRule(
                    id = "1",
                    appointmentTypes = setOf("1", "2", "3")
                ),
                createRule(
                    id = "2",
                    appointmentTypes = setOf("4", "5", "6")
                ),
                false
            ),
            // both apply to all staff
            Arguments.of(
                createRule(
                    id = "1",
                    staff = emptySet()
                ),
                createRule(
                    id = "2",
                    staff = emptySet()
                ),
                true
            ),
            // one applies to all staff
            Arguments.of(
                createRule(
                    id = "1",
                    staff = emptySet()
                ),
                createRule(
                    id = "2",
                    staff = setOf("1", "2")
                ),
                true
            ),
            // both share at least one staff
            Arguments.of(
                createRule(
                    id = "1",
                    staff = setOf("2")
                ),
                createRule(
                    id = "2",
                    staff = setOf("1", "2")
                ),
                true
            ),
            // no shared staff
            Arguments.of(
                createRule(
                    id = "1",
                    staff = setOf("3")
                ),
                createRule(
                    id = "2",
                    staff = setOf("1", "2")
                ),
                false
            ),
            // both are all practice/locations
            Arguments.of(
                createRule(
                    id = "1",
                    practiceLocations = null
                ),
                createRule(
                    id = "2",
                    practiceLocations = null
                ),
                true
            ),
            // one is all practice/locations
            Arguments.of(
                createRule(
                    id = "1",
                    practiceLocations = null
                ),
                createRule(
                    id = "2",
                    practiceLocations = setOf(
                        PracticeLocationRule(practiceId = "1", allLocations = true)
                    )
                ),
                true
            ),
            // different practices
            Arguments.of(
                createRule(
                    id = "1",
                    practiceLocations = setOf(
                        PracticeLocationRule(practiceId = "2", allLocations = true)
                    )
                ),
                createRule(
                    id = "2",
                    practiceLocations = setOf(
                        PracticeLocationRule(practiceId = "1", allLocations = true)
                    )
                ),
                false
            ),
            // share a location
            Arguments.of(
                createRule(
                    id = "1",
                    practiceLocations = setOf(
                        PracticeLocationRule(
                            practiceId = "1",
                            allLocations = false,
                            locations = setOf("1", "2")
                        )
                    )
                ),
                createRule(
                    id = "2",
                    practiceLocations = setOf(
                        PracticeLocationRule(
                            practiceId = "1",
                            allLocations = false,
                            locations = setOf("2", "3")
                        )
                    )
                ),
                true
            ),
            // no locations shared
            Arguments.of(
                createRule(
                    id = "1",
                    practiceLocations = setOf(
                        PracticeLocationRule(
                            practiceId = "1",
                            allLocations = false,
                            locations = setOf("1")
                        )
                    )
                ),
                createRule(
                    id = "2",
                    practiceLocations = setOf(
                        PracticeLocationRule(
                            practiceId = "1",
                            allLocations = false,
                            locations = setOf("2", "3")
                        )
                    )
                ),
                false
            ),
        )
    }
}