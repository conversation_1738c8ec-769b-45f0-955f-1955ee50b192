package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.ApiException
import com.connexin.pmx.server.models.AppointmentResource
import com.connexin.pmx.server.models.CheckInStatus
import com.connexin.pmx.server.models.ContactMethod
import com.connexin.pmx.server.models.ContactResource
import com.connexin.pmx.server.models.Language
import com.connexin.pmx.server.services.BridgeService
import com.connexin.urlformattingtool.model.ShortenOptions
import com.connexin.urlformattingtool.service.UrlShortener
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant

@ExtendWith(MockKExtension::class)
class CheckInServiceImplTest {

    @MockK
    lateinit var bridgeService: BridgeService

    @MockK
    lateinit var urlShortener: UrlShortener

    @InjectMockKs
    lateinit var sut: CheckInServiceImpl

    @Test
    fun `generateCheckInLink should return shortened URL when successful`() {
        // Given
        val appointment = AppointmentResource(
            id = "appointment-123",
            startTime = Instant.now(),
            reason = "Test appointment",
            location = "location-1",
            staff = "staff-1",
            patient = "patient-123",
            appointmentType = "type-1",
            practice = "practice-1",
            checkInStatus = CheckInStatus.NOT_CHECKED_IN
        )
        val contact = ContactResource(
            id = "contact-123",
            familyName = "Doe",
            givenName = "John",
            email = "<EMAIL>",
            phone = "+**********",
            contactMethod = ContactMethod.EMAIL,
            language = Language.ENGLISH
        )
        val opmedId = "opmed-123"
        val bridgeUrl = "https://bridge.example.com/checkin/12345"
        val shortenedUrl = "https://short.ly/abc123"

        every { bridgeService.generatePatientCheckinUrl(appointment.id, appointment.patient, contact.id, opmedId) } returns bridgeUrl
        every { urlShortener.shortenUrl(bridgeUrl, any<ShortenOptions>()) } returns shortenedUrl

        // When
        val result = sut.generateCheckInLink(appointment, contact, opmedId)

        // Then
        assertThat(result).isEqualTo(shortenedUrl)
    }

    @Test
    fun `generateCheckInLink should return null when bridge service throws exception`() {
        // Given
        val appointment = AppointmentResource(
            id = "appointment-123",
            startTime = Instant.now(),
            reason = "Test appointment",
            location = "location-1",
            staff = "staff-1",
            patient = "patient-123",
            appointmentType = "type-1",
            practice = "practice-1",
            checkInStatus = CheckInStatus.NOT_CHECKED_IN
        )
        val contact = ContactResource(
            id = "contact-123",
            familyName = "Doe",
            givenName = "John",
            email = "<EMAIL>",
            phone = "+**********",
            contactMethod = ContactMethod.EMAIL,
            language = Language.ENGLISH
        )
        val opmedId = "opmed-123"

        every { bridgeService.generatePatientCheckinUrl(appointment.id, appointment.patient, contact.id, opmedId) } throws ApiException(message = "Bridge service error")

        // When
        val result = sut.generateCheckInLink(appointment, contact, opmedId)

        // Then
        assertThat(result).isNull()
    }

    @Test
    fun `generateCheckInLink should return null when URL shortener throws exception`() {
        // Given
        val appointment = AppointmentResource(
            id = "appointment-123",
            startTime = Instant.now(),
            reason = "Test appointment",
            location = "location-1",
            staff = "staff-1",
            patient = "patient-123",
            appointmentType = "type-1",
            practice = "practice-1",
            checkInStatus = CheckInStatus.NOT_CHECKED_IN
        )
        val contact = ContactResource(
            id = "contact-123",
            familyName = "Doe",
            givenName = "John",
            email = "<EMAIL>",
            phone = "+**********",
            contactMethod = ContactMethod.EMAIL,
            language = Language.ENGLISH
        )
        val opmedId = "opmed-123"
        val bridgeUrl = "https://bridge.example.com/checkin/12345"

        every { bridgeService.generatePatientCheckinUrl(appointment.id, appointment.patient, contact.id, opmedId) } returns bridgeUrl
        every { urlShortener.shortenUrl(bridgeUrl, any<ShortenOptions>()) } throws RuntimeException("URL shortener error")

        // When
        val result = sut.generateCheckInLink(appointment, contact, opmedId)

        // Then
        assertThat(result).isNull()
    }
}
