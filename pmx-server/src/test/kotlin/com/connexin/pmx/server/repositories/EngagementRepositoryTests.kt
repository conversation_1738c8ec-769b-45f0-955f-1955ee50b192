package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.Pageable
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.findAllAndRemove
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.repository.findByIdOrNull
import org.springframework.test.context.ActiveProfiles
import java.time.Instant
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class EngagementRepositoryTests {
    @Autowired
    lateinit var template: MongoTemplate

    @Autowired
    lateinit var sut: EngagementRepository

    @AfterEach
    fun cleanup() {
        template.findAllAndRemove<Engagement>(
            Query(Criteria.where("customerId").`in`("test", "test2"))
        )
        sut.deleteAll()
    }

    @Test
    fun `should create retrieve and delete engagements`() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        val expected = Engagement(
            customerId = "test",
            eventDate = now,
            resources = mutableSetOf(
                AppointmentResource(
                    id = "1",
                    startTime = now,
                    reason = "test",
                    location = "test",
                    staff = "test",
                    patient = "test",
                    appointmentType = "test",
                    practice = "test"
                ),
                PracticeResource(
                    id = "test",
                    name = "test"
                ),
                LocationResource(
                    id = "test",
                    practiceId = "test",
                    name = "test",
                    zipCode = "test",
                    zoneId = "test"
                ),
                StaffResource(
                    id = "test",
                    name = "test"
                ),
                PatientResource(
                    id = "test",
                    familyName = "test",
                    givenName = "patient"
                ),
                ContactResource(
                    id = "test",
                    familyName = "test",
                    givenName = "Contact",
                    contactMethod = ContactMethod.VOICE,
                    phone = "+14405551234",
                    language = Language.SPANISH,
                    email = null
                )
            ),
            nextCheckpoint = now
        )

        val actual = sut.save(expected)

        assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expected)
        assertThat(actual.id).isNotNull
        assertThat(actual.createdAt).isAfter(now)
        assertThat(actual.updatedAt).isAfter(now)

        assertThat(sut.findById(actual.id!!).get())
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(actual)

        sut.deleteById(actual.id!!)

        assertThat(sut.findById(actual.id!!).isEmpty).isTrue
    }

    @Test
    fun `findAwaitingCheckpoint should return less than or equal checkpoint and exclude failed or completed`() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        val expected = sut.saveAll(
            listOf(
                // good
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now,
                    status = EngagementStatus.REMIND
                ),
                // bad, checkpoint not met
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now.plus(1, ChronoUnit.DAYS),
                ),
                // bad, excluding completed
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now,
                    status = EngagementStatus.COMPLETED
                ),
                // bad, excluding failed
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now,
                    status = EngagementStatus.ERROR
                ),
            )
        )

        val actual = sut.findAwaitingCheckpoint(Instant.now(), setOf(EngagementStatus.REMIND), Pageable.ofSize(10))

        assertThat(actual.size).isEqualTo(1)
        assertThat(actual[0].id).isEqualTo(expected[0].id)
    }

    @Test
    fun `findByCustomerId should return for customer`() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        val expected = sut.saveAll(
            listOf(
                // good
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now
                ),
                // bad, checkpoint not met
                Engagement(
                    customerId = "test2",
                    eventDate = now,
                    resources = mutableSetOf(),
                    nextCheckpoint = now
                ),
            )
        )

        val actual = sut.findByCustomerId("test", Pageable.ofSize(10))

        assertThat(actual.totalElements).isEqualTo(1)
        assertThat(actual.content[0].id).isEqualTo(expected[0].id)
    }

    @Test
    fun existsByResource() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        sut.saveAll(
            listOf(
                Engagement(
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(
                        AppointmentResource(
                            id = "1",
                            startTime = now,
                            reason = "reason",
                            location = "location",
                            staff = "staff",
                            patient = "patient",
                            appointmentType = "type",
                            practice = "practice"
                        ),
                        PracticeResource(
                            id = "1",
                            name = "Practice 1"
                        ),
                        LocationResource(
                            id = "2",
                            name = "Location 2",
                            practiceId = "1",
                            zipCode = "44145",
                            email = null,
                            phone = null,
                            address = null,
                            zoneId = "US/Eastern"
                        )
                    ),
                    nextCheckpoint = now
                )
            )
        )

        // true
        assertThat(sut.existsByResources("test", setOf("1"), AppointmentResource::class.java.name)).isTrue

        // false
        assertThat(sut.existsByResources("test2", setOf("1"), AppointmentResource::class.java.name)).isFalse
        assertThat(sut.existsByResources("test", setOf("1", "2"), ContactResource::class.java.name)).isFalse
    }

    @Test
    fun findByResource() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        sut.saveAll(
            listOf(
                Engagement(
                    id = "1",
                    customerId = "test",
                    eventDate = now,
                    resources = mutableSetOf(
                        AppointmentResource(
                            id = "1",
                            startTime = now,
                            reason = "reason",
                            location = "location",
                            staff = "staff",
                            patient = "patient",
                            appointmentType = "type",
                            practice = "practice"
                        ),
                        PracticeResource(
                            id = "1",
                            name = "Practice 1"
                        ),
                        LocationResource(
                            id = "2",
                            name = "Location 2",
                            practiceId = "1",
                            zipCode = "44145",
                            email = null,
                            phone = null,
                            address = null,
                            zoneId = "US/Eastern"
                        )
                    ),
                    nextCheckpoint = now
                ),
                Engagement(
                    id = "2",
                    customerId = "test2",
                    eventDate = now,
                    resources = mutableSetOf(
                        AppointmentResource(
                            id = "1",
                            startTime = now,
                            reason = "reason",
                            location = "location",
                            staff = "staff",
                            patient = "patient",
                            appointmentType = "type",
                            practice = "practice"
                        ),
                        PracticeResource(
                            id = "1",
                            name = "Practice 1"
                        ),
                        LocationResource(
                            id = "2",
                            name = "Location 2",
                            practiceId = "1",
                            zipCode = "44145",
                            email = null,
                            phone = null,
                            address = null,
                            zoneId = "US/Eastern"
                        )
                    ),
                    nextCheckpoint = now
                )
            )
        )

        // true
        assertThat(sut.findByResource("test", "1", AppointmentResource::class.java.name, Pageable.ofSize(1))).hasSize(1).first().hasFieldOrPropertyWithValue("id", "1")
        assertThat(sut.findByResource("test2", "1", AppointmentResource::class.java.name, Pageable.ofSize(1))).hasSize(1).first().hasFieldOrPropertyWithValue("id", "2")

        // false
        assertThat(sut.findByResource("test", "2", AppointmentResource::class.java.name, Pageable.ofSize(1))).isEmpty()
        assertThat(sut.findByResource("test", "1", ContactResource::class.java.name, Pageable.ofSize(1))).isEmpty()
    }

    @Test
    fun `updateResource should update matching resources in engagements`() {
        val now = OffsetDateTime.now().withNano(0).toInstant()
        val contact1 = ContactResource(
            id = "100",
            familyName = "test1",
            givenName = "test1",
            email = "test1",
            phone = "test1",
            contactMethod = ContactMethod.NONE,
            language = Language.ENGLISH
        )
        val contact2 = ContactResource(
            id = "101",
            familyName = "test2",
            givenName = "test2",
            email = null,
            phone = null,
            contactMethod = ContactMethod.NONE,
            language = Language.SPANISH
        )
        val location1 = LocationResource(
            id = "101",
            practiceId = "1",
            name = "test location",
            zipCode = "test",
            zoneId = "test"
        )
        sut.saveAll(listOf(
            // bad - eventDate passed
            Engagement(
                id = "1",
                customerId = "test",
                eventDate = now.minusSeconds(30),
                resources = mutableSetOf(contact1, contact2, location1),
                nextCheckpoint = now
            ),
            //  bad - different customer
            Engagement(
                id = "2",
                customerId = "test2",
                eventDate = now,
                resources = mutableSetOf(contact1, contact2, location1),
                nextCheckpoint = now
            ),
            // good
            Engagement(
                id = "3",
                customerId = "test",
                eventDate = now,
                resources = mutableSetOf(contact1,contact2,location1),
                nextCheckpoint = now
            ),
            // good
            Engagement(
                id = "4",
                customerId = "test",
                eventDate = now.plusSeconds(30),
                resources = mutableSetOf(contact2),
                nextCheckpoint = now
            ),
        ))

        // update contact2
        val expected = contact2.copy(email = "test2", phone = "test2")
        val updatedCount = sut.updateResources("test", now, expected)

        assertThat(updatedCount).isEqualTo(2)

        // not updated
        val actual1 = sut.findByIdOrNull("1") ?: fail("not found")
        assertThat(actual1.resources).containsOnly(contact1, contact2, location1)

        val actual2 = sut.findByIdOrNull("2") ?: fail("not found")
        assertThat(actual2.resources).containsOnly(contact1, contact2, location1)

        // updated
        val actual3 = sut.findByIdOrNull("3") ?: fail("not found")
        assertThat(actual3.resources).containsOnly(contact1, expected, location1)

        val actual4 = sut.findByIdOrNull("4") ?: fail("not found")
        assertThat(actual4.resources).containsOnly(expected)
    }
}