package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.TestFileUtil
import com.connexin.pmx.server.config.TelnyxProperties
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.RespondContext
import com.connexin.pmx.server.services.TelnyxService
import com.connexin.pmx.server.services.UrlGenerator
import com.connexin.pmx.server.services.WebhookDeduper
import com.fasterxml.jackson.databind.cfg.CoercionAction
import com.fasterxml.jackson.databind.cfg.CoercionInputShape
import com.fasterxml.jackson.databind.type.LogicalType
import com.telnyx.sdk.ApiException
import com.telnyx.sdk.model.CreateMessageRequest
import com.telnyx.sdk.model.MessageResponse
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verifySequence
import org.assertj.core.api.Assertions.assertThat
import org.json.JSONException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource
import java.time.Duration
import java.time.Instant
import java.util.stream.Stream

@ExtendWith(MockKExtension::class)
class TelnyxSmsMessageDispatcherTest {
    @MockK
    lateinit var client: TelnyxService

    @RelaxedMockK
    lateinit var deduper: WebhookDeduper

    private lateinit var sut: TelnyxSmsMessageDispatcher

    private val urlGenerator = UrlGenerator("http://localhost:8080", "http://localhost:8080")

    private val customer = Customer(
        id = "1",
        organizationId = 1L,
        name = "Test",
        telnyxConfig = Customer.TelnyxConfig(
            messagingProfileId = "4000eba1-a0c0-4563-9925-b25e842a7cb6"
        ),
        status = CustomerStatus.ENABLED
    )

    private val properties = TelnyxProperties.Engagement(
        englishMessagingProfileId = "test-english-messaging-profile-id",
        spanishMessagingProfileId = "test-spanish-messaging-profile-id",
        englishVoiceNumbers = "+14405551234",
        spanishVoiceNumbers = "+14405551235",
        callControlConnectionId = "test-call-control-id"
    )

    @BeforeEach
    fun setup() {
        TestFileUtil.mapper.coercionConfigFor(LogicalType.Integer).setCoercion(CoercionInputShape.String, CoercionAction.TryConvert)
        sut = TelnyxSmsMessageDispatcher(
            client,
            TestFileUtil.mapper,
            urlGenerator,
            deduper,
            properties
        )
    }

    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }

    @Test
    fun `initiate should create a message`() {
        val response = TestFileUtil.get("/telnyx/messaging/message_success_response.json", MessageResponse::class.java)
        val expected = CreateMessageRequest()
            .messagingProfileId(customer.telnyxConfig.messagingProfileId)
            .to("+14405551234")
            .text("test")
            .type(CreateMessageRequest.TypeEnum.SMS)
            .useProfileWebhooks(false)
            .webhookUrl("http://localhost:8080/webhooks/pmx/messaging/status")
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        every { client.createMessage(expected) } returns response.data!!.id!!.toString()

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isTrue
        assertThat(actual.remoteId).isEqualTo("40385f64-5717-4562-b3fc-2c963f66afa6")

        verifySequence {
            client.createMessage(expected)
        }
    }

    @Test
    fun `initiate should create a message using engagement messaging profile`() {
        val response = TestFileUtil.get("/telnyx/messaging/message_success_response.json", MessageResponse::class.java)
        val expected = CreateMessageRequest()
            .messagingProfileId(properties.englishMessagingProfileId)
            .to("+14405551234")
            .text("test")
            .type(CreateMessageRequest.TypeEnum.SMS)
            .useProfileWebhooks(false)
            .webhookUrl("http://localhost:8080/webhooks/pmx/messaging/status")
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH,
            engagementId = "test",
            engagementRuleId = "test"
        )
        every { client.createMessage(expected) } returns response.data!!.id!!.toString()

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isTrue
        assertThat(actual.remoteId).isEqualTo("40385f64-5717-4562-b3fc-2c963f66afa6")

        verifySequence {
            client.createMessage(expected)
        }
    }

    @Test
    fun `respond should update status to sent when message sent webhook is received`() {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_sent_webhook.json")
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.respond(RespondContext(
            payload = "",
            type = MessageType.SMS,
            decodedPayload = payload,
            message = message
        ))

        assertThat(actual.success).isTrue
        assertThat(message.status).isEqualTo(MessageStatus.SENT)
        assertThat(message.completedAt).isNull()
    }

    @Test
    fun `respond should update status to delivered when message finalized delivered webhook is received`() {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_finalized_delivered_webhook.json")
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        val now = Instant.now()

        val actual = sut.respond(RespondContext(
            payload = "",
            type = MessageType.SMS,
            decodedPayload = payload,
            message = message
        ))

        assertThat(actual.success).isTrue
        assertThat(message.status).isEqualTo(MessageStatus.DELIVERED)
        assertThat(message.completedAt).isAfterOrEqualTo(now)
    }

    @Test
    fun `respond should update status to failed when message finalized failed webhook is received`() {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_finalized_failed_webhook.json")
        val message = PmxMessage(
            id = "message-id",
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        val now = Instant.now()

        val actual = sut.respond(RespondContext(
            payload = "",
            type = MessageType.SMS,
            decodedPayload = payload,
            message = message
        ))

        assertThat(actual.success).isTrue
        assertThat(message.status).isEqualTo(MessageStatus.FAILED)
        assertThat(message.completedAt).isAfterOrEqualTo(now)
    }

    @ParameterizedTest(name = "given the response status of {0}, initiate should reschedule if a retryable status is returned")
    @MethodSource("retryErrorResponses")
    fun `initiate should reschedule if a retryable status is returned`(statusCode: Int) {
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        every { client.createMessage(any()) } throws ApiException(statusCode, "error", emptyMap(), "")

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isTrue
        assertThat(actual.retryDelay).isEqualTo(Duration.ofMinutes(1))
    }

    @Test
    fun `initiate should not request retry upon unhandled exception`() {
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        every { client.createMessage(any()) } throws JSONException("test")

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isFalse
        assertThat(actual.errors).isEqualTo("test")
    }

    @Test
    fun `initiate should not request retry upon non-retryable ApiException`() {
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.QUEUED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        every { client.createMessage(any()) } throws ApiException(401, emptyMap(), "test")

        val actual = sut.initiate(message, customer)

        assertThat(actual.success).isFalse
        assertThat(actual.retry).isFalse
        assertThat(actual.errors).isEqualTo("test")
    }

    @ParameterizedTest(name = "respond to sent webhook should not change status if status is already {0}")
    @MethodSource("finalStatuses")
    fun `respond to sent webhook should not change status if status is already final`(status: MessageStatus) {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_sent_webhook.json")
        val message = PmxMessage(
            type = MessageType.SMS,
            customerId = "1",
            status = status,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.respond(RespondContext(
            payload = "",
            type = MessageType.SMS,
            decodedPayload = payload,
            message = message
        ))

        assertThat(actual.success).isTrue
        assertThat(message.status).isEqualTo(status)
    }

    @Test
    fun `respond should not fail for unknown line_type`() {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_finalized_failed_webhook_unknown_line_type.json")
        val message = PmxMessage(
            id = "message-id",
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.SENT,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )

        val actual = sut.respond(RespondContext(
            payload = "",
            type = MessageType.SMS,
            decodedPayload = payload,
            message = message
        ))

        assertThat(actual.success).isTrue
        assertThat(message.status).isEqualTo(MessageStatus.FAILED)
    }

    @ParameterizedTest
    @ValueSource(strings = ["YES", "CONFIRM"])
    fun `respond should update confirmationStatus to CONFIRMED`(text: String) {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_received_yes_webhook.json")
        val message = PmxMessage(
            id = "message-id",
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.DELIVERED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        val context = RespondContext(
            decodedPayload = payload,
            payload = "",
            type = MessageType.SMS,
            responseCategory = RespondContext.ResponseCategory.CONFIRM,
            receivedMessage = RespondContext.ReceivedMessage(
                from = to,
                text = text
            ),
            message = message
        )

        val actual = sut.respond(context)

        assertThat(actual.success).isTrue
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.CONFIRMED)
        assertThat(message.responses).hasSize(1)
        assertThat(message.responses[0].text).isEqualTo(text)
    }

    @ParameterizedTest
    @ValueSource(strings = ["NO", "CANCEL"])
    fun `respond should update confirmationStatus to DECLINED`(text: String) {
        val payload = TestFileUtil.getAsTree("/telnyx/messaging/message_received_no_webhook.json")
        val message = PmxMessage(
            id = "message-id",
            type = MessageType.SMS,
            customerId = "1",
            status = MessageStatus.DELIVERED,
            to = "+14405551234",
            message = "test",
            sendAfter = Instant.EPOCH
        )
        val context = RespondContext(
            decodedPayload = payload,
            payload = "",
            type = MessageType.SMS,
            responseCategory = RespondContext.ResponseCategory.DECLINE,
            receivedMessage = RespondContext.ReceivedMessage(
                from = to,
                text = text
            ),
            message = message
        )

        val actual = sut.respond(context)

        assertThat(actual.success).isTrue
        assertThat(message.confirmationStatus).isEqualTo(ConfirmationStatus.DECLINED)
        assertThat(message.responses).hasSize(1)
        assertThat(message.responses[0].text).isEqualTo(text)
    }

    @ParameterizedTest(name = "beforeRespond should resolve remoteId for payload in {0}")
    @MethodSource("validWebhookPayloads")
    fun `beforeRespond should resolve remoteId`(filename: String) {
        val payload = TestFileUtil.getAsString("/telnyx/messaging/$filename")
        val actual = sut.beforeRespond(
            RespondContext(
                payload = payload,
                type = MessageType.SMS
            )
        )

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context.remoteId)
            .isEqualTo("40385f64-5717-4562-b3fc-2c963f66afa6")
    }

    @ParameterizedTest(name = "beforeRespond should return unacceptable for invalid payload in {0}")
    @MethodSource("invalidWebhookPayloads")
    fun `beforeRespond should return unacceptable for invalid payload`(filename: String) {
        val payload = TestFileUtil.getAsString("/telnyx/messaging/$filename")

        val actual = sut.beforeRespond(
            RespondContext(
                payload = payload,
                type = MessageType.SMS
            )
        )

        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
        assertThat(actual.context.remoteId).isNull()
    }

    @Test
    fun `deduperService should skip duplicate webhooks`() {
        val json = TestFileUtil.getAsTree("/telnyx/messaging/message_webhook_duplicate.json")

        every { deduper.isDuplicate(any()) } returnsMany listOf(false, true, false)

        //Sending unique webhook
        var context = RespondContext(
                payload = json.get(0).toString(),
                type = MessageType.SMS
        )
        //Should save this webhook and deduperService should return false
        var actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull

        //Sending duplicate webhook
        //Should skip duplicate webhook and deduperService should return true
        actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isFalse
        assertThat(actual.context).isNotNull

        //Sending unique webhook
        context = RespondContext(
                payload = json.get(1).toString(),
                type = MessageType.SMS
        )

        //Should save this webhook and deduperService should return false
        actual = sut.beforeRespond(context)
        assertThat(actual.success).isTrue
        assertThat(actual.accepted).isTrue
        assertThat(actual.context).isNotNull
    }


    companion object {
        private const val to = "+14405551234"

        @JvmStatic
        fun noConsentStatuses(): Stream<Arguments> = Stream.of(
            Arguments.of(ConsentStatus.REQUESTED),
            Arguments.of(ConsentStatus.OPTED_OUT)
        )

        @JvmStatic
        fun languages(): Stream<Arguments> = Stream.of(
            Arguments.of(Language.ENGLISH),
            Arguments.of(Language.SPANISH)
        )

        @JvmStatic
        fun finalStatuses(): Stream<Arguments> = Stream.of(
            Arguments.of(MessageStatus.DELIVERED),
            Arguments.of(MessageStatus.FAILED)
        )

        @JvmStatic
        fun validWebhookPayloads(): Stream<Arguments> = Stream.of(
            Arguments.of("message_finalized_delivered_webhook.json"),
            Arguments.of("message_finalized_failed_webhook.json"),
            Arguments.of("message_finalized_failed_webhook_unknown_line_type.json"),
            Arguments.of("message_sent_webhook.json")
        )

        @JvmStatic
        fun invalidWebhookPayloads(): Stream<Arguments> = Stream.of(
            Arguments.of("message_finalized_missing_event_type.json"),
            Arguments.of("message_finalized_missing_id.json"),
            Arguments.of("message_finalized_missing_payload.json"),
            Arguments.of("message_received_webhook_missing_from.json"),
            Arguments.of("message_received_webhook_invalid_text.json")
        )

        @JvmStatic
        fun retryErrorResponses(): Stream<Arguments> = Stream.of(
            Arguments.of(429),
            Arguments.of(504)
        )
    }
}