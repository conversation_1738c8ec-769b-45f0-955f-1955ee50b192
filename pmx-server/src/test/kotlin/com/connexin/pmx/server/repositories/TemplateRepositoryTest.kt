package com.connexin.pmx.server.repositories

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.models.EngagementWorkflow
import com.connexin.pmx.server.models.Language
import com.connexin.pmx.server.models.MessageType
import com.connexin.pmx.server.models.Template
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.repository.findByIdOrNull
import org.springframework.test.context.ActiveProfiles

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class TemplateRepositoryTest {

    @Autowired
    lateinit var template: MongoTemplate

    @Autowired
    lateinit var sut: TemplateRepository

    @AfterEach
    fun cleanup() {
        template.remove(Template::class.java)
    }

    @Test
    fun `CRUD operations`() {
        val created = sut.save(
            Template(
                name = "test",
                workflow = EngagementWorkflow.CONFIRMATION,
                variations = mapOf(
                    MessageType.EMAIL to mapOf(
                        Language.ENGLISH to Template.Segments(main = "test", subject = "test"),
                        Language.SPANISH to Template.Segments(main = "test", subject = "test")
                    ),
                    MessageType.SMS to mapOf(
                        Language.ENGLISH to Template.Segments(main = "test"),
                        Language.SPANISH to Template.Segments(main = "test")
                    ),
                    MessageType.VOICE to mapOf(
                        Language.ENGLISH to Template.Segments(main = "test", instructions = "test"),
                        Language.SPANISH to Template.Segments(main = "test", instructions = "test")
                    )
                )
            )
        )

        assertThat(created.id).isNotNull

        val retrieved = sut.findByIdOrNull(created.id)

        assertThat(retrieved).isEqualTo(created)

        sut.deleteById(created.id!!)

        assertThat(sut.findByIdOrNull(created.id)).isNull()
    }
}