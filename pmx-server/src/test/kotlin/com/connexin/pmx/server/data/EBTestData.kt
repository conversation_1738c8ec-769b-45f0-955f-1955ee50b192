package com.connexin.pmx.server.data

import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.bli.EBAttachment
import com.connexin.pmx.server.models.bli.EBMessage
import com.connexin.pmx.server.models.bli.EBMessages
import com.connexin.pmx.server.models.bli.EBProof
import com.connexin.pmx.server.utils.InstantUtil.Companion.toInstant
import java.time.Instant
import java.time.temporal.ChronoUnit

class EBTestData() {
    companion object {
        val ebTestMessage = """
            UserName=96267&UserPassword=8993&XMLPost=
            <Orders>
            <Order Type="EB">
            <Project>Broadcast: Appointment Reminders</Project>
            <BillCode/>
            <Date>2021-01-22</Date>
            <Time>21:00</Time>
            <DisplayName>Jon <PERSON></DisplayName>
            <From><EMAIL></From>
            <Subject>Appointment Reminder</Subject>
            <ReplyTo><EMAIL></ReplyTo>
            <Forward>No</Forward>
            <ReplaceLink>No</ReplaceLink>
            <Unsubscribe>Yes</Unsubscribe>
            <NumberOfRedials>0</NumberOfRedials>
            <NumberOfResends>2</NumberOfResends>
            <ResendInterval>3</ResendInterval>
            <ListID/>
            <ListName>email.csv</ListName>
            <ListBinary>********************************************************************************************************************************************************************************************</ListBinary>
            <EmailField>Email</EmailField>
            <HtmlFile/><HtmlID/><HTMLBinary/>
            <TextFile>message.txt</TextFile><TextID/>
            <TextBinary>VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</TextBinary>
            <RtfFile/><RtfID/><EnrichedFile/><EnrichedID/><XmlFile/><XmlID/>
            <Attachments>
            <Attachment>
            <AttachmentID/>
            <AttachmentName>EBAttachment1.pdf</AttachmentName>
            <AttachmentBinary>VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIFByZXNzIDEgbm93IHRvIGNvbmZpcm0sIG9yIHByZXNzIDIgdG8gYmUgY29ubmVjdGVkIHRvIHNvbWVvbmUgd2hvIGNhbiBoZWxwIHlvdSByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</AttachmentBinary>
            </Attachment>
            <Attachment>
            <AttachmentID/>
            <AttachmentName>EBAttachment2.txt</AttachmentName>
            <AttachmentBinary>VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh</AttachmentBinary>
            </Attachment>
            </Attachments>
            <Proofs><Proof/></Proofs>
            <AutoLaunch>Yes</AutoLaunch>
            </Order></Orders>&PostWay=sync"""
    }
}

fun testEBMessages(): EBMessages {

    val sampleMessage = testEBMessage1()
    val ebMessages = EBMessages(sampleMessage)
    return ebMessages

}

fun testEBMessage1(): EBMessage {

    val sampleAttachments1: ArrayList<EBAttachment> = ArrayList<EBAttachment>()
    sampleAttachments1.add(testAttachment1())
    sampleAttachments1.add(testAttachment2())

    val sampleProofs1: ArrayList<EBProof> = ArrayList<EBProof>()
    sampleProofs1.add(testProof())

    val testOrder = EBMessage (
         type = "EB",
         project = "Broadcast: Appointment Reminders",
         billCode = "",
         date = "2021-01-22",
         time = "21:00",
         displayName = "Jon Smith",
         from = "<EMAIL>",
         subject = "Appointment Reminder",
         replyTo = "<EMAIL>",
         forward = "No",
         replaceLink = "No",
         unsubscribe = "Yes",
         numberOfRedials = "0",
         numberOfResends = "2",
         resendInterval = "3",
         listID = "",
         listName = "email.csv",
         listBinary = "********************************************************************************************************************************************************************************************",
         emailField = "Email",
         htmlFile = "",
         htmlID = "",
         htmlBinary = "",
         textFile = "message.txt",
         textID = "",
         textBinary = "VGhpcyBpcyAtIC0gLSAtIC0gIFBlZGlhdHJpY3MgY2FsbGluZyB0byByZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBwb2ludG1lbnQgd2l0aCBEZW1vRG9jIG9uIEZyaWRheSBKYW51YXJ5IDE0IGF0IDE6MDAgUE0uIElmIHlvdSBjYW5ub3QgbWFrZSB0aGlzIGFwcG9pbnRtZW50LCBwbGVhc2UgY2FsbCB0aGUgb2ZmaWNlIGF0IDAwMC0wMDAtMDAwMCBkdXJpbmcgcmVndWxhciBidXNpbmVzcyBob3VycyB0byByZXNjaGVkdWxlLiBUaGFuayB5b3Uh",
         rtfFile = "",
         rtfID = "",
         enrichedFile = "",
         enrichedID = "",
         xmlFile = "",
         xmlID = "",
         ebAttachments = sampleAttachments1,
         ebProofs = sampleProofs1,
         autoLaunch = "Yes")

    return testOrder
}

fun testProof(): EBProof {

    val testProof = EBProof (
        proof = "test")

    return testProof
}

fun testAttachment1(): EBAttachment {

    val testAttachment = EBAttachment (
        attachmentID = "11",
        attachmentName = "TEST1.PDF",
        attachmentBinary = "VGhpcyBpcyAgLSAtIC0gLSAtIFBlZGlhdHJpY3MgY2FsbGluZyB0b")

    return testAttachment
}

fun testAttachment2(): EBAttachment {

    val testAttachment = EBAttachment (
        attachmentID = "22",
        attachmentName = "TEST2.TXT",
        attachmentBinary = "yZW1pbmQgeW91IHRoYXQgQ2x1ZSBoYXMgYW4gYXBw")

    return testAttachment
}

// test pmx message for testing EB Reports
fun testPMXMessageWithForEBReport(): PmxMessage {

    val destinations: ArrayList<PmxMessage.EmailRecipient> = ArrayList<PmxMessage.EmailRecipient>()
    destinations.add(testDestination1())
    destinations.add(testDestination2())

    val testMessage = PmxMessage(
        id = "300",
        customerId = "10",
        type = MessageType.EMAIL_BROADCAST,
        status = MessageStatus.DELIVERED,
        sendAfter = Instant.now().plus(2, ChronoUnit.HOURS),
        sendWindow = PmxMessage.SendWindow.DEFAULT,
        attempts = 2,
        createdAt = toInstant("09/15/2019 02:10:42 PM"),
        updatedAt = toInstant("09/15/2019 02:11:20 PM"),
        completedAt = toInstant("09/15/2019 02:15:10 PM"),
        to = "<EMAIL>",
        from = "<EMAIL>",
        subject = "Appointment Reminder",
        message = "VGhpcyBpcyBhIHJlbWluZGVyIHRoYXQgQ2xhc3N5IGhhcyBhbiBhcHBvaW50bWVudCB3aXRoIERlbW9Eb2Mgb24gV2VkbmVzZGF5IEphbnVhcnkgMTIgYXQgMTE6MzAgQU0uIFRvIGNvbmZpcm0sIGNsaWNrIGhlcmU6IC4gSWYgeW91IG5lZWQgdG8gcmVzY2hlZHVsZSwgcGxlYXNlIGNhbGwgdGhlIG9mZmljZSBhdCAwMDAtMDAwLTAwMDAgZHVyaW5nIG5vcm1hbCBidXNpbmVzcyBob3VycyBhdCB5b3VyIGVhcmxpZXN0IG9wcG9ydHVuaXR5LiBUaGFuayB5b3Uh",
        altMessage = null,    // used for voice messages only
        remoteId = "885Ad871-864d-4d32-8f1d-7a07d2ef449a",
        errors = null,
        replyTo = "<EMAIL>",
        responseData = null,
        confirmationStatus = ConfirmationStatus.NOT_APPLICABLE,   // used for voice messages only
        voiceDeliveryMethod = VoiceDeliveryMethod.UNKNOWN,   // used for voice messages only
        emailRecipients = destinations
    )

    return testMessage
}

fun testDestination1(): PmxMessage.EmailRecipient {

    val testDestination = PmxMessage.EmailRecipient (
        address = "<EMAIL>",
        status = MessageStatus.DELIVERED,
        remoteId = "5827171-864d-4d32-8f1d-7a07d2ef449a")

    return testDestination
}

fun testDestination2(): PmxMessage.EmailRecipient {

    val testDestination = PmxMessage.EmailRecipient (
        address = "<EMAIL>",
        status = MessageStatus.DELIVERED,
        remoteId = "9157978-864d-4d32-8f1d-7a07d2ef449a")

    return testDestination
}
/*
========================================================================================
XML from OP:
      '<Orders><Order Type="' + BLI_MSG_TYPE_EMAIL_BROADCAST + '">' +
      '<Project>Broadcast: ' + CleanEmailSubject(Copy(FieldByName('EMAIL_SUBJECT').AsString, 1, 50)) + '</Project>' +
      '<BillCode/>' +
      '<Date>' + FormatDateTime('yyyy-mm-dd', Date) + '</Date>' +
      '<Time>' + FormatDateTime('hh:nn', Time) + '</Time>' +
      '<DisplayName>' + FieldByName('SENDER_NAME').AsString + '</DisplayName>' +
      '<From>' + CleanEmailAddress(FieldByName('SENDER_EMAIL').AsString) + '</From>' +
      '<Subject>' + CleanEmailSubject(FieldByName('EMAIL_SUBJECT').AsString) + '</Subject>' +
      '<ReplyTo>' + CleanEmailAddress(FieldByName('REPLY_TO').AsString) + '</ReplyTo>' +
      '<Forward>No</Forward>' +
      '<ReplaceLink>No</ReplaceLink>' +
      '<Unsubscribe>Yes</Unsubscribe>' +
      '<NumberOfRedials>0</NumberOfRedials>' +
      '<NumberOfResends>2</NumberOfResends>' +
      '<ResendInterval>3 Hours</ResendInterval>' +
      '<ListID/>' +
      '<ListName>email.csv</ListName>' +
      '<ListBinary>' + String(DIMime.MimeEncodeStringNoCRLF(AnsiString(CleanEmailList(DataSet)))) + '</ListBinary>' +
      '<EmailField>Email</EmailField>' +
      '<HtmlFile/><HtmlID/><HTMLBinary/>' +
      '<TextFile>message.txt</TextFile><TextID/>' +
      '<TextBinary>' + String(DIMime.MimeEncodeStringNoCRLF(AnsiString(FieldByName('MESSAGE_DATA').AsString))) + '</TextBinary>' +
      '<RtfFile/><RtfID/><EnrichedFile/><EnrichedID/><XmlFile/><XmlID/>' +
      '<Attachments>' +
        '<Attachment><AttachmentID/><AttachmentName/><AttachmentBinary/></Attachment>' +
      '</Attachments>' +
      '<Proofs><Proof/></Proofs>' +
      '<AutoLaunch>Yes</AutoLaunch>' +
    '</Order></Orders>';
======================================================================================
Sample xml:
<Orders>
	<Order Type="EB">
		<Project>Broadcast: Patient Reminders</Project>
		<BillCode/>
		<Date>2022-01-24</Date>
		<Time>12:49</Time>
		<DisplayName>Patient Reminders</DisplayName>
		<From><EMAIL></From>
		<Subject>Patient Reminders</Subject>
		<ReplyTo/>
		<Forward>No</Forward>
		<ReplaceLink>No</ReplaceLink>
		<Unsubscribe>Yes</Unsubscribe>
		<NumberOfRedials>0</NumberOfRedials>
		<NumberOfResends>2</NumberOfResends>
		<ResendInterval>3 Hours</ResendInterval>
		<ListID/>
		<ListName>email.csv</ListName>
		<ListBinary>********************************************************************************************************************************************************************************************</ListBinary>
		<EmailField>Email</EmailField>
		<HtmlFile/>
		<HtmlID/>
		<HTMLBinary/>
		<TextFile>message.txt</TextFile>
		<TextID/>
		<TextBinary>dGVzdGluZyAxLzI0DQpURVNUSU5HIFBBVElFTlQgRU1BSUwgQlJPQURDQVNU</TextBinary>
		<RtfFile/>
		<RtfID/>
		<EnrichedFile/>
		<EnrichedID/>
		<XmlFile/>
		<XmlID/>
		<Attachments>
			<Attachment>
				<AttachmentID/>
				<AttachmentName/>
				<AttachmentBinary/>
			</Attachment>
		</Attachments>
		<Proofs>
			<Proof/>
		</Proofs>
		<AutoLaunch>Yes</AutoLaunch>
	</Order>
</Orders>
*/

/*
XML Sample of EB Report:

<Report>
    <EB>
        <OrderId>5711918</OrderId>
        <Project>Broadcast: TEST BROADCAST</Project>
        <EmailAddress><EMAIL></EmailAddress>
        <OpenCount>0</OpenCount>
        <LastOpened>1/1/1900 12:00:00 AM</LastOpened>
        <JobStatus>Sent</JobStatus>
        <Result></Result>
        <Error></Error>
        <Timestamp>03/17/2014 10:55:41 AM</Timestamp>
        <Email><EMAIL></Email>
        <Name>BELINDA SMITH</Name>
    </EB>
    <EB>
        <OrderId>5711918</OrderId>
        <Project>Broadcast: TEST BROADCAST</Project>
        <EmailAddress><EMAIL></EmailAddress>
        <OpenCount>0</OpenCount>
        <LastOpened>1/1/1900 12:00:00 AM</LastOpened>
        <JobStatus>Sent</JobStatus>
        <Result></Result>
        <Error></Error>
        <Timestamp>03/17/2014 10:56:30 AM</Timestamp>
        <Email><EMAIL></Email>
        <Name>EILEEN JONSON</Name>
    </EB>
    <EB>
        <OrderId>5711918</OrderId>
        <Project>Broadcast: TEST BROADCAST</Project>
        <EmailAddress><EMAIL></EmailAddress>
        <OpenCount>0</OpenCount>
        <LastOpened>1/1/1900 12:00:00 AM</LastOpened>
        <JobStatus>Sent</JobStatus>
        <Result></Result>
        <Error></Error>
        <Timestamp>03/17/2014 10:55:58 AM</Timestamp>
        <Email><EMAIL></Email>
        <Name>MARCIA BRADY</Name>
    </EB>
</Report>
*/