package com.connexin.pmx.server.web

import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.config.SecurityConfig
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.EngagementService
import com.connexin.pmx.server.services.PmxMessageService
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

@Import(value = [SecurityConfig::class, TestConfig::class])
@ActiveProfiles("test")
@ExtendWith(SpringExtension::class)
@WebMvcTest(ConfirmationController::class)
class ConfirmationControllerTests {
    @MockkBean
    lateinit var engagementService: EngagementService

    @MockkBean
    lateinit var customerService: CustomerService

    @MockkBean
    lateinit var messageService: PmxMessageService

    @Autowired
    lateinit var mockMvc: MockMvc

    private val good = Engagement(
        id = "good",
        customerId = "1",
        eventDate = Instant.now().plus(5, ChronoUnit.DAYS),
        nextCheckpoint = Instant.now().plus(5, ChronoUnit.DAYS),
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )
    private val good2 = Engagement(
        id = "good2",
        customerId = "1",
        eventDate = Instant.now().plus(5, ChronoUnit.DAYS),
        nextCheckpoint = Instant.now().plus(5, ChronoUnit.DAYS),
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )

    private val pastCutoff = Engagement(
        id = "pastcutoff",
        customerId = "1",
        eventDate = Instant.now().plus(5, ChronoUnit.HOURS),
        nextCheckpoint = Instant.now().plus(5, ChronoUnit.HOURS),
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )

    private val noCust = Engagement(
        id = "noCust",
        customerId = "2",
        eventDate = Instant.now(),
        nextCheckpoint = Instant.now(),
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )

    private val confirmed = Engagement(
        id = "confirmed",
        customerId = "1",
        eventDate = Instant.now().plus(12, ChronoUnit.HOURS),
        nextCheckpoint = Instant.now().plus(12, ChronoUnit.HOURS),
        confirmationStatus = ConfirmationStatus.CONFIRMED,
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )

    private val declined = Engagement(
        id = "declined",
        customerId = "1",
        eventDate = Instant.now().plus(12, ChronoUnit.HOURS),
        nextCheckpoint = Instant.now().plus(12, ChronoUnit.HOURS),
        confirmationStatus = ConfirmationStatus.DECLINED,
        resources = mutableSetOf(
            LocationResource(
                id = "location1",
                name = "Test Location",
                practiceId = "practice1",
                zipCode = "44145",
                zoneId = "US/Eastern"
            ),
            PracticeResource(
                id = "practice1",
                name = "Practice 1"
            ),
            ContactResource(
                id = "contact1",
                familyName = "User",
                givenName = "Spanish",
                contactMethod = ContactMethod.SMS,
                phone = "+14405551234",
                email = null,
                language = Language.SPANISH
            )
        )
    )

    val customer = Customer(
        id = "1",
        name = "Test 1",
        status = CustomerStatus.ENABLED,
        cancellationDeadline = Duration.ofHours(6)
    )

    @BeforeEach
    fun setup() {
        every { engagementService.getById(any()) } returns null
        every { engagementService.getById("good") } returns good
        every { engagementService.getById("good2") } returns good2
        every { engagementService.getById("confirmed") } returns confirmed
        every { engagementService.getById("declined") } returns declined
        every { engagementService.getById("noCust") } returns noCust
        every { engagementService.getById("pastcutoff") } returns pastCutoff

        every { engagementService.sendEvent(any<ConfirmationResponseEvent>(), any()) } answers {
            val msg = firstArg<ConfirmationResponseEvent>()
            msg.engagement.copy(confirmationStatus = msg.result)
        }

        every { customerService.getById(any()) } returns null
        every { customerService.getById("1") } returns customer

        every { messageService.findForEngagementIdAndContact("good", "+14405551234") } answers {
            PmxMessage(
                id = "message1",
                type = MessageType.SMS,
                status = MessageStatus.DELIVERED,
                customerId = customer.id!!,
                sendAfter = Instant.now(),
                message = "test",
                to = secondArg(),
                engagementId = firstArg()
            )
        }
        every { messageService.findForEngagementIdAndContact("good2", "+14405551234") } returns null
    }

    @Test
    fun `get should return error if engagement ID is missing`() {
        mockMvc.perform(
            get("/confirm")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", false))
    }

    @Test
    fun `get should return error if engagement cannot be found`() {
        mockMvc.perform(
            get("/confirm")
                .queryParam("id", "bad")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", false))
    }

    @Test
    fun `get should return prompt`() {
        mockMvc.perform(
            get("/confirm")
                .queryParam("id", "good")
                .queryParam("contact", "contact1")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-prompt"))
            .andExpect(model().attribute("title", "Practice 1"))
    }

    @Test
    fun `get should return result if already confirmed`() {
        mockMvc.perform(
            get("/confirm")
                .queryParam("id", "confirmed")
                .queryParam("contact", "contact1")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("title", "Practice 1"))
            .andExpect(model().attribute("success", true))
            .andExpect(model().attribute("successReason", "confirmed"))
    }

    @Test
    fun `get should return result if already declined`() {
        mockMvc.perform(
            get("/confirm")
                .queryParam("id", "declined")
                .queryParam("contact", "contact1")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("title", "Practice 1"))
            .andExpect(model().attribute("success", true))
            .andExpect(model().attribute("successReason", "declined"))
    }

    @Test
    fun `get should return deadline message past cutoff`() {
        mockMvc.perform(
            get("/confirm")
                .queryParam("id", "pastcutoff")
                .queryParam("contact", "contact1")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-deadline"))
            .andExpect(model().attribute("title", "Practice 1"))
    }

    @Test
    fun `update should show error if engagement not found`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "bad")
                .queryParam("contact", "contact1")
                .param("result", "confirmed")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", false))
    }

    @Test
    fun `update should show error if customer not found`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "noCust")
                .queryParam("contact", "contact1")
                .param("result", "confirmed")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", false))
    }

    @Test
    fun `update should record confirmed and display result`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "good")
                .queryParam("contact", "contact1")
                .param("result", "confirmed")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", true))
            .andExpect(model().attribute("successReason", "confirmed"))

        verify {
            engagementService.sendEvent(match<ConfirmationResponseEvent> { it.messageId == "message1" })
        }
    }

    @Test
    fun `update should record confirmed and display result even without finding message`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "good2")
                .queryParam("contact", "contact1")
                .param("result", "confirmed")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", true))
            .andExpect(model().attribute("successReason", "confirmed"))

        verify {
            engagementService.sendEvent(match<ConfirmationResponseEvent> { it.messageId == null })
        }
    }

    @Test
    fun `update should record declined and display result`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "good")
                .queryParam("contact", "contact1")
                .param("result", "declined")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", true))
            .andExpect(model().attribute("successReason", "declined"))
    }

    @Test
    fun `update should show error if past cutoff`() {
        mockMvc.perform(
            post("/confirm")
                .queryParam("id", "pastcutoff")
                .queryParam("contact", "contact1")
                .param("result", "declined")
        )
            .andExpect(status().isOk)
            .andExpect(view().name("confirm-result"))
            .andExpect(model().attribute("success", false))
    }
}