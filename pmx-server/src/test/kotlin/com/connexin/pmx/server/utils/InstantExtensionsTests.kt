package com.connexin.pmx.server.utils

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class InstantExtensionsTests {
    @Test
    fun `isWithin should return correct value`() {
        val now = Instant.now()
        val yesterday = now.minus(1, ChronoUnit.DAYS)
        val tomorrow = now.plus(1, ChronoUnit.DAYS)
        val nextWeek = now.plus(7, ChronoUnit.DAYS)
        val lastWeek = now.minus(7, ChronoUnit.DAYS)

        assertThat(now.isWithin(null, null)).withFailMessage("null start and end date").isTrue
        assertThat(now.isWithin(yesterday, tomorrow)).withFailMessage("within start and end date").isTrue
        assertThat(lastWeek.isWithin(yesterday, tomorrow)).withFailMessage("before start and end date").isFalse
        assertThat(nextWeek.isWithin(yesterday, tomorrow)).withFailMessage("after start and end date").isFalse
        assertThat(now.isWithin(yesterday, null)).withFailMessage("after start date").isTrue
        assertThat(lastWeek.isWithin(yesterday, null)).withFailMessage("before start date").isFalse
        assertThat(now.isWithin(null, tomorrow)).withFailMessage("before start date").isTrue
        assertThat(nextWeek.isWithin(null, tomorrow)).withFailMessage("before start date").isFalse
    }
}