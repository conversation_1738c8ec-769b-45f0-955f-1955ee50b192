package com.connexin.pmx.server.services.impl

import com.connexin.atlas.sl.survey.dto.RemoteSurveyLinkDto
import com.connexin.pmx.server.TestConfig
import com.connexin.pmx.server.exceptions.NotFoundException
import com.connexin.pmx.server.models.Customer
import com.connexin.pmx.server.models.CustomerStatus
import com.connexin.pmx.server.services.AtlasService
import com.connexin.pmx.server.services.CustomerService
import com.connexin.pmx.server.services.SurveyService
import com.connexin.urlformattingtool.service.UrlShortener
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.kotlin.argumentCaptor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.util.ReflectionTestUtils

@Import(TestConfig::class)
@SpringBootTest
@ActiveProfiles("test")
class AtlasSurveyServiceImplTest {
    @Autowired
    lateinit var surveyService: SurveyService

    @Mock
    lateinit var atlasService: AtlasService

    @Mock
    lateinit var customerService: CustomerService

    @Mock
    lateinit var urlShortener: UrlShortener

    @BeforeEach
    fun beforeEach() {
        ReflectionTestUtils.setField(surveyService, "atlasService", atlasService)
        ReflectionTestUtils.setField(surveyService, "urlShortener", urlShortener)
        ReflectionTestUtils.setField(surveyService, "customerService", customerService)
    }

    @Test
    fun `Fetch atlas survey links successfully and convert to list of SurveyLinkDto`() {
        val patientLocalId = "12"
        val surveyUrl = "surveyUrl"
        val shortenedUrl = "shortenedUrl"
        val testCustomer = Customer("123", CustomerStatus.ENABLED, "testCustomer")
        val organizationId = 24L
        Mockito.`when`(atlasService.getOrganizationId(testCustomer)).thenReturn(organizationId)

        val remoteSurveyLinkDto = RemoteSurveyLinkDto()
        remoteSurveyLinkDto.surveyQueueId = 11
        remoteSurveyLinkDto.guid = "testGUID12"
        remoteSurveyLinkDto.url = surveyUrl
        Mockito.`when`(atlasService.getSurveyLinks(organizationId, patientLocalId)).thenReturn(listOf(
            remoteSurveyLinkDto))
        Mockito.`when`(urlShortener.shortenUrl(Mockito.eq(surveyUrl), any())).thenReturn(shortenedUrl)

        val surveyLinkDtos = surveyService.generateSurveyLinks(testCustomer, patientLocalId)

        val customerCaptor = argumentCaptor<Customer>()

        Mockito.verify(customerService).save(customerCaptor.capture())
        assertEquals(organizationId, customerCaptor.firstValue.organizationId)

        assertThat(surveyLinkDtos.size).isEqualTo(1)
        assertThat(surveyLinkDtos[0].url).isEqualTo(shortenedUrl)
    }

    @Test
    fun `atlas-survey empty list of SurveyLinkDtos`() {
        val patientLocalId = "12"
        val organizationId = 24L

        val testCustomer = Customer("123", CustomerStatus.ENABLED, "testCustomer", organizationId = organizationId)
        Mockito.`when`(atlasService.getSurveyLinks(organizationId, patientLocalId)).thenThrow(NotFoundException())

        val surveyLinkDtos = surveyService.generateSurveyLinks(testCustomer, patientLocalId)

        assertThat(surveyLinkDtos.size).isEqualTo(0)
    }

    fun <T> any(): T = Mockito.any()
}