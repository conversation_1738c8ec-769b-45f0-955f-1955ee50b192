{"eventType": "Delivery", "mail": {"timestamp": "2022-03-11T20:30:48.008Z", "source": "<EMAIL>", "sourceArn": "arn:aws:ses:us-east-1:**********:identity/<EMAIL>", "sendingAccountId": "************", "messageId": "3788ec5c-db08-409d-9b85-5f642b87096a", "destination": ["<EMAIL>"], "headersTruncated": false, "headers": [{"name": "From", "value": "<EMAIL>"}, {"name": "Reply-To", "value": "<EMAIL>"}, {"name": "To", "value": "<EMAIL>"}, {"name": "Subject", "value": "New message available on Test Pediatrics patient portal"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "text/plain; charset=UTF-8"}, {"name": "Content-Transfer-Encoding", "value": "7bit"}], "commonHeaders": {"from": ["<EMAIL>"], "replyTo": ["<EMAIL>"], "to": ["<EMAIL>"], "messageId": "3788ec5c-db08-409d-9b85-5f642b87096a", "subject": "New message available on Test Pediatrics patient portal"}, "tags": {"pmx-message-id": ["0000016cb87f5e313e4e13b0"], "pmx-message-type": ["EMAIL"], "ses:operation": ["SendEmail"], "ses:configuration-set": ["pmx"], "ses:source-ip": ["**********"], "ses:from-domain": ["op.health"], "ses:caller-identity": ["pmx"], "ses:outgoing-ip": ["*********"]}}, "delivery": {"timestamp": "2022-03-11T20:30:50.209Z", "processingTimeMillis": 2201, "recipients": ["<EMAIL>"], "smtpResponse": "250 ok dirdel", "reportingMTA": "a8-25.smtp-out.amazonses.com"}}