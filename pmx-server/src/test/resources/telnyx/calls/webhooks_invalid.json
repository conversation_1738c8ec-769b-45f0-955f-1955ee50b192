[{"data": {"event_type": "call.initiated", "id": "9017be1d-9275-4813-b8fb-db1b58b24c11", "occurred_at": "2022-01-21T16:51:46.112097Z", "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "http://localhost:8080/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.initiated", "id": "9017be1d-9275-4813-b8fb-db1b58b24c12", "occurred_at": "2022-01-21T16:51:46.112097Z", "payload": {"call_leg_id": "6b1afa64-7ada-11ec-adcb-02420a0d7f68", "call_session_id": "6b12bfa2-7ada-11ec-849a-02420a0d7f68", "client_state": "Z3JlZXQtaW5pdA==", "connection_id": "1811032298234054132", "direction": "outgoing", "from": "+16065550727", "state": "bridging", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "http://localhost:8080/webhooks/pmx/voice/incoming"}}]