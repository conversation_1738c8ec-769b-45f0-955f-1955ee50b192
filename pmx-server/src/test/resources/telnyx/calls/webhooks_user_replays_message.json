[{"data": {"event_type": "call.initiated", "id": "b8bd8ca4-7fdd-4bb4-85a3-6eb2cb83563e", "occurred_at": "2023-08-09T19:05:48.692465Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "direction": "outgoing", "from": "+16065550727", "state": "bridging", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.answered", "id": "89eb80f8-f531-4a5b-9f22-6670a7117eb0", "occurred_at": "2023-08-09T19:05:53.632478Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "from": "+16065550727", "start_time": "2023-08-09T19:05:48.832463Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.machine.detection.ended", "id": "2eff9e2a-2fd2-4bed-a744-840e30b323e7", "occurred_at": "2023-08-09T19:05:57.812478Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "from": "+16065550727", "result": "human", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.gather.ended", "id": "a557b29b-41d8-4a13-b372-ea1c74025f3f", "occurred_at": "2023-08-09T19:06:13.552523Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digits": "3", "from": "+16065550727", "status": "valid", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.dtmf.received", "id": "134bb918-f1ee-4bde-985f-52cce278f8dc", "occurred_at": "2023-08-09T19:06:13.552523Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digit": "3", "from": "+16065550727", "start_time": "2023-08-09T19:05:48.832463Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.gather.ended", "id": "e252b9c1-6ea8-4893-92e9-77a1148c1c3e", "occurred_at": "2022-01-21T22:36:13.128853Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_session_id": "72c9b0b8-7b0a-11ec-83fe-02420a0d7e68", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "1811032298234054132", "digits": "", "from": "+16065550727", "status": "call_hangup", "to": "+4405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "http://localhost:8080/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.hangup", "id": "a824c1ee-d18b-48c3-8bca-279c6ad21b1f", "occurred_at": "2022-01-21T22:36:13.128853Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_session_id": "72c9b0b8-7b0a-11ec-83fe-02420a0d7e68", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "1811032298234054132", "end_time": "2022-01-21T22:36:13.128853Z", "from": "+16065550727", "hangup_cause": "normal_clearing", "hangup_source": "callee", "sip_hangup_cause": "200", "start_time": "2022-01-21T22:35:35.028853Z", "to": "+4405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "http://localhost:8080/webhooks/pmx/voice/incoming"}}]