[{"data": {"event_type": "call.initiated", "id": "b8bd8ca4-7fdd-4bb4-85a3-6eb2cb83563e", "occurred_at": "2023-08-09T19:05:48.692465Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "direction": "outgoing", "from": "+16065550727", "state": "bridging", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.answered", "id": "89eb80f8-f531-4a5b-9f22-6670a7117eb0", "occurred_at": "2023-08-09T19:05:53.632478Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "from": "+16065550727", "start_time": "2023-08-09T19:05:48.832463Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.machine.detection.ended", "id": "2eff9e2a-2fd2-4bed-a744-840e30b323e7", "occurred_at": "2023-08-09T19:05:57.812478Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "from": "+16065550727", "result": "human", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.gather.ended", "id": "a557b29b-41d8-4a13-b372-ea1c74025f3f", "occurred_at": "2023-08-09T19:06:13.552523Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digits": "1", "from": "+16065550727", "status": "valid", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.dtmf.received", "id": "134bb918-f1ee-4bde-985f-52cce278f8dc", "occurred_at": "2023-08-09T19:06:13.552523Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digit": "1", "from": "+16065550727", "start_time": "2023-08-09T19:05:48.832463Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.started", "id": "66827aa4-16ed-4da0-8be4-d3d66704a66f", "occurred_at": "2023-08-09T19:06:14.112537Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "c2F5LWdvb2RieWUtY29uZmlybQ==", "connection_id": "2142199583214666771"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.ended", "id": "4ade1b32-d4de-43d0-8e4c-88c289d330cf", "occurred_at": "2023-08-09T19:06:15.832462Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "c2F5LWdvb2RieWUtY29uZmlybQ==", "connection_id": "2142199583214666771", "status": "completed"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.hangup", "id": "a23baca0-09af-41f8-a1d6-7841b304be22", "occurred_at": "2023-08-09T19:06:16.052464Z", "payload": {"call_control_id": "v3:nfJgiCx4xzaihV7goQlj9c8BfbgR6JUiPDPeIHayg0yAwcKU7OcEaw", "call_leg_id": "c03a9778-36e7-11ee-b799-02420a0d4168", "call_session_id": "c0364cea-36e7-11ee-b400-02420a0d4168", "client_state": "aGFuZ3VwLWNvbmZpcm0=", "connection_id": "2142199583214666771", "end_time": "2023-08-09T19:06:16.052464Z", "from": "+16065550727", "hangup_cause": "normal_clearing", "hangup_source": "caller", "sip_hangup_cause": "unspecified", "start_time": "2023-08-09T19:05:48.832463Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}]