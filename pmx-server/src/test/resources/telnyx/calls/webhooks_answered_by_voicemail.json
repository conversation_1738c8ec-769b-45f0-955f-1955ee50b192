[{"data": {"event_type": "call.initiated", "id": "2b645c37-fe33-466d-9ac8-ec7c9ee398fe", "occurred_at": "2023-08-09T18:26:07.259018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "direction": "outgoing", "from": "+16065550727", "state": "bridging", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.answered", "id": "4f24eb4a-dece-41ad-bfe3-ca5a5713da74", "occurred_at": "2023-08-09T18:26:40.459020Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "from": "+16065550727", "start_time": "2023-08-09T18:26:07.339019Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.machine.detection.ended", "id": "39c936a3-cbda-419b-a500-c69fcca5b353", "occurred_at": "2023-08-09T18:26:43.219018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "from": "+16065550727", "result": "machine", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.gather.ended", "id": "238fd3b5-3c93-45ac-99f7-d97cf607eb9c", "occurred_at": "2023-08-09T18:26:43.459018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digits": "", "from": "+16065550727", "status": "cancelled_amd", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.machine.greeting.ended", "id": "c696168f-068c-49c7-a090-0048bcacd3ca", "occurred_at": "2023-08-09T18:27:00.439018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "from": "+16065550727", "result": "beep_detected", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.started", "id": "1c6d18fc-27c2-492d-a304-a6073750272b", "occurred_at": "2023-08-09T18:27:01.239020Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "c2F5LXZt", "connection_id": "2142199583214666771"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.ended", "id": "5f122871-fed0-42d4-8fe7-30744b5735af", "occurred_at": "2023-08-09T18:27:13.039018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "c2F5LXZt", "connection_id": "2142199583214666771", "status": "completed"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.hangup", "id": "388e37b5-fcf3-4d6d-91c0-8fc1c69dd7ca", "occurred_at": "2023-08-09T18:27:13.759018Z", "payload": {"call_control_id": "v3:BOJCQTCnDZGNFDOJvGkVy_UnaLvtbXzaZRZKi7Hk7ubORLAnaYjE7A", "call_leg_id": "34c7fe10-36e2-11ee-8197-02420a0d4168", "call_session_id": "34c2ae6a-36e2-11ee-ab93-02420a0d4168", "client_state": "aGFuZ3VwLXZt", "connection_id": "2142199583214666771", "end_time": "2023-08-09T18:27:13.759018Z", "from": "+16065550727", "hangup_cause": "normal_clearing", "hangup_source": "caller", "sip_hangup_cause": "unspecified", "start_time": "2023-08-09T18:26:07.339019Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}]