[{"data": {"event_type": "call.initiated", "id": "c81be296-b68d-46ff-b87a-0eca37235895", "occurred_at": "2023-08-09T18:52:33.578020Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "direction": "outgoing", "from": "+16065550727", "state": "bridging", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.answered", "id": "f11f6ba3-82c1-4029-8b62-ae16775df28c", "occurred_at": "2023-08-09T18:52:39.378016Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "aW5pdA==", "connection_id": "2142199583214666771", "from": "+16065550727", "start_time": "2023-08-09T18:52:33.738035Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.machine.detection.ended", "id": "50243a4b-1c61-47f8-af2d-412cb261b69c", "occurred_at": "2023-08-09T18:52:43.838015Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "from": "+16065550727", "result": "human", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.gather.ended", "id": "b8bc275f-4b44-4870-947c-06a1393aaa2b", "occurred_at": "2023-08-09T18:54:19.938016Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "Z2F0aGVyLWNvbmZpcm0=", "connection_id": "2142199583214666771", "digits": "", "from": "+16065550727", "status": "invalid", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.started", "id": "e630b213-6cd9-4617-af70-e84a62534213", "occurred_at": "2023-08-09T18:54:20.758016Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "c2F5LW5vLXJlc3BvbnNl", "connection_id": "2142199583214666771"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.speak.ended", "id": "667a6417-58a4-4bb5-a144-d5e964a923dd", "occurred_at": "2023-08-09T18:54:27.758016Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "c2F5LW5vLXJlc3BvbnNl", "connection_id": "2142199583214666771", "status": "completed"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}, {"data": {"event_type": "call.hangup", "id": "9028f303-80de-4ad4-81ee-9d5d81fee366", "occurred_at": "2023-08-09T18:54:28.418018Z", "payload": {"call_control_id": "v3:K2SyiNkssDbM12w4vbMcYjvMpL33VIF0JiU38bPQ3mNpfqeUaxXQIA", "call_leg_id": "e64e79d6-36e5-11ee-8c1f-02420a0de768", "call_session_id": "e6497472-36e5-11ee-9ea9-02420a0de768", "client_state": "aGFuZ3VwLW5vLXJlc3BvbnNl", "connection_id": "2142199583214666771", "end_time": "2023-08-09T18:54:28.418018Z", "from": "+16065550727", "hangup_cause": "normal_clearing", "hangup_source": "caller", "sip_hangup_cause": "unspecified", "start_time": "2023-08-09T18:52:33.738035Z", "to": "+14405551234"}, "record_type": "event"}, "meta": {"attempt": 1, "delivered_to": "https://ef72-173-90-242-171.ngrok-free.app/webhooks/pmx/voice/incoming"}}]