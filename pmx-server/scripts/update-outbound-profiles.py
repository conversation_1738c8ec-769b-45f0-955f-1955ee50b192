from getpass import getpass
import requests
import json


def getUrl(env):
    return {
        "dev": "https://pmx-dev.op.healthcare/api/v1/customers",
        "prod": "https://pmx.op.healthcare/api/v1/customers"
    }[env.lower()]


url = getUrl(input("Which environment [prod, dev]? "))
username = input("Admin username: ")
password = getpass(f"Password for {username}: ")

print(
    f"Retrieving outbound voice profiles for all customers at {url} as {username}")

outbound_profile_ids = []
results = True
page = 0

while results:
    paged_results = requests.get(
        url + f"?page={page}&size=100&sort=id", auth=(username, password)).json()
    results = paged_results.get("content", [])
    for customer in results:
        outbound_profile_id = customer["telnyxConfig"]["outboundVoiceProfileId"]
        if (outbound_profile_id is not None):
            outbound_profile_ids.append(outbound_profile_id)
    page += 1

if (len(outbound_profile_ids) == 0):
    print('Found no outbound voice profiles, nothing to do. Goodbye!')
    exit()

print(f"Found {len(outbound_profile_ids)} outbound voice profiles: ",
      outbound_profile_ids)

telnyx_api_token = getpass("Telnyx API token: ")
telnyx_url = "https://api.telnyx.com/v2/outbound_voice_profiles/"
whitelisted_destinations = ["US", "CA", "PR", "VI"]

for outbound_profile_id in outbound_profile_ids:
    headers = {
        "Authorization": f"Bearer {telnyx_api_token}",
        "Content-Type": "application/json"
    }
    data = json.dumps({'whitelisted_destinations': whitelisted_destinations})

    res = requests.patch(telnyx_url + outbound_profile_id, headers=headers, data=data).json()

    if (res["data"]["whitelisted_destinations"] == whitelisted_destinations):
        print(
            f"Outbound voice profile {outbound_profile_id} updated successfully")
    else:
        print(f"FAILURE: {outbound_profile_id} could not be updated {res} - {res.content}")
