<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <version>2.7.18</version> <!-- lookup parent from repository -->
        <relativePath/>
    </parent>

    <groupId>com.connexin.pmx</groupId>
    <artifactId>pmx-server</artifactId>
    <name>pmx-server</name>
    <description>Server that provides PMX (Patient Messaging eXchange) functionality.</description>
    <version>1.18.0-SNAPSHOT</version>

    <properties>
        <java.version>17</java.version>
        <kotlin.version>1.8.10</kotlin.version>
        <kotlin-coroutines.version>1.6.4</kotlin-coroutines.version>
        <aws.sdk.version>1.12.780</aws.sdk.version>
        <telnyx.version>3.6.0</telnyx.version>
        <bouncycastle.version>1.70</bouncycastle.version>
        <google-libphonenumber.version>8.12.41</google-libphonenumber.version>
        <valiktor.version>0.12.0</valiktor.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <springmockk.version>3.1.0</springmockk.version>
        <mockk.version>1.12.2</mockk.version>
        <micrometer-registry-dynatrace.version>1.8.2</micrometer-registry-dynatrace.version>
        <jackson.version>2.13.4</jackson.version>
        <unirest.version>3.13.7</unirest.version>
        <caffeine.version>3.1.4</caffeine.version>
        <json.version>20220924</json.version>
        <springdoc-openapi.version>1.6.14</springdoc-openapi.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <schedlock-spring.version>4.44.0</schedlock-spring.version>
        <sl.version>3.27.27</sl.version>
        <okhttp.version>4.10.0</okhttp.version>
        <jmail.version>1.6.1</jmail.version>
        <security-library.version>1.14.0</security-library.version>
        <url.formatter.version>1.0.0</url.formatter.version>
    </properties>


    <dependencies>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-security</artifactId>
            <version>${springdoc-openapi.version}</version>
        </dependency>
        <!-- JSON-P patch support -->
        <dependency>
            <groupId>com.github.java-json-tools</groupId>
            <artifactId>json-patch</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sanctionco.jmail</groupId>
            <artifactId>jmail</artifactId>
            <version>${jmail.version}</version>
        </dependency>
        <dependency>
            <groupId>com.connexin.atlas.sl</groupId>
            <artifactId>practice</artifactId>
            <version>${sl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.connexin.atlas.sl</groupId>
            <artifactId>survey</artifactId>
            <version>${sl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <!-- kotlin -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
            <version>${kotlin-coroutines.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-csv</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- aws -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sesv2</artifactId>
            <version>${aws.sdk.version}</version>
        </dependency>

        <!-- Telnyx -->
        <dependency>
            <groupId>com.telnyx.sdk</groupId>
            <artifactId>telnyx</artifactId>
            <version>${telnyx.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- needed to validate Telnyx webhooks -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>

        <!-- phone number validation and formatting -->
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>${google-libphonenumber.version}</version>
        </dependency>

        <!-- validation -->
        <dependency>
            <groupId>org.valiktor</groupId>
            <artifactId>valiktor-spring-boot-starter</artifactId>
            <version>${valiktor.version}</version>
        </dependency>

        <!-- Dynatrace Micrometer metrics -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-dynatrace</artifactId>
            <version>${micrometer-registry-dynatrace.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!-- SchedLock - ensuring only one poller at a time -->
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${schedlock-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-mongo</artifactId>
            <version>${schedlock-spring.version}</version>
        </dependency>

        <dependency>
            <groupId>com.connexin.auth</groupId>
            <artifactId>security-library</artifactId>
            <version>${security-library.version}</version>
        </dependency>

        <dependency>
            <groupId>com.connexin.auth</groupId>
            <artifactId>security-models</artifactId>
            <version>${security-library.version}</version>
        </dependency>


        <dependency>
            <groupId>com.connexin</groupId>
            <artifactId>url-formatting-tool</artifactId>
            <version>${url.formatter.version}</version>
        </dependency>
        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-junit-jupiter</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>spring-security-test</artifactId>
            <groupId>org.springframework.security</groupId>
            <scope>test</scope>
        </dependency>

        <!-- Use mockk + spring mockk integration rather than mockito -->
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId>
            <version>3.1.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.konghq</groupId>
            <artifactId>unirest-java</artifactId>
            <version>${unirest.version}</version>
        </dependency>
        <dependency>
            <groupId>com.konghq</groupId>
            <artifactId>unirest-mocks</artifactId>
            <version>${unirest.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty</artifactId>
            <version>5.15.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito.kotlin</groupId>
            <artifactId>mockito-kotlin</artifactId>
            <version>4.1.0</version>
            <scope>test</scope>
        </dependency>


        <!-- Rate limiting -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-spring-boot2</artifactId>
            <version>${resilience4j.version}</version>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/main/kotlin</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>-Dspring.application.admin.enabled=true</jvmArguments>
                    <profiles>
                        <profile>test</profile>
                    </profiles>
                    <environmentVariables>
                        <spring.cache.type>caffeine</spring.cache.type>
                    </environmentVariables>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-integration-test</id>
                        <goals>
                            <goal>start</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-integration-test</id>
                        <goals>
                            <goal>stop</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-maven-plugin</artifactId>
                <version>1.1</version>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <outputDir>../pmx-sdk/src/main/resources</outputDir>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>kapt</id>
                        <goals>
                            <goal>kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>src/main/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths>
                                <annotationProcessorPath>
                                    <groupId>org.mapstruct</groupId>
                                    <artifactId>mapstruct-processor</artifactId>
                                    <version>${org.mapstruct.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                        </configuration>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <proc>none</proc>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
                <executions>
                    <!-- Replacing default-compile as it is treated specially by maven -->
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <!-- Replacing default-testCompile as it is treated specially by maven -->
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.4.4</version>
                        <configuration>
                            <from>
                                <image>officepracticum/java-17-rds:base</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                            </from>
                            <to>
                                <image>officepracticum/pmx</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.DOCKER_TAG}</tag>
                                </tags>
                            </to>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>dev-staging</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.4.4</version>
                        <configuration>
                            <from>
                                <image>officepracticum/java-17-rds:dev</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                            </from>
                            <to>
                                <image>officepracticum/pmx</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>${env.DOCKER_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <entrypoint>INHERIT</entrypoint>
                                <args>
                                    <arg>java</arg>
                                    <arg>-cp</arg>
                                    <arg>/app/resources:/app/classes:/app/libs/*</arg>
                                    <arg>com.connexin.pmx.server.ApplicationKt</arg>
                                </args>
                            </container>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>production</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.4.4</version>
                        <configuration>
                            <from>
                                <image>officepracticum/java-17-rds:prod</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                            </from>
                            <to>
                                <image>officepracticum/pmx</image>
                                <auth>
                                    <username>${env.DOCKER_USER}</username>
                                    <password>${env.DOCKER_PASS}</password>
                                </auth>
                                <!--JAVA_TOOL_OPTIONS-->
                                <tags>
                                    <tag>${env.DOCKER_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <entrypoint>INHERIT</entrypoint>
                                <args>
                                    <arg>java</arg>
                                    <arg>-cp</arg>
                                    <arg>/app/resources:/app/classes:/app/libs/*</arg>
                                    <arg>com.connexin.pmx.server.ApplicationKt</arg>
                                </args>
                            </container>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>connexin-release</id>
            <name>Connexin Releases</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-release/</url>
        </repository>
        <snapshotRepository>
            <id>connexin-snapshot</id>
            <name>Connexin Snapshots</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>connexin-snapshot</id>
            <name>Connexin Snapshots</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>connexin-release</id>
            <name>Connexin Releases</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-release</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
