[{"ParameterKey": "ClusterName", "ParameterValue": "Atlas-Third-Party"}, {"ParameterKey": "EnvironmentName", "ParameterValue": "<PERSON>"}, {"ParameterKey": "ServiceName", "ParameterValue": "PMX"}, {"ParameterKey": "ServiceDiscovery", "ParameterValue": "ns-x7a5z5yz6gtrbf3t"}, {"ParameterKey": "VPCID", "ParameterValue": "vpc-fb761e9e"}, {"ParameterKey": "DefaultAppSG", "ParameterValue": "sg-2be5394f"}, {"ParameterKey": "PrivateSubnets", "ParameterValue": "subnet-04b24e5d9ac17a781"}, {"ParameterKey": "DesiredCount", "ParameterValue": "2"}, {"ParameterKey": "ImageUrl", "ParameterValue": "officepracticum/pmx:__DOCKER_TAG__"}, {"ParameterKey": "TaskCpu", "ParameterValue": "512"}, {"ParameterKey": "TaskMemory", "ParameterValue": "1024"}, {"ParameterKey": "ContainerCpu", "ParameterValue": "512"}, {"ParameterKey": "Container<PERSON><PERSON>ory", "ParameterValue": "1024"}, {"ParameterKey": "ContainerPort", "ParameterValue": "8080"}, {"ParameterKey": "Role", "ParameterValue": "ecsTaskExecutionRole"}, {"ParameterKey": "SumoLambda", "ParameterValue": "sumologic-digest-atlas-logs"}, {"ParameterKey": "MongoSecret", "ParameterValue": "dev/atlas-pmx/mongo-AutRP1"}, {"ParameterKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ParameterValue": "docker_creds-LhUGxY"}, {"ParameterKey": "AwsSecret", "ParameterValue": "dev/atlas-pmx/aws-NQHbf2"}, {"ParameterKey": "EnvSesConfigurationSet", "ParameterValue": "pmx"}, {"ParameterKey": "EnvDefaultFromEmailAddress", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "EnvAwsRegion", "ParameterValue": "us-east-1"}, {"ParameterKey": "DefaultLogLevel", "ParameterValue": "TRACE"}, {"ParameterKey": "EnvServerPort", "ParameterValue": "8080"}, {"ParameterKey": "SpringBootAdminSecret", "ParameterValue": "dev/atlas/springbootadmin-aVmbp6"}, {"ParameterKey": "EnvSpringBootAdminClientEnabled", "ParameterValue": "false"}, {"ParameterKey": "EnvSpringBootAdminClientUrl", "ParameterValue": ""}, {"ParameterKey": "TargetGroupArn", "ParameterValue": "arn:aws:elasticloadbalancing:us-east-1:125643248614:targetgroup/Atlas-PMX-Dev/9ada27c9151bd7b2"}, {"ParameterKey": "SnsSubscriptionEndpoint", "ParameterValue": "https://applications-dev.op.healthcare/pmx/webhooks/pmx/email/events"}, {"ParameterKey": "SesConfigSetName", "ParameterValue": "pmx"}, {"ParameterKey": "TelnyxSecret", "ParameterValue": "dev/atlas-pmx/telnyx-M5k2vc"}, {"ParameterKey": "EnvPmxBaseUrl", "ParameterValue": "https://applications-dev.op.healthcare/pmx"}, {"ParameterKey": "EnvPmxDispatcherPageSize", "ParameterValue": "1000"}, {"ParameterKey": "EnvPmxDispatcherInitialDelay", "ParameterValue": "20000"}, {"ParameterKey": "EnvPmxDispatcherFixedDelay", "ParameterValue": "20000"}, {"ParameterKey": "DynatraceSecret", "ParameterValue": "dev/dynatrace/api-2cHyXj"}, {"ParameterKey": "EnvDynatraceEnabled", "ParameterValue": "false"}, {"ParameterKey": "EnvPmxProvisioningPageSize", "ParameterValue": "50"}, {"ParameterKey": "EnvPmxProvisioningInitialDelay", "ParameterValue": "20000"}, {"ParameterKey": "EnvPmxProvisioningFixedDelay", "ParameterValue": "60000"}, {"ParameterKey": "EnvPmxProvisioningDisableVendorCalls", "ParameterValue": "false"}, {"ParameterKey": "EnvPmxProvisioningMaxAttempts", "ParameterValue": "5"}, {"ParameterKey": "EnvPmxEnvironment", "ParameterValue": "DEV"}, {"ParameterKey": "PmxUsersSecret", "ParameterValue": "dev/atlas-pmx/users-jrF9KN"}, {"ParameterKey": "EnvPmxCallTimeout", "ParameterValue": "120"}, {"ParameterKey": "EnvPmxCallTimeLimit", "ParameterValue": "3600"}, {"ParameterKey": "EnvCacheCustomersDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheCustomersMax", "ParameterValue": "500"}, {"ParameterKey": "EnvCacheCustomersByCredentialsDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheCustomersByCredentialsMax", "ParameterValue": "500"}, {"ParameterKey": "EnvCacheMessagesByRemoteIdDuration", "ParameterValue": "5"}, {"ParameterKey": "EnvCacheMessagesByRemoteIdMax", "ParameterValue": "1000"}, {"ParameterKey": "EnvCacheWebhooksDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheWebhooksMax", "ParameterValue": "2000"}, {"ParameterKey": "EnvCacheSesSendQuotaDuration", "ParameterValue": "5"}, {"ParameterKey": "EnvZipUrl", "ParameterValue": "https://kczm6uix68.execute-api.us-east-1.amazonaws.com/zip"}, {"ParameterKey": "EnvTelnyxEngagementEnglishMessagingProfileId", "ParameterValue": "40018714-7518-4ad2-baf7-7de2a6355ffb"}, {"ParameterKey": "EnvTelnyxEngagementSpanishMessagingProfileId", "ParameterValue": "40018776-32b5-4697-b407-5cf26aa51a64"}, {"ParameterKey": "EnvTelnyxEngagementEnglishVoiceNumbers", "ParameterValue": "+18556541760"}, {"ParameterKey": "EnvTelnyxEngagementSpanishVoiceNumbers", "ParameterValue": "+18556542330"}, {"ParameterKey": "EnvTelnyxEngagementCallControlConnectionId", "ParameterValue": "2142199583214666771"}, {"ParameterKey": "EnvPmxConfirmationBaseUrl", "ParameterValue": "https://dev.healthconfirmations.com"}, {"ParameterKey": "EnvCacheZipCodesDuration", "ParameterValue": "60"}, {"ParameterKey": "EnvCacheZipCodesMax", "ParameterValue": "2000"}, {"ParameterKey": "RedisSecret", "ParameterValue": "/dev/PMXPlus-Redis-W2e8tw"}, {"ParameterKey": "EnvRedisEnabled", "ParameterValue": "true"}, {"ParameterKey": "SlackSecret", "ParameterValue": "dev/pmx/slack-gA2jgQ"}, {"ParameterKey": "EnvDeliveryStatsCron", "ParameterValue": "-"}, {"ParameterKey": "ShortenerSecret", "ParameterValue": "dev/pmx/shortener-YX6IKI"}, {"ParameterKey": "PmxKeycloakSecret", "ParameterValue": "dev/pmx/keycloak-MiPBu3"}, {"ParameterKey": "EnvKeycloakBridgeScope", "ParameterValue": "read:bridge write:bridge"}, {"ParameterKey": "EnvBridgeUrl", "ParameterValue": "https://applications-dev.op.healthcare/bridge"}, {"ParameterKey": "EnvPmxEngagementSchedulerPageSize", "ParameterValue": "1000"}, {"ParameterKey": "EnvPmxEngagementSchedulerParallelBatchSize", "ParameterValue": "100"}, {"ParameterKey": "EnvPmxEngagementDelayTimeSeconds", "ParameterValue": "300"}, {"ParameterKey": "AtlasSecret", "ParameterValue": "dev/pmx/atlas-sHiB4C"}]