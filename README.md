# PMX+

## Table of Contents
- [Description](#description)
- [Prerequisites](#prerequisites)
- [Configuration](#configuration)
- [Running the Application](#running-the-application)
- [Testing](#testing)
- [Pending Work](#pending-work)
- [API Documentation](#api-documentation)
- [Architecture Diagrams](#architecture-diagrams)
- [License](#license)
- [Maintainer](#maintainer)
- [Known Issues](#known-issues)
- [Versioning](#versioning)
- [CI/CD](#cicd)

## Description
The **PMX+ (Patient Message eXchange Plus)** is a product that allows practices to engage patients using SMS, Voice, and
Email to perform automated tasks such as appointment confirmation and reminders, check-in, or point-to-point
communication like email campaigns, recalls, etc.

This multi-module project contains the server, which stores customer configuration and performs the task of
communicating with patient/patient contacts using Telnyx (voice, SMS) or AWS SES (email). Services, such as
OPEN, can interact with the REST APIs to set up customer preferences, send messages, and check
back on responses to those messages.

The project also contains a Java client SDK module which is auto-generated from the server's OpenAPI
specification, which can be imported into a client app to do tasks like configure customers or send messages.

* `pmx-server` - The actual server hosting the APIs, entities, business logic, etc.
* `pmx-sdk` - A Java client module that can be imported into other applications to interact with the server's APIs. It contains models, enums, etc. so you don't have to generate them in your project by hand and keep them in sync with the server as it evolves.

## Prerequisites
Before running this project, ensure the following software is installed on your system:
- **Java 11** or higher
- **Maven 3.6+**
- **Docker**
- Local **port 27017** (MongoDB) to be open and unused
- A free **NGrok account**, the CLI tool installed (for testing Telnyx and AWS webhooks)


## Configuration
Review the configurations in the current `pmx-server/src/main/resources/application-local-dev.yml` file
Obtain the developer env vars necessary to run the application locally from a tech lead, and add to your `Application` profile (see table below)

Required env vars (The last two are essential):

| Name                                               | Value                                        | Comments                                                                         |
|----------------------------------------------------|----------------------------------------------|----------------------------------------------------------------------------------|
| ENV_PMX_BASE_URL                                   | [copy HTTPS forwarding URL from NGrok]       |                                                                                  |
| ENV_TELNYX_ENGAGEMENT_ENGLISH_MESSAGING_PROFILE_ID | 40018714-7518-4ad2-baf7-7de2a6355ffb         | This is the DEV PMX+ Engagement (english) profile                                |
| ENV_TELNYX_ENGAGEMENT_SPANISH_MESSAGING_PROFILE_ID | 40018776-32b5-4697-b407-5cf26aa51a64         | This is the DEV PMX+ Engagement (spanish) profile                                |
| ENV_TELNYX_ENGAGEMENT_ENGLISH_VOICE_NUMBERS        | +18556541760                                 | This is the DEV PMX+ Engagement (english) number                                 |
| ENV_TELNYX_ENGAGEMENT_SPANISH_VOICE_NUMBERS        | +***********                                 | This is the DEV PMX+ Engagement (spanish) number                                 |
| ENV_TELNYX_ENGAGEMENT_CALL_CONTROL_CONNECTION_ID   | 2142199583214666771                          | This is the DEV PMX+ Engagement call control ID                                  |
| ENV_TELNYX_API_KEY                                 | test                                         | Can be found in Telnyx portal > Account Settings > Keys & Credentials > API Keys |
| ENV_TELNYX_PUBLIC_KEY                              | dGVzdHRlc3R0ZXN0dGVzdHRlc3R0ZXN0dGVzdHRlc3Q= | Can be found in Telnyx portal > Account Settings > Public Key                    |


## Running the Application

### Step 1: Clone the Repository
```bash
<NAME_EMAIL>:op-se/atlas-pmx.git
```

### Step 2: Run ngrok (Optional)
In a terminal, run
```bash
ngrok http 8080
```
when testing webhooks from Telnyx or AWS

### Step 3: Open the project in Intellij

### Step 4: Run docker
Please reference the ``docker-compose.yml`` file.
```bash
docker compose up -d
```

### Step 5: Build project
Run maven
```bash
clean install -U -Dmaven.wagon.http.ssl.insecure=true -Daether.connector.https.securityMode=insecure -DskipTests
```

### Step 6: Run application
Edit the Spring Boot configuration for `Application` and set the active profile to ``local-dev``
And hit ``Run``

## Testing
Run the unit and integration tests using Maven:
```bash
clean install -U -Dmaven.wagon.http.ssl.insecure=true -Daether.connector.https.securityMode=insecure
```

## Pending Work
As pending work we need to completely get rid of atlas naming for this project as this is not supposed to have any connection
with the rest of atlas microservices and should keep agnostic from atlas data and work.

We need to remove the entire concept of ATLAS, move the entire services out of the ATLAS cloud infrastructure and git repository naming without ATLAS.
The AWS secrets named after atlas used for this project should also need to be renamed

## API Documentation

The server uses [OpenAPI](https://springdoc.org/) to document the available REST APIs. You can view the online documentation
locally by:

1. Start the project as per the instructions above
2. Go to [http://localhost:8080/swagger-ui.html](http://localhost:8080/swagger-ui.html) in your browser

Alternatively, you can view the documentation for the version that has been deployed to Development by going to
[https://applications-dev.op.healthcare/pmx/swagger-ui.html](https://applications-dev.op.healthcare/pmx/swagger-ui.html)

### Updating `pmx-sdk`

`pmx-sdk` is configured to automatically generate all the classes
necessary to use the enabled operations during compilation using OpenAPI. 

If you update the server and want to rebuild the client and install the result into your local maven cache, simply run
`mvn install`. This will build and test your server, output a new openapi specification file that reflects the current configuration,
regenerates and compiles `pmx-sdk`, then installs it into your local maven cache.

### Bumping Version

When you need to bump the version, this command makes it easy:

```bash
mvn versions:set -DnewVersion={your semantic version here} -DprocessAllModules -DgenerateBackupPoms=false
git commit -am "release: {your semantic version here}" # commits changes to the three poms that will be modified with the new version
```

### Where does `pmx-server` save the specification file when it does the export?

It will export to `pmx-sdk/src/main/resources/openapi.json`, overwriting the existing file. `pmx-sdk` will read this file
when generating sources.

### What do I do if the specification file is missing or out of date?

Try one of the following:

* Run `mvn install`, which will regenerate the specification file amongst the usual installation tasks
* Run `mvn verify`, which will compile, run tests, and export the specification file, but not install

### How do I recompile/regenerate `pmx-sdk` without regenerating the specification?

Run `mvn compile`. It will still build `pmx-server`, but won't run tests or update the specification file.
However, it will generate sources and build `pmx-sdk` using the current `openapi.json` specification file.

### Do I need to check in changes to `openapi.json`?

Not really. Normally, CI/CD will ensure the specification file is updated, and `pmx-sdk` is generated from that copy. It will
overwrite whatever is currently committed in the repo anyway. However, there is a copy of the specification in there
simply so developers don't have a broken build when the checkout the project for the first time. Just be aware
the checked in copy may be out of date, so if you're building you can get a local copy of the sdk installed into your 
maven cache, remember to just do `mvn install`.

## Architecture Diagrams
- [Confluence Link](https://opservice.atlassian.net/wiki/spaces/TEC/pages/1770403/PMX)

## License
This project is licensed under the OP License. See the LICENSE file for more details.

## Maintainer
- ClinPeds Team

## Known Issues
- Right now we can't have more than one instance running because some messages are sent more than once (it seems that the Jobs Lock is not working).

## Versioning
- [Semantic Versioning](https://semver.org/)
- [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)
- [GitFlow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow)

## CI/CD
Please reference the `ci` folder for the Cloudformation template and parameter files.
Please reference the `gitlab-ci.yml` for the ci/cd process.