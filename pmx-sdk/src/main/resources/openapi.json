{"openapi": "3.0.1", "info": {"title": "PMX+", "description": "PMX+ (Patient Message eXchange Plus) is a product that allows practices to engage patients using SMS, Voice, and \nEmail to perform automated tasks such as appointment confirmation and reminders, check-in, or point-to-point \ncommunication like email campaigns, recalls, etc.", "contact": {"name": "Office Practicum", "url": "https://www.officepracticum.com/supporthub", "email": "<EMAIL>"}, "license": {"name": "Proprietary"}, "version": "2.0.0"}, "servers": [{"url": "https://applications-dev.op.healthcare/pmx", "description": "Development"}, {"url": "https://applications.op.healthcare/pmx", "description": "Production"}], "security": [{"bearerAuth": []}], "tags": [{"name": "Engagement", "description": "Endpoints for managing patient engagements."}, {"name": "Customer Admin", "description": "Endpoints for administrators to manage customers."}, {"name": "Customer", "description": "Endpoints for managing the authenticated user's customer configuration."}, {"name": "Template Admin", "description": "Endpoints for administrators to manage templates"}, {"name": "Template", "description": "Endpoints for managing engagement templates."}, {"name": "Response", "description": "Endpoints for managing responses from contacts."}, {"name": "Statistics Admin", "description": "Endpoints for administrators to view statistics related to usage."}, {"name": "Resource", "description": "Endpoint for updating resources referenced within engagements."}], "paths": {"/api/v2/engagements/{id}": {"get": {"tags": ["Engagement"], "summary": "Gets an engagement record.", "operationId": "getById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The engagement record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Engagement"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "put": {"tags": ["Engagement"], "summary": "Updates an existing engagement.", "operationId": "update", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEngagementRequest"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Existing engagement was updated.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Engagement"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "delete": {"tags": ["Engagement"], "summary": "Deletes an existing engagement.", "operationId": "delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Existing engagement was deleted."}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "patch": {"tags": ["Engagement"], "summary": "Patches an existing engagement.", "operationId": "patch", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/JsonPatch"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Existing engagement was patched.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Engagement"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/customer/engagement-rules/{ruleId}": {"get": {"tags": ["Customer"], "summary": "Gets an existing engagement rule for the current customer.", "operationId": "getEngagementRule", "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement rule not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Existing engagement rule.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "put": {"tags": ["Customer"], "summary": "Creates or replaces an engagement rule with the provided ID for the current customer.", "operationId": "saveEngagementRule", "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement rule was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Engagement rule was saved for the customer.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "delete": {"tags": ["Customer"], "summary": "Deletes an existing engagement rule for the current customer.", "operationId": "deleteEngagementRule", "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement rule not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Existing engagement rule was deleted for the customer."}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/admin/templates/{id}": {"get": {"tags": ["Template Admin"], "summary": "Gets a template by ID.", "operationId": "getById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON> not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The template record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "put": {"tags": ["Template Admin"], "summary": "Updates an existing template.", "operationId": "update_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateRequest"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "One or more fields are invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The template was updated.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "delete": {"tags": ["Template Admin"], "summary": "Deletes an existing template.", "operationId": "delete_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "The template was deleted."}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/admin/customers/{customerId}/engagement-rules/{ruleId}": {"get": {"tags": ["Customer Admin"], "summary": "Gets an existing engagement rule for a customer.", "operationId": "getCustomerEngagementRule", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement rule not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Existing engagement rule.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "put": {"tags": ["Customer Admin"], "summary": "Updates an existing engagement rule for a customer.", "operationId": "updateCustomerEngagementRule", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement rule not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement rule was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Existing engagement rule was updated for the customer.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "delete": {"tags": ["Customer Admin"], "summary": "Deletes an existing engagement rule for a customer.", "operationId": "deleteCustomerEngagementRule", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement rule not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "Existing engagement rule was deleted for the customer."}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/webhooks/pmx/voice/incoming": {"post": {"tags": ["pmx-webhooks-controller"], "operationId": "handleIncoming", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}, "/webhooks/pmx/messaging/status": {"post": {"tags": ["pmx-webhooks-controller"], "operationId": "status", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}, "/webhooks/pmx/email/events": {"post": {"tags": ["pmx-webhooks-controller"], "operationId": "handleEvent", "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/v2/engagements": {"get": {"tags": ["Engagement"], "summary": "Gets an engagement record for an appointment.", "operationId": "getByAppointmentId", "parameters": [{"name": "appointmentId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer or engagement not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The engagement record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Engagement"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "post": {"tags": ["Engagement"], "summary": "Creates a new engagement.", "operationId": "create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEngagementRequest"}}}, "required": true}, "responses": {"409": {"description": "An engagement for the specified appointment already exists.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "New engagement was created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Engagement"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/engagements/advance-engagement": {"post": {"tags": ["Engagement"], "summary": "Triggers an engagement event.", "operationId": "advanceEngagement", "parameters": [{"name": "x-opmed", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementEventDto"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement event was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Engagement event triggered", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseString"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/customer/engagement-rules": {"get": {"tags": ["Customer"], "summary": "Gets all engagement rules for the current customer.", "operationId": "getEngagementRules", "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A list of engagement rules for the customer.", "content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/EngagementRule"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "post": {"tags": ["Customer"], "summary": "Creates a new engagement rule for the current customer.", "operationId": "createEngagementRule", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}, "required": true}, "responses": {"409": {"description": "An engagement rule with the specified ID already exists.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement rule was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "New engagement rule was created for the customer.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/admin/templates": {"get": {"tags": ["Template Admin"], "summary": "Gets a paginated list of templates.", "operationId": "find", "parameters": [{"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A page of templates.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTemplate"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "post": {"tags": ["Template Admin"], "summary": "Creates a new template.", "operationId": "create_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplateRequest"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "One or more fields are invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The template was created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/admin/customers": {"post": {"tags": ["Customer Admin"], "operationId": "createCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerRequest"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCustomer"}}}}}}}, "/api/v2/admin/customers/{customerId}/engagement-rules": {"get": {"tags": ["Customer Admin"], "summary": "Gets all engagement rules for a customer.", "operationId": "getCustomerEngagementRules", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A list of engagement rules for the customer.", "content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/EngagementRule"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "post": {"tags": ["Customer Admin"], "summary": "Creates a new engagement rule for a customer.", "operationId": "createCustomerEngagementRule", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}, "required": true}, "responses": {"409": {"description": "An engagement rule with the specified ID already exists.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided engagement rule was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "New engagement rule was created for the customer.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EngagementRule"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/resource/staff": {"patch": {"tags": ["Resource"], "summary": "Patches all matching staff resources referenced within engagements.", "operationId": "patchStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Staff"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided resource was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON> was successful.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Staff"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/resource/practice": {"patch": {"tags": ["Resource"], "summary": "Patches all matching practice resources referenced within engagements.", "operationId": "patchPractice", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Practice"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided resource was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON> was successful.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Practice"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/resource/patient": {"patch": {"tags": ["Resource"], "summary": "Patches all matching patient resources referenced within engagements.", "operationId": "patchPatient", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Patient"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided resource was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON> was successful.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Patient"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/resource/location": {"patch": {"tags": ["Resource"], "summary": "Patches all matching location resources referenced within engagements.", "operationId": "patchLocation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Location"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided resource was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON> was successful.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Location"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/resource/contact": {"patch": {"tags": ["Resource"], "summary": "Patches all matching contact resources referenced within engagements.", "operationId": "patchContact", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "The provided resource was poorly formed or failed validation.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "<PERSON> was successful.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contact"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/customer": {"get": {"tags": ["Customer"], "summary": "Gets the current user's customer record.", "operationId": "getCustomer", "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The customer record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SelfManagedCustomer"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "patch": {"tags": ["Customer"], "operationId": "patchCustomer", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/JsonPatch"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SelfManagedCustomer"}}}}}}}, "/api/v2/admin/customers/{customerId}": {"get": {"tags": ["Customer Admin"], "summary": "Gets a customer by ID.", "operationId": "getCustomerById", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The customer record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCustomer"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}, "patch": {"tags": ["Customer Admin"], "operationId": "patchCustomerById", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/JsonPatch"}}}, "required": true}, "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminCustomer"}}}}}}}, "/api/v2/templates": {"get": {"tags": ["Template"], "summary": "Gets a paginated list of templates.", "operationId": "getTemplates", "parameters": [{"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A page of templates.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageTemplate"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/templates/{id}": {"get": {"tags": ["Template"], "summary": "Gets a template by ID.", "operationId": "getById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "<PERSON><PERSON><PERSON> not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "The template record.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/templates/preview/{id}": {"get": {"tags": ["Template"], "summary": "Shows a preview of what the template generates", "operationId": "getPreview", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A preview of the template.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/MessageSegments"}}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/responses": {"get": {"tags": ["Response"], "summary": "Gets a paginated list of responses from contacts.", "operationId": "getResponses", "parameters": [{"name": "since", "in": "query", "description": "Only return responses that have occurred after this date/time.", "required": false, "schema": {"type": "string", "description": "Only return responses that have occurred after this date/time."}}, {"name": "messageId", "in": "query", "description": "Only return responses that are related to a specific message.", "required": false, "schema": {"type": "string", "description": "Only return responses that are related to a specific message."}}, {"name": "engagementId", "in": "query", "description": "Only return responses that are related to a specific engagement.", "required": false, "schema": {"type": "string", "description": "Only return responses that are related to a specific engagement."}}, {"name": "archived", "in": "query", "description": "Whether or not to include archived responses.", "required": false, "schema": {"type": "string", "description": "Whether or not to include archived responses.", "default": "false"}}, {"name": "page", "in": "query", "description": "Zero-based page index (0..N)", "schema": {"type": "integer", "default": 0}}, {"name": "size", "in": "query", "description": "The size of the page to be returned", "schema": {"type": "integer", "default": 20}}, {"name": "sort", "in": "query", "description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Customer not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A page of responses.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}, "/api/v2/admin/templates/{id}/preview": {"get": {"tags": ["Template Admin"], "operationId": "preview", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/MessageSegments"}}}}}}}}}, "/api/v2/admin/stats/delivery": {"get": {"tags": ["Statistics Admin"], "summary": "Gets a paginated list of delivery statistics, broken down by application and message type.", "operationId": "getDeliveryStatistics", "parameters": [{"name": "from", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "until", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["QUEUED", "FAILED", "DISPATCHED", "SENT", "DELIVERED"]}}}, {"name": "types", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["SMS", "VOICE", "EMAIL", "EMAIL_BROADCAST"]}}}], "responses": {"409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "A page of statistics.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryStatisticResult"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {}}}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"path": {"type": "string", "description": "Path where the error occurred."}, "message": {"type": "string", "description": "Describes the error."}, "errorCode": {"type": "integer", "description": "The error code.", "format": "int32"}, "details": {"type": "string", "description": "Additional details relating to the error."}}, "description": "Describes an error that occurred."}, "ErrorResponse": {"type": "object", "properties": {"timestamp": {"type": "string", "description": "A timestamp of when the error occurred.", "format": "date-time"}, "status": {"type": "integer", "description": "The HTTP status code describing the response.", "format": "int32"}, "message": {"type": "string", "description": "Description of the error."}, "path": {"type": "string", "description": "Path where the error occurred."}, "errors": {"type": "array", "description": "Additional error details", "items": {"$ref": "#/components/schemas/Error"}}}, "description": "Describes an error that occurred while processing a request."}, "Appointment": {"type": "object", "properties": {"id": {"type": "string", "description": "The appointment's ID in the customer system."}, "appointmentTypeId": {"type": "string", "description": "The appointment type ID in the customer system."}, "startTime": {"type": "string", "description": "The scheduled start date/time of the appointment represented as an instant. Must be provided if localStartTime is null.", "format": "date-time"}, "localStartTime": {"pattern": "\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d", "type": "string", "description": "The scheduled start date/time of the appointment represented as a date-time local to the appointment location. Must be provided if startTime is null.", "example": "2023-07-13T08:30"}, "patient": {"$ref": "#/components/schemas/Patient"}, "practice": {"$ref": "#/components/schemas/Practice"}, "location": {"$ref": "#/components/schemas/Location"}, "staff": {"$ref": "#/components/schemas/Staff"}, "cancelled": {"type": "boolean", "description": "Whether the appointment has been cancelled."}, "reason": {"type": "string", "description": "The reason for the appointment."}, "confirmationStatus": {"$ref": "#/components/schemas/ConfirmationStatus"}, "checkInStatus": {"$ref": "#/components/schemas/CheckInStatus"}}, "description": "A scheduled appointment for a patient."}, "CheckInStatus": {"type": "string", "description": "The check-in status of the appointment", "enum": ["NOT_CHECKED_IN", "CHECKED_IN", "NO_RESPONSE"]}, "ConfirmationStatus": {"type": "string", "description": "The confirmation status of the appointment.", "enum": ["NOT_APPLICABLE", "UNCONFIRMED", "CONFIRMED", "DECLINED", "NO_RESPONSE"]}, "Contact": {"type": "object", "properties": {"id": {"type": "string", "description": "The contact's ID in the customer system."}, "givenName": {"type": "string", "description": "The contact's given name."}, "familyName": {"type": "string", "description": "The contact's family name"}, "contactMethod": {"$ref": "#/components/schemas/ContactMethod"}, "language": {"$ref": "#/components/schemas/Language"}, "email": {"type": "string", "description": "The contact's email address."}, "phone": {"type": "string", "description": "The contact's phone number."}}, "description": "A patient's contact."}, "ContactMethod": {"type": "string", "description": "The contact's specified method for receiving communications.", "enum": ["NONE", "SMS", "VOICE", "EMAIL"]}, "Language": {"type": "string", "description": "The contacts preferred language for receiving communications.", "enum": ["ENGLISH", "SPANISH"]}, "Location": {"type": "object", "properties": {"id": {"type": "string", "description": "The location's ID in the customer system."}, "practiceId": {"type": "string", "description": "The location's practice ID in the customer system."}, "name": {"type": "string", "description": "The location's name in the customer system."}, "zipCode": {"type": "string", "description": "The location's zip code."}, "address": {"type": "string", "description": "The location's business address."}, "phone": {"type": "string", "description": "The location's business phone number."}, "email": {"type": "string", "description": "The location's email address."}}, "description": "A practice location."}, "Patient": {"type": "object", "properties": {"id": {"type": "string", "description": "The patient's ID in the customer system."}, "givenName": {"type": "string", "description": "The patient's given name."}, "familyName": {"type": "string", "description": "The patient's family name."}}, "description": "A patient."}, "Practice": {"type": "object", "properties": {"id": {"type": "string", "description": "The practice's ID in the customer system."}, "name": {"type": "string"}}, "description": "A practice."}, "Staff": {"type": "object", "properties": {"id": {"type": "string", "description": "The staff member's ID in the customer system."}, "name": {"type": "string"}}, "description": "A practice staff member."}, "UpdateEngagementRequest": {"type": "object", "properties": {"appointments": {"type": "array", "description": "A list of appointments associated with the engagement.", "items": {"$ref": "#/components/schemas/Appointment"}}, "contacts": {"type": "array", "description": "A list of patient contacts that will be participants in the engagement.", "items": {"$ref": "#/components/schemas/Contact"}}}, "description": "Describes a new engagement record that will be created."}, "Activity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "occurredAt": {"type": "string", "format": "date-time"}, "code": {"type": "string", "enum": ["INFORMATIONAL"]}, "description": {"type": "string"}}}, "Engagement": {"type": "object", "properties": {"id": {"type": "string"}, "customerId": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "state": {"$ref": "#/components/schemas/EngagementStatus"}, "confirmationStatus": {"$ref": "#/components/schemas/ConfirmationStatus"}, "confirmationAttempts": {"type": "integer", "format": "int32"}, "checkInStatus": {"$ref": "#/components/schemas/CheckInStatus"}, "checkInAttempts": {"type": "integer", "format": "int32"}, "reminderAttempts": {"type": "integer", "format": "int32"}, "eventDate": {"type": "string", "format": "date-time"}, "nextCheckpoint": {"type": "string", "format": "date-time"}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/Appointment"}}, "contacts": {"type": "array", "items": {"$ref": "#/components/schemas/Contact"}}, "activity": {"type": "array", "items": {"$ref": "#/components/schemas/Activity"}}}}, "EngagementStatus": {"type": "string", "enum": ["INITIAL", "CONFIRM", "CONFIRMED", "CHECK_IN", "DECLINED", "REMIND", "CANCEL", "CANCELLED", "AWAIT_COMPLETED", "COMPLETED", "ERROR", "BOOK", "APPOINTMENT_SURVEY"]}, "EngagementRule": {"type": "object", "properties": {"id": {"type": "string", "description": "The vendor's ID for the rule."}, "enabled": {"type": "boolean", "description": "Whether or not the rule should be enabled."}, "workflow": {"$ref": "#/components/schemas/EngagementWorkflow"}, "allAppointmentTypes": {"type": "boolean", "description": "Whether the rule should match any appointment type."}, "allStaff": {"type": "boolean", "description": "Whether the rule should match any staff member."}, "allPracticeLocations": {"type": "boolean", "description": "Whether the rule should match any location in all practices."}, "startDate": {"type": "string", "description": "The starting date and time (in UTC) when the rule should take effect.", "format": "date-time"}, "endDate": {"type": "string", "description": "The ending date and time (in UTC) when the rule should no longer be in effect.", "format": "date-time"}, "templateOverrides": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Segments"}, "description": "Overridden templates that can be used for EMAIL or VOICE messages."}, "description": "Overridden templates that can be used for EMAIL or VOICE messages."}, "description": "Overridden templates that can be used for EMAIL or VOICE messages."}, "templateIds": {"type": "object", "additionalProperties": {"type": "string", "description": "A map of scenarios to the desired standard template ID.", "deprecated": true}, "description": "A map of scenarios to the desired standard template ID.", "deprecated": true}, "appointmentTypes": {"uniqueItems": true, "type": "array", "description": "If allAppointmentTypes is false, a set of vendor appointment type IDs that the rule applies to.", "items": {"type": "string", "description": "If allAppointmentTypes is false, a set of vendor appointment type IDs that the rule applies to."}}, "practiceLocations": {"uniqueItems": true, "type": "array", "description": "If allPracticeLocations is false, a set of practices and locations the rule applies to.", "items": {"$ref": "#/components/schemas/PracticeLocationRule"}}, "staff": {"uniqueItems": true, "type": "array", "description": "If allStaff is false, a set of staff IDs the rule applies to.", "items": {"type": "string", "description": "If allStaff is false, a set of staff IDs the rule applies to."}}, "schedule": {"$ref": "#/components/schemas/Schedule"}, "templateId": {"type": "string", "description": "The template ID associated with the rule"}}, "description": "A rule that determines which workflows and which templates should be used when engaging a contact for an appointment that matches a set of criteria."}, "EngagementWorkflow": {"type": "string", "description": "Determines which workflow should be activated if an appointment matches this rule.", "enum": ["CONFIRMATION", "REMINDER", "CANCELLATION", "CHECKIN", "BOOKING", "APPOINTMENT_SURVEY", "APPT_SURVEY_EXTERNAL", "APPT_SURVEY_INTERNAL"]}, "PracticeLocationRule": {"type": "object", "properties": {"practiceId": {"type": "string", "description": "The vendor's ID for the practice."}, "allLocations": {"type": "boolean", "description": "Whether the rule should apply to all locations in the practice."}, "locations": {"uniqueItems": true, "type": "array", "description": "If allLocations is false, the locations in the practice the rule applies to.", "items": {"type": "string", "description": "If allLocations is false, the locations in the practice the rule applies to."}}}, "description": "Describes how to match a practice and its locations to a rule."}, "Schedule": {"type": "object", "properties": {"unit": {"type": "string", "enum": ["<PERSON><PERSON>", "Micros", "<PERSON><PERSON>", "Seconds", "Minutes", "Hours", "HalfDays", "Days", "Weeks", "Months", "Years", "Decades", "Centuries", "Millennia", "Eras", "Forever"]}, "attempts": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}, "totalAttempts": {"type": "integer", "format": "int32"}}, "description": "The schedule at which this rule will send and check for responses."}, "Segments": {"type": "object", "properties": {"main": {"type": "string"}, "subject": {"type": "string"}, "instructions": {"type": "string"}}, "description": "Overridden templates that can be used for EMAIL or VOICE messages."}, "CreateTemplateRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "A name that describes the template."}, "workflow": {"$ref": "#/components/schemas/EngagementWorkflow"}, "variations": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Segments"}, "description": "Variations of the template for different communication channels and languages.", "deprecated": true}, "description": "Variations of the template for different communication channels and languages.", "deprecated": true}, "scenarios": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Segments"}, "description": "Variations of the template for different scenarios, communication channels, and languages."}, "description": "Variations of the template for different scenarios, communication channels, and languages."}, "description": "Variations of the template for different scenarios, communication channels, and languages."}}, "description": "Parameters used to create or edit a Template."}, "Template": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "workflow": {"$ref": "#/components/schemas/EngagementWorkflow"}, "variations": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Segments"}}}, "scenarios": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Segments"}}}}}, "description": "Contains variations of a message template."}, "CreateEngagementRequest": {"type": "object", "properties": {"appointments": {"type": "array", "description": "A list of appointments associated with the engagement.", "items": {"$ref": "#/components/schemas/Appointment"}}, "contacts": {"type": "array", "description": "A list of patient contacts that will be participants in the engagement.", "items": {"$ref": "#/components/schemas/Contact"}}}, "description": "Describes a new engagement record that will be created."}, "EngagementEventDto": {"type": "object", "properties": {"appointmentId": {"type": "string", "description": "Op appointment id which is related to the requested engagement"}, "engagementEventType": {"type": "string", "description": "Event type that is happening in the engagement", "enum": ["CHECKPOINT", "CANCELLED", "DECLINED", "CONFIRMED", "REMINDERS_SENT", "CANCELLATION_SENT", "START_CONFIRMATION", "START_CHECKIN", "START_REMINDER", "ERROR", "START_BOOKING", "BOOKING_SENT", "WORKFLOW_COMPLETE", "CHECKED_IN", "CHECKIN_DONE", "START_APPOINTMENT_SURVEY", "APPOINTMENT_SURVEY_SENT"]}}}, "ResponseString": {"type": "object", "properties": {"success": {"type": "boolean"}, "status": {"type": "string", "enum": ["100 CONTINUE", "101 SWITCHING_PROTOCOLS", "102 PROCESSING", "103 CHECKPOINT", "200 OK", "201 CREATED", "202 ACCEPTED", "203 NON_AUTHORITATIVE_INFORMATION", "204 NO_CONTENT", "205 RESET_CONTENT", "206 PARTIAL_CONTENT", "207 MULTI_STATUS", "208 ALREADY_REPORTED", "226 IM_USED", "300 MULTIPLE_CHOICES", "301 MOVED_PERMANENTLY", "302 FOUND", "302 MOVED_TEMPORARILY", "303 SEE_OTHER", "304 NOT_MODIFIED", "305 USE_PROXY", "307 TEMPORARY_REDIRECT", "308 PERMANENT_REDIRECT", "400 BAD_REQUEST", "401 UNAUTHORIZED", "402 PAYMENT_REQUIRED", "403 FORBIDDEN", "404 NOT_FOUND", "405 METHOD_NOT_ALLOWED", "406 NOT_ACCEPTABLE", "407 PROXY_AUTHENTICATION_REQUIRED", "408 REQUEST_TIMEOUT", "409 CONFLICT", "410 GONE", "411 LENGTH_REQUIRED", "412 PRECONDITION_FAILED", "413 PAYLOAD_TOO_LARGE", "413 REQUEST_ENTITY_TOO_LARGE", "414 URI_TOO_LONG", "414 REQUEST_URI_TOO_LONG", "415 UNSUPPORTED_MEDIA_TYPE", "416 REQUESTED_RANGE_NOT_SATISFIABLE", "417 EXPECTATION_FAILED", "418 I_AM_A_TEAPOT", "419 INSUFFICIENT_SPACE_ON_RESOURCE", "420 METHOD_FAILURE", "421 DESTINATION_LOCKED", "422 UNPROCESSABLE_ENTITY", "423 LOCKED", "424 FAILED_DEPENDENCY", "425 TOO_EARLY", "426 UPGRADE_REQUIRED", "428 PRECONDITION_REQUIRED", "429 TOO_MANY_REQUESTS", "431 REQUEST_HEADER_FIELDS_TOO_LARGE", "451 UNAVAILABLE_FOR_LEGAL_REASONS", "500 INTERNAL_SERVER_ERROR", "501 NOT_IMPLEMENTED", "502 BAD_GATEWAY", "503 SERVICE_UNAVAILABLE", "504 GATEWAY_TIMEOUT", "505 HTTP_VERSION_NOT_SUPPORTED", "506 VARIANT_ALSO_NEGOTIATES", "507 INSUFFICIENT_STORAGE", "508 LOOP_DETECTED", "509 BANDWIDTH_LIMIT_EXCEEDED", "510 NOT_EXTENDED", "511 NETWORK_AUTHENTICATION_REQUIRED"]}, "result": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}}}, "CreateCustomerRequest": {"type": "object", "properties": {"opmedId": {"type": "string", "description": "The customer's OPMED ID."}, "name": {"type": "string", "description": "The customer's name."}, "legacyUsername": {"type": "string", "description": "The customer's legacy PMX username."}, "legacyPassword": {"type": "string", "description": "The customer's legacy PMX password."}}, "description": "Describes a new PMX customer record that will be created."}, "AdminCustomer": {"type": "object", "properties": {"id": {"type": "string", "description": "The vendor's customer ID. Must be unique across the entire system."}, "name": {"type": "string", "description": "Business name for the customer (not to be confused with the name of a practice or location within the customer's hierarchy)"}, "status": {"$ref": "#/components/schemas/CustomerStatus"}, "legacyUsername": {"type": "string", "description": "The username used to authenticate to the legacy PMX APIs."}, "createdAt": {"type": "string", "description": "When the rule was created.", "format": "date-time", "readOnly": true}, "updatedAt": {"type": "string", "description": "When the customer was last updated.", "format": "date-time", "readOnly": true}, "deliveryDays": {"uniqueItems": true, "type": "array", "description": "The days when messages can be sent. Messages will be sent on the next allowed delivery day.", "items": {"$ref": "#/components/schemas/DeliveryDay"}}, "deliveryStartTime": {"pattern": "\\d{2}:\\d{2}:\\{d}{2}", "type": "string", "description": "The beginning of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.", "format": "time", "example": "09:00:00"}, "deliveryEndTime": {"pattern": "\\d{2}:\\d{2}:\\{d}{2}", "type": "string", "description": "The ending of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.", "format": "time", "example": "17:00:00"}, "cancellationDeadline": {"type": "number", "description": "The minimum timeframe (in seconds) before an appointment start when the contact can cancel an appointment.", "format": "float", "example": 43200.0}, "appointmentTimeDisplayOffset": {"type": "number", "description": "The period of time (in seconds) prior to scheduled appointment time during which patient is asked to arrive.", "format": "float", "example": 900.0}, "telnyxConfig": {"$ref": "#/components/schemas/TelnyxConfig"}}}, "CustomerStatus": {"type": "string", "description": "Describes the status of a customer account.", "enum": ["PROVISIONING", "ENABLED", "DISABLED"]}, "DeliveryDay": {"type": "string", "description": "The days when messages can be sent. Messages will be sent on the next allowed delivery day.", "enum": ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]}, "PhoneNumber": {"type": "object", "properties": {"id": {"type": "string"}, "phoneNumber": {"type": "string"}, "orderId": {"type": "string"}, "orderStatus": {"$ref": "#/components/schemas/PhoneNumberOrderStatus"}, "defaultVoice": {"type": "boolean"}, "defaultMessaging": {"type": "boolean"}}, "description": "The phone numbers that have been ordered for the customer which will be used for legacy SMS and voice workflows."}, "PhoneNumberOrderStatus": {"type": "string", "description": "Describes the order status of a phone number.", "enum": ["PENDING", "SUCCESS", "FAILURE"]}, "TelnyxConfig": {"type": "object", "properties": {"billingGroupId": {"type": "string", "description": "The ID of the Telnyx Billing Group that was created for the customer."}, "outboundVoiceProfileId": {"type": "string", "description": "The ID of the Telnyx Outbound Voice Profile that was created for the customer."}, "messagingProfileId": {"type": "string", "description": "The ID of the Telnyx Messaging Profile that was created for the customer."}, "callControlConnectionId": {"type": "string", "description": "The ID of the Telnyx Call Control Connection that was created for the customer."}, "phoneNumbers": {"uniqueItems": true, "type": "array", "description": "The phone numbers that have been ordered for the customer which will be used for legacy SMS and voice workflows.", "items": {"$ref": "#/components/schemas/PhoneNumber"}}}, "description": "The customer's Telnyx configuration."}, "JsonPatch": {"type": "object", "description": "An RFC 6902 JSON Patch payload describing the changes to the customer that you wish to make."}, "SelfManagedCustomer": {"type": "object", "properties": {"id": {"type": "string", "description": "The vendor's customer ID. Must be unique across the entire system."}, "name": {"type": "string", "description": "Business name for the customer (not to be confused with the name of a practice or location within the customer's hierarchy)"}, "status": {"$ref": "#/components/schemas/CustomerStatus"}, "legacyUsername": {"type": "string", "description": "The username used to authenticate to the legacy PMX APIs."}, "createdAt": {"type": "string", "description": "When the rule was created.", "format": "date-time", "readOnly": true}, "updatedAt": {"type": "string", "description": "When the customer was last updated.", "format": "date-time", "readOnly": true}, "deliveryDays": {"uniqueItems": true, "type": "array", "description": "The days when messages can be sent. Messages will be sent on the next allowed delivery day.", "items": {"$ref": "#/components/schemas/DeliveryDay"}}, "deliveryStartTime": {"pattern": "\\d{2}:\\d{2}:\\{d}{2}", "type": "string", "description": "The beginning of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.", "format": "time", "example": "09:00:00"}, "deliveryEndTime": {"pattern": "\\d{2}:\\d{2}:\\{d}{2}", "type": "string", "description": "The ending of the window when messages can be sent. Used to prevent messages such as SMS and voice from contacting patients at inappropriate times of the day.", "format": "time", "example": "17:00:00"}, "cancellationDeadline": {"type": "number", "description": "The minimum timeframe (in seconds) before an appointment start when the contact can cancel an appointment.", "format": "float", "example": 43200.0}, "appointmentTimeDisplayOffset": {"type": "number", "description": "The period of time (in seconds) prior to scheduled appointment time during which patient is asked to arrive.", "format": "float", "example": 900.0}}, "description": "The customer's view of their own customer record."}, "Pageable": {"type": "object", "properties": {"page": {"minimum": 0, "type": "integer", "format": "int32"}, "size": {"minimum": 1, "type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "PageTemplate": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"offset": {"type": "integer", "format": "int64"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "unpaged": {"type": "boolean"}, "paged": {"type": "boolean"}}}, "SortObject": {"type": "object", "properties": {"empty": {"type": "boolean"}, "sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}}}, "MessageSegments": {"type": "object", "properties": {"main": {"type": "string"}, "altMain": {"type": "string"}, "subject": {"type": "string"}, "instructions": {"type": "string"}}}, "AppointmentResource": {"type": "object", "properties": {"id": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "reason": {"type": "string"}, "location": {"type": "string"}, "staff": {"type": "string"}, "patient": {"type": "string"}, "appointmentType": {"type": "string"}, "practice": {"type": "string"}, "cancelled": {"type": "boolean"}, "checkInStatus": {"$ref": "#/components/schemas/CheckInStatus"}, "type": {"$ref": "#/components/schemas/ResourceType"}}}, "CancellationResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "cancellationStatus": {"$ref": "#/components/schemas/CancellationStatus"}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response from the system regarding an appointment confirmation."}, "CancellationStatus": {"type": "string", "description": "The cancellation status of an appointment", "enum": ["NOT_CANCELLED", "CANCELLED"]}, "CheckInResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "checkInStatus": {"$ref": "#/components/schemas/CheckInStatus"}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response from one or more contacts regarding check-in for one or more appointments."}, "CompleteResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response that engagement for the following appointments has completed successfully."}, "ConfirmationResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "confirmationStatus": {"$ref": "#/components/schemas/ConfirmationStatus"}, "messageId": {"type": "string"}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response from one or more contacts regarding confirmation of one or more appointments."}, "ContactResource": {"type": "object", "properties": {"id": {"type": "string"}, "familyName": {"type": "string"}, "givenName": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "contactMethod": {"$ref": "#/components/schemas/ContactMethod"}, "language": {"$ref": "#/components/schemas/Language"}, "type": {"$ref": "#/components/schemas/ResourceType"}}}, "ErrorResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response from the system that an error occurred."}, "MessageResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "workflow": {"$ref": "#/components/schemas/EngagementWorkflow"}, "status": {"type": "string", "enum": ["QUEUED", "FAILED", "DISPATCHED", "SENT", "DELIVERED"]}, "messageId": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/Error"}}, "message": {"type": "string"}, "altMessage": {"type": "string"}, "subject": {"type": "string"}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "isFinal": {"type": "boolean"}}, "description": "A response regarding the successful or failed delivery of a message."}, "PageResponse": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Response"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "numberOfElements": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "empty": {"type": "boolean"}}}, "ResourceType": {"type": "string", "enum": ["LOCATION", "STAFF", "CONTACT", "PATIENT", "APPOINTMENT", "PRACTICE"]}, "Response": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["CONFIRMATION", "CHECK_IN", "CANCELLATION", "ERROR", "COMPLETE", "MESSAGE"]}, "respondents": {"type": "array", "items": {"$ref": "#/components/schemas/ContactResource"}}, "appointments": {"type": "array", "items": {"$ref": "#/components/schemas/AppointmentResource"}}, "occurredAt": {"type": "string", "format": "date-time"}, "customerId": {"type": "string"}, "engagementId": {"type": "string"}, "isFinal": {"type": "boolean"}}, "description": "A response from one or more contacts to a particular workflow in an engagement.", "oneOf": [{"$ref": "#/components/schemas/ErrorResponseDto"}, {"$ref": "#/components/schemas/CancellationResponseDto"}, {"$ref": "#/components/schemas/ConfirmationResponseDto"}, {"$ref": "#/components/schemas/CheckInResponseDto"}, {"$ref": "#/components/schemas/CompleteResponseDto"}, {"$ref": "#/components/schemas/MessageResponseDto"}]}, "DeliveryStatisticResult": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "until": {"type": "string", "format": "date-time"}, "application": {"type": "string", "enum": ["ALL", "LEGACY", "ENGAGEMENT"]}, "type": {"type": "string", "enum": ["SMS", "VOICE", "EMAIL", "EMAIL_BROADCAST"]}, "total": {"type": "integer", "format": "int64"}, "delivered": {"type": "integer", "format": "int64"}, "failed": {"type": "integer", "format": "int64"}, "other": {"type": "integer", "format": "int64"}, "deliveryRate": {"type": "number", "format": "double"}, "failureRate": {"type": "number", "format": "double"}, "otherRate": {"type": "number", "format": "double"}, "details": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}