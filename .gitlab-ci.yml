variables:
  MAVEN_OPTS: -Dmaven.repo.local=/root/.m2
  REGION: us-east-1
  ENV_MONGO_URI: mongodb://mongo:27017/pmx

stages:
  - dependencies
  - build
  - test
  - upload
  - deploy

dependencies:
  stage: dependencies
  image: maven:3.6.3-openjdk-17-slim
  script:
    - >
      mvn dependency:go-offline -B -Dmaven.wagon.http.ssl.insecure=true

test:
  stage: test
  image: maven:3.6.3-openjdk-17-slim
  services:
    - mongo:latest
  script:
    - >
      mvn clean install sonar:sonar -B

upload-dev:
  stage: upload
  image: maven:3.6.3-openjdk-17-slim
  services:
    - mongo:latest
  variables:
    DOCKER_TAG: '$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA'
  script:
    - >
      mvn deploy -B -DskipTests -Dmaven.wagon.http.ssl.insecure=true && cd pmx-server && mvn -P dev-staging jib:build -Dmaven.wagon.http.ssl.insecure=true
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: on_success
    - if: $CI_COMMIT_BRANCH =~ /^(release|hotfix|feature|bugfix)\/.*$/
      allow_failure: true
      when: manual
  allow_failure:
    exit_codes: 42

upload-preprod:
  stage: upload
  image: maven:3.6.3-openjdk-17-slim
  services:
    - mongo:latest
  variables:
    DOCKER_TAG: 'preprod-$CI_COMMIT_SHORT_SHA'
  script:
    - >
      mvn deploy -B -DskipTests -Dmaven.wagon.http.ssl.insecure=true && cd pmx-server && mvn -P dev-staging jib:build -Dmaven.wagon.http.ssl.insecure=true
  only:
    - /^pre-prod.*$/
    - /^preprod.*$/
    - /^release\/.*$/

upload-prod:
  stage: upload
  image: maven:3.6.3-openjdk-17-slim
  services:
    - mongo:latest
  variables:
    DOCKER_TAG: '$CI_COMMIT_TAG'
  script:
    - >
      mvn deploy -B -DskipTests -Dmaven.wagon.http.ssl.insecure=true && cd pmx-server && mvn -P production jib:build -Dmaven.wagon.http.ssl.insecure=true
  only:
    - tags

deploy-dev:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  environment:
    name: dev
  variables:
    DOCKER_TAG: '$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA'
  script:
    - >
      sed -i "s/\__DOCKER_TAG__/${DOCKER_TAG}/g" ci/aws/params-dev.json

      aws --region us-east-1 cloudformation deploy
      --role-arn $AWS_DEPLOY_ROLE_ARN
      --stack-name Atlas-PMX-Dev
      --template-file ./ci/aws/cloudformation-template.yml
      --parameter-overrides file://ci/aws/params-dev.json
      --s3-bucket cxn-cd-cf-templates
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: on_success
    - if: $CI_COMMIT_BRANCH =~ /^(release|hotfix|feature|bugfix)\/.*$/
      allow_failure: true
      when: manual
  allow_failure:
    exit_codes: 42

deploy-preprod:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  environment:
    name: dev
  variables:
    DOCKER_TAG: 'preprod-$CI_COMMIT_SHORT_SHA'
  script:
    - >
      sed -i "s/\__DOCKER_TAG__/${DOCKER_TAG}/g" ci/aws/params-preprod.json

      aws --region us-east-1 cloudformation deploy
      --role-arn $AWS_DEPLOY_ROLE_ARN
      --stack-name Atlas-PMX-PreProd
      --template-file ./ci/aws/cloudformation-template.yml
      --parameter-overrides file://ci/aws/params-preprod.json
      --s3-bucket cxn-cd-cf-templates
  only:
    - /^pre-prod.*$/
    - /^preprod.*$/
    - /^release\/.*$/

deploy-prod:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  environment:
    name: prod
  variables:
    DOCKER_TAG: '$CI_COMMIT_TAG'
  script:
    - >
      sed -i "s/\__DOCKER_TAG__/${DOCKER_TAG}/g" ci/aws/params-prod.json

      aws --region us-east-1 cloudformation deploy
      --role-arn $AWS_DEPLOY_ROLE_ARN
      --stack-name Atlas-PMX
      --template-file ./ci/aws/cloudformation-template.yml
      --parameter-overrides file://ci/aws/params-prod.json
      --s3-bucket cxn-cd-cf-templates-prod
  only:
    - tags
  when: manual
