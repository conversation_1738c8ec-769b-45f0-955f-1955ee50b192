<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.connexin</groupId>
    <artifactId>pmx</artifactId>
    <version>1.18.0-SNAPSHOT</version>
    <name>pmx</name>
    <packaging>pom</packaging>

    <modules>
        <module>pmx-server</module>
        <module>pmx-sdk</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <kotlin.version>1.7.10</kotlin.version>

        <sonar.host.url>http://**********:9000</sonar.host.url>
        <sonar.language>kotlin</sonar.language>
        <sonar.exclusions>
            **/ApplicationContext.java
            **/pom.xml
        </sonar.exclusions>
        <sonar.coverage.exclusions>
            **/ApplicationContext.java
        </sonar.coverage.exclusions>
        <sonar.cpd.exclusions>
            **/ApplicationContext.java
        </sonar.cpd.exclusions>
        <sonar.junit.reportPaths>
            target/surefire-reports
        </sonar.junit.reportPaths>
        <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <sonar.sources>src/main/kotlin</sonar.sources>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.modules>pmx-server</sonar.modules>
    </properties>

    <distributionManagement>
        <repository>
            <id>connexin-release</id>
            <name>Connexin Releases</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-release/</url>
        </repository>
        <snapshotRepository>
            <id>connexin-snapshot</id>
            <name>Connexin Snapshots</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>connexin-snapshot</id>
            <name>Connexin Snapshots</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>connexin-release</id>
            <name>Connexin Releases</name>
            <url>https://repo1.connexin.local/content/repositories/connexin-release</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
