version: '3.2'

services:
  mongodb:
    image: mongo:4
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: test
    networks:
      - pmx
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - pmx

networks:
  pmx:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ***********/24